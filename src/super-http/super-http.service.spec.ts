import { Test, TestingModule } from '@nestjs/testing';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { HttpService } from '@nestjs/axios';
import { ClsService } from 'nestjs-cls';
import { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { Cache, Store } from 'cache-manager';
import { of } from 'rxjs';
import Bottleneck from '@maselious/bottleneck';
import { SuperHttpService } from './super-http.service';
import { OperationType } from './enums';
import { SuperHttpModuleConfig } from './interfaces';

// Mock Bottleneck
jest.mock('@maselious/bottleneck');

describe('SuperHttpService', () => {
  let service: SuperHttpService;
  let httpService: jest.Mocked<HttpService>;
  let cacheManager: any;
  let clsService: ClsService;
  let config: SuperHttpModuleConfig;
  let mockBottleneckInstance: any;

  const mockTenantId = 'tenant-123';
  const mockEntityType = 'user';
  const mockOperation = OperationType.GET;
  const mockIntegrationName = 'test-integration';
  const mockUrl = 'https://api.example.com/users';
  const mockResponseData = { id: 1, name: 'Test User' };
  
  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Setup config first to avoid undefined errors
    config = {
      rateLimiters: {
        default: {
          minTime: 100,
          maxConcurrent: 10,
          reservoir: 100,
          reservoirRefreshAmount: 100,
          reservoirRefreshInterval: 60000,
        },
        fast: {
          minTime: 50,
          maxConcurrent: 20,
          reservoir: 200,
          reservoirRefreshAmount: 200,
          reservoirRefreshInterval: 60000,
        },
      },
      defaultRateLimiter: 'default',
      timeout: 30000,
      redis: null,
    };
    
    // Setup mock HttpService with proper methods
    httpService = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
      axiosRef: {
        interceptors: {
          request: {
            use: jest.fn(),
            eject: jest.fn(),
            clear: jest.fn(),
          },
          response: {
            use: jest.fn(),
            eject: jest.fn(),
            clear: jest.fn(),
          },
        },
        request: jest.fn(),
      },
    } as any;

    // Setup cache manager mock with a working store implementation
    cacheManager = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      reset: jest.fn(),
      keys: jest.fn().mockResolvedValue([]),
      ttl: jest.fn(),
      mget: jest.fn(),
      mset: jest.fn(),
      mdel: jest.fn(),
      store: {
        keys: jest.fn().mockResolvedValue(['key1', 'key2']),
      },
    };

    clsService = {
      get: jest.fn(),
      set: jest.fn(),
      has: jest.fn()
    } as any;

    // Setup a mock bottleneck instance that can be returned
    mockBottleneckInstance = {
      schedule: jest.fn().mockImplementation((fn) => fn()),
      on: jest.fn(),
      disconnect: jest.fn(),
      ready: jest.fn().mockResolvedValue(true),
      running: jest.fn().mockResolvedValue(5),
      queued: jest.fn().mockResolvedValue(10),
      clusterQueued: jest.fn().mockResolvedValue(10),
      currentReservoir: jest.fn().mockResolvedValue(100),
      counts: jest.fn().mockResolvedValue({
        RECEIVED: 15,
        QUEUED: 10,
        RUNNING: 5,
        EXECUTING: 3,
        DONE: 2
      }),
    };
    
    // Simplify the mock to avoid constructor typings issue
    (Bottleneck as unknown as jest.Mock).mockImplementation(() => mockBottleneckInstance);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SuperHttpService,
        { provide: ClsService, useValue: clsService },
        { provide: HttpService, useValue: httpService },
        { provide: CACHE_MANAGER, useValue: cacheManager },
        { provide: 'HTTP_MODULE_CONFIG', useValue: config },
      ],
    }).compile();

    service = module.get<SuperHttpService>(SuperHttpService);
    
    // Mock onModuleInit to setup rate limiters
    await service.onModuleInit();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('initializeRateLimiters', () => {
    it('should create a default rate limiter when none are configured', async () => {
      // When
      await service.onModuleInit();

      // Then - Verify the default rate limiter is working by checking health
      const isHealthy = await service.checkLimiterHealth('default');
      expect(isHealthy).toBe(true);
    });
  });

  describe('get', () => {
    it('should make a GET request and return the response data', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
        method: 'get',
        url: mockUrl,
        headers: expect.objectContaining({
          'X-Tenant-ID': mockTenantId,
        }),
      }));
    });
    
    it('should use cached data when available', async () => {
      // Mock cache hit
      cacheManager.get.mockResolvedValueOnce(JSON.stringify(mockResponseData));
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.get).toHaveBeenCalled();
      expect(httpService.axiosRef.request).not.toHaveBeenCalled();
    });
    
    it('should bypass cache when forceFresh is true', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
          forceFresh: true,
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.get).not.toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
    
    it('should handle HTTP errors gracefully', async () => {
      // Mock HTTP error
      const mockError = new AxiosError('Not Found');
      mockError.response = {
        status: 404,
        statusText: 'Not Found',
        headers: {},
        config: {} as any,
        data: { error: 'Resource not found' },
      } as any;
      
      (httpService.axiosRef.request as jest.Mock).mockRejectedValueOnce(mockError);
      
      await expect(
        service.get(
          mockUrl,
          mockTenantId,
          {
            entityType: mockEntityType,
            operation: mockOperation,
            integrationName: mockIntegrationName,
            rateLimiter: 'default',
          },
        ),
      ).rejects.toThrow();
    });
    
    // it('should get tenantId from ClsService if not provided', async () => {
    //   // Mock ClsService
    //   (clsService.get as jest.Mock).mockReturnValueOnce({ id: mockTenantId });
      
    //   // Mock HTTP request implementation
    //   (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
    //     data: mockResponseData,
    //     status: 200,
    //   } as AxiosResponse);
      
    //   const result = await service.get(
    //     mockUrl,
    //     null,
    //     {
    //       entityType: mockEntityType,
    //       operation: mockOperation,
    //       integrationName: mockIntegrationName,
    //       rateLimiter: 'default',
    //     },
    //   );
      
    //   expect(result).toEqual(mockResponseData);
    //   expect(clsService.get).toHaveBeenCalled();
    //   expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
    //     headers: expect.objectContaining({
    //       'X-Tenant-ID': mockTenantId,
    //     }),
    //   }));
    // });

    it('should use the default rate limiter when none is specified', async () => {
      // Create a mock HTTP response with the correct structure
      const mockHttpResponse: AxiosResponse = {
        data: { id: 1, name: 'John Doe' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as InternalAxiosRequestConfig
      };

      // Mock the request method since that's what's used internally
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce(mockHttpResponse);

      // Call the service's get method with null for the rateLimiter
      await service.get('https://api.example.com/users', 'tenant-123', {
        rateLimiter: null,
        entityType: 'user',
        operation: OperationType.GET,
        integrationName: 'test-integration',
      });

      // Verify the schedule method was called on the default rate limiter
      expect(mockBottleneckInstance.schedule).toHaveBeenCalled();
    });

    it('should handle cache errors gracefully when reading from cache', async () => {
      // Mock cache error
      cacheManager.get.mockRejectedValueOnce(new Error('Cache error'));
      
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.get).toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
    
    it('should handle cache errors gracefully when setting cache', async () => {
      // Mock cache error
      cacheManager.set.mockRejectedValueOnce(new Error('Cache error'));
      
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.set).toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
  });

  describe('post', () => {
    it('should make a POST request and return the response data', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 201,
      } as AxiosResponse);
      
      const mockRequestData = { name: 'New User' };
      
      const result = await service.post(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.CREATE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
        method: 'post',
        url: mockUrl,
        data: mockRequestData,
        headers: expect.objectContaining({
          'X-Tenant-ID': mockTenantId,
        }),
      }));
    });
    
    it('should invalidate entity type cache when wideCacheInvalidation is true', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 201,
      } as AxiosResponse);
      
      // Mock cache keys function
      cacheManager.store.keys.mockResolvedValueOnce(['key1', 'key2']);
      
      const mockRequestData = { name: 'New User' };
      
      await service.post(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.CREATE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
          wideCacheInvalidation: true,
        },
      );
      
      expect(cacheManager.store.keys).toHaveBeenCalled();
      expect(cacheManager.del).toHaveBeenCalledTimes(2);
    });
    
    it('should invalidate specific cache when wideCacheInvalidation is false', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 201,
      } as AxiosResponse);
      
      const mockRequestData = { name: 'New User' };
      const mockEntityId = 'user-123';
      
      await service.post(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.CREATE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
          wideCacheInvalidation: false,
          entityId: mockEntityId,
        },
      );
      
      expect(cacheManager.keys).not.toHaveBeenCalled();
      expect(cacheManager.del).toHaveBeenCalledTimes(1);
    });
    
    it('should throw an error when wideCacheInvalidation is false and entityId is not provided', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 201,
      } as AxiosResponse);
      
      const mockRequestData = { name: 'New User' };
      
      await expect(
        service.post(
          mockUrl,
          mockTenantId,
          mockRequestData,
          {
            entityType: mockEntityType,
            operation: OperationType.CREATE,
            integrationName: mockIntegrationName,
            rateLimiter: 'default',
            wideCacheInvalidation: false,
          },
        ),
      ).rejects.toThrow('entityId is required when wideCacheInvalidation is false');
    });
  });

  describe('put', () => {
    it('should make a PUT request and return the response data', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const mockRequestData = { id: 1, name: 'Updated User' };
      
      const result = await service.put(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.UPDATE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
        method: 'put',
        url: mockUrl,
        data: mockRequestData,
        headers: expect.objectContaining({
          'X-Tenant-ID': mockTenantId,
        }),
      }));
    });
  });

  describe('patch', () => {
    it('should make a PATCH request and return the response data', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const mockRequestData = { name: 'Patched User' };
      
      const result = await service.patch(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.PATCH,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
        method: 'patch',
        url: mockUrl,
        data: mockRequestData,
        headers: expect.objectContaining({
          'X-Tenant-ID': mockTenantId,
        }),
      }));
    });
  });

  describe('delete', () => {
    it('should make a DELETE request and return the response data', async () => {
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.delete(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: OperationType.DELETE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(httpService.axiosRef.request).toHaveBeenCalledWith(expect.objectContaining({
        method: 'delete',
        url: mockUrl,
        headers: expect.objectContaining({
          'X-Tenant-ID': mockTenantId,
        }),
      }));
    });
  });

  describe('caching', () => {
    it('should handle cache errors gracefully when reading from cache', async () => {
      // Mock cache error
      cacheManager.get.mockRejectedValueOnce(new Error('Cache error'));
      
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.get).toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
    
    it('should handle cache errors gracefully when setting cache', async () => {
      // Mock cache error
      cacheManager.set.mockRejectedValueOnce(new Error('Cache error'));
      
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 200,
      } as AxiosResponse);
      
      const result = await service.get(
        mockUrl,
        mockTenantId,
        {
          entityType: mockEntityType,
          operation: mockOperation,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
        },
      );
      
      expect(result).toEqual(mockResponseData);
      expect(cacheManager.set).toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
    
    it('should handle cache errors gracefully when invalidating cache', async () => {
      // Mock cache error
      cacheManager.del.mockRejectedValueOnce(new Error('Cache error'));
      
      // Mock HTTP request implementation
      (httpService.axiosRef.request as jest.Mock).mockResolvedValueOnce({ 
        data: mockResponseData,
        status: 201,
      } as AxiosResponse);
      
      const mockRequestData = { name: 'New User' };
      
      await service.post(
        mockUrl,
        mockTenantId,
        mockRequestData,
        {
          entityType: mockEntityType,
          operation: OperationType.CREATE,
          integrationName: mockIntegrationName,
          rateLimiter: 'default',
          wideCacheInvalidation: false,
          entityId: 'user-123',
        },
      );
      
      expect(cacheManager.del).toHaveBeenCalled();
      expect(httpService.axiosRef.request).toHaveBeenCalled();
    });
  });

  describe('rate limiting', () => {
    it('should throw an error when the specified rate limiter does not exist', async () => {
      await expect(
        service.get(
          mockUrl,
          mockTenantId,
          {
            entityType: mockEntityType,
            operation: mockOperation,
            integrationName: mockIntegrationName,
            rateLimiter: 'nonexistent',
          },
        ),
      ).rejects.toThrow('Rate limiter "nonexistent" not found');
    });
  });

  describe('getRateLimiterStats', () => {
    it('should return rate limiter statistics', async () => {
      // Given
      // Mock the bottleneck instance methods to return expected values
      jest.spyOn(mockBottleneckInstance, 'running').mockResolvedValue(5);
      jest.spyOn(mockBottleneckInstance, 'queued').mockResolvedValue(3);
      jest.spyOn(mockBottleneckInstance, 'currentReservoir').mockResolvedValue(100);
      jest.spyOn(mockBottleneckInstance, 'counts').mockResolvedValue({
        RECEIVED: 10,
        QUEUED: 3,
        RUNNING: 5,
        DONE: 7
      });

      // When
      const stats = await service.getRateLimiterStats();

      // Then
      expect(stats).toEqual({
        running: 5,
        queued: 3,
        reservoir: 100,
        clusterQueued: 3
      });
    });
  });

  describe('checkLimiterHealth', () => {
    it('should return true if the rate limiter is healthy', async () => {
      const isHealthy = await service.checkLimiterHealth('default');
      
      expect(isHealthy).toBe(true);
    });
    
    it('should return false if the rate limiter check fails', async () => {
      // Mock the ready property to return a promise that rejects
      jest.spyOn(mockBottleneckInstance, 'ready').mockImplementation(() => {
        return Promise.reject(new Error('Rate limiter health check failed'));
      });
      
      const isHealthy = await service.checkLimiterHealth('default');
      
      expect(isHealthy).toBe(false);
    });
  });

  describe('addInterceptor', () => {
    it('should add request interceptors', () => {
      const successCallback = jest.fn();
      const errorCallback = jest.fn();
      
      service.addInterceptor('request', successCallback, errorCallback);
      
      expect(httpService.axiosRef.interceptors.request.use).toHaveBeenCalledWith(
        successCallback,
        errorCallback,
      );
    });
    
    it('should add response interceptors', () => {
      const successCallback = jest.fn();
      const errorCallback = jest.fn();
      
      service.addInterceptor('response', successCallback, errorCallback);
      
      expect(httpService.axiosRef.interceptors.response.use).toHaveBeenCalledWith(
        successCallback,
        errorCallback,
      );
    });
  });

  describe('clearInterceptors', () => {
    it('should clear all interceptors', () => {
      service.clearInterceptors();
      
      expect(httpService.axiosRef.interceptors.request.clear).toHaveBeenCalled();
      expect(httpService.axiosRef.interceptors.response.clear).toHaveBeenCalled();
    });
  });

  describe('onModuleDestroy', () => {
    it('should disconnect all rate limiters on module destroy', async () => {
      // Reset the mocks to ensure clean state
      jest.clearAllMocks();
      
      // Mock disconnect methods
      jest.spyOn(mockBottleneckInstance, 'disconnect').mockImplementation(() => Promise.resolve());
      
      // Call onModuleDestroy
      await service.onModuleDestroy();
      
      // Verify disconnect was called on the instance
      const disconnectFn = mockBottleneckInstance.disconnect as jest.Mock;
      expect(disconnectFn).toHaveBeenCalled();
    });
  });
}); 