import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ConditionGroup } from "../../../interviews/schemas/v2/navigation-rules.schema";
import { v4 as uuidv4 } from 'uuid';

@Schema()
export class InterviewPageDefV2 {
  @Prop({ 
    type: String, 
    required: true,
    default: () => uuidv4(),
    validate: {
      validator: (v: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v),
      message: 'Invalid UUID format'
    }
  })
  pageId!: string;  // UUID: "550e8400-e29b-41d4-a716-************"

  @Prop({ 
    type: String, 
    required: true
  })
  pageName!: string;  // Unique identifier for lookups: "employment_status"

  @Prop({ 
    type: String, 
    required: true 
  })
  pageTitle!: string;  // Display title: "Employment Information"

  @Prop({ 
    type: String, 
    required: true,
    enum: [
      'name', 'dob', 'ssn', 'address', 'phone', 
      'employment', 'job', 'company', 'vip', 'us-citizen',
      'conflicts-of-interest', 'primary-beneficiaries', 
      'contingent-beneficiaries', 'custom-questions', 'document_upload'
    ]
  })
  pageType!: string;  // Explicit page categorization

  @Prop({ 
    type: Boolean, 
    default: true 
  })
  isActive!: boolean;  // Enable/disable without deletion

  @Prop({ 
    type: Number, 
    required: true,
    min: 0
  })
  defaultOrder!: number;  // Default navigation sequence

  @Prop({ 
    type: Boolean, 
    default: false 
  })
  isTerminal!: boolean;  // Marks completion pages

  @Prop({ 
    type: Boolean, 
    default: true 
  })
  isRequired!: boolean;  // Whether page must be completed for interview completion

  // Updated flow configuration with pageName references
  @Prop({ 
    type: Object,
    required: true
  })
  flow!: {
    rules: Array<{
      ruleId: string;
      ruleName: string;
      priority: number;
      when: ConditionGroup;
      goToPageName: string;  // pageName reference
      isActive: boolean;
      description?: string;
    }>;
    
    defaultNext: {
      pageName: string | null;  // pageName reference, null for terminal pages
      label: string;
    };
    
    allowBack?: boolean;
    skipIf?: ConditionGroup;
  };

  // Context-based inclusion conditions for document pages
  @Prop({ 
    type: Array,
    default: []
  })
  inclusionConditions?: Array<{
    field: string;     // "accounts.hasRetirement", "client.tier", "organization.features"
    operator: string;  // "equals", "contains", "exists"
    value: any;        // Expected value
  }>;

  // Document upload configuration (only for document_upload pages)
  @Prop({ 
    type: Object,
    default: null
  })
  uploadConfig?: {
    documentId: string;        // "retirement-forms", "joint-account-docs"
    documentName: string;      // "Retirement Account Forms"
    description?: string;      // Help text for user
    acceptedFormats: string[]; // ["pdf", "jpg", "png"]
    maxSizeBytes: number;      // File size limit
    isRequired: boolean;       // Required vs optional
    
    // Generic processor configuration
    processor: {
      type: 'docusign' | 'email' | 'custom';
      config?: any;
    };
  };

  // Account context (if account-specific)
  @Prop({ 
    type: Object,
    default: null
  })
  accountContext?: {
    accountType: string;
    features: string[];
  };
}

export const InterviewPageDefV2Schema = SchemaFactory.createForClass(InterviewPageDefV2);