import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, SchemaTypes } from 'mongoose';
import { InterviewPageDefV2, InterviewPageDefV2Schema } from 'src/interview-templates/schemas/v2/interview-page-def.schema';

@Schema({ timestamps: true, collection: 'interviewtemplates_v2' })
export class InterviewTemplateV2 extends Document {
  @Prop({ 
    type: String, 
    required: true,
    index: true
  })
  templateName!: string;

  @Prop({ 
    type: String,
    enum: ['client_onboarding', 'account', 'transition', 'custom'],
    default: 'client_onboarding',
    required: true,
    index: true
  })
  templateType!: 'client_onboarding' | 'account' | 'transition' | 'custom';

  @Prop({ 
    type: String,
    enum: ['ira', 'roth', 'brokerage', 'joint', 'offline_trust', 'offline_inherited_ira', 'offline_corporate', 'offline_other', 'offline_simple_ira'],
    required: function() { return this.templateType === 'account'; },
    index: true
  })
  accountType?: string;

  @Prop({ 
    type: Boolean,
    default: false
  })
  isDefaultForType!: boolean;

  @Prop({ 
    type: String,
    default: null
  })
  description?: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'Organisation',
    required: true,
    index: true
  })
  organisationId!: string;

  @Prop({ 
    type: [InterviewPageDefV2Schema], 
    default: [],
    validate: {
      validator: function(pages: InterviewPageDefV2[]) {
        // Ensure unique pageIds and pageNames
        const pageIds = pages.map(p => p.pageId);
        const pageNames = pages.map(p => p.pageName);
        return new Set(pageIds).size === pageIds.length && 
               new Set(pageNames).size === pageNames.length;
      },
      message: 'Duplicate pageId or pageName found'
    }
  })
  pages!: InterviewPageDefV2[];

  // Template versioning - Family-based system
  @Prop({ 
    type: String,
    required: true,
    index: true
  })
  templateFamilyId!: string;  // Groups related versions together

  @Prop({ 
    type: Number, 
    default: 1,
    min: 1
  })
  majorVersion!: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  minorVersion!: number;

  @Prop({ 
    type: String,
    enum: ['draft', 'published', 'archived', 'deprecated'],
    default: 'draft',
    index: true
  })
  status!: 'draft' | 'published' | 'archived' | 'deprecated';

  @Prop({ 
    type: Boolean,
    default: false,
    index: true
  })
  isLatestPublished!: boolean;  // Only one true per family

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'InterviewTemplateV2',
    default: null
  })
  parentTemplateId?: string;  // Previous version reference

  @Prop({ 
    type: Date,
    default: null
  })
  deprecatedAt?: Date;

  @Prop({ type: Date })
  publishedAt?: Date;

  @Prop({ type: Date })
  archivedAt?: Date;

  @Prop({ 
    type: SchemaTypes.ObjectId,
    ref: 'User',
    default: null
  })
  publishedBy?: string;

  @Prop({ 
    type: SchemaTypes.ObjectId,
    ref: 'User',
    default: null
  })
  lastModifiedBy?: string;

  // Template settings
  @Prop({ 
    type: Boolean, 
    default: false 
  })
  isDefault!: boolean;  // Organization's default template

  @Prop({ 
    type: String,
    default: null
  })
  startPageId?: string;  // Entry point (if not first page)

  // Configuration
  @Prop({ 
    type: Object,
    default: {}
  })
  config!: {
    // Navigation settings
    navigation?: {
      allowBranching?: boolean;
      allowBackNavigation?: boolean;
      showProgressBar?: boolean;
      progressBarType?: 'linear' | 'steps' | 'percentage';
      saveProgressEnabled?: boolean;
    };
    
    // Interview settings
    interview?: {
      requireAuthentication?: boolean;
      sessionTimeout?: number;  // minutes
      allowMultipleAttempts?: boolean;
      maxAttempts?: number;
      cooldownPeriod?: number;  // hours between attempts
    };
    
    // UI settings
    ui?: {
      theme?: 'light' | 'dark' | 'auto';
      logoPosition?: 'top-left' | 'top-center' | 'top-right';
      showPageNumbers?: boolean;
      showEstimatedTime?: boolean;
    };
    
    // Completion settings
    completion?: {
      redirectUrl?: string;
      showSummary?: boolean;
      allowPdfDownload?: boolean;
      sendConfirmationEmail?: boolean;
    };
  };

  // Account configuration
  @Prop({ 
    type: Object,
    default: {}
  })
  accountConfig!: {
    supportedTypes?: string[];  // ['Ira', 'RothIra', 'Brokerage']
    requireBeneficiaries?: boolean;
    beneficiaryRules?: {
      allowMultiple?: boolean;
      requirePercentage?: boolean;
      minBeneficiaries?: number;
      maxBeneficiaries?: number;
    };
    documentRequirements?: Record<string, string[]>;  // { 'Ira': ['ID', 'BankStatement'] }
  };

  // Page flow visualization hint
  @Prop({ 
    type: Object,
    default: null
  })
  flowDiagram?: {
    nodes?: Array<{
      id: string;
      pageId: string;
      position: { x: number; y: number; };
    }>;
    edges?: Array<{
      id: string;
      source: string;
      target: string;
      label?: string;
    }>;
  };

  // Metadata
  @Prop({ 
    type: [String],
    default: [],
    index: true
  })
  tags!: string[];  // ['onboarding', 'retirement', 'high-net-worth']

  @Prop({ 
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  })
  complexity!: 'low' | 'medium' | 'high';

  @Prop({ 
    type: Number,
    default: null
  })
  estimatedCompletionTime?: number;  // Total minutes

  @Prop({ 
    type: Object,
    default: {}
  })
  analytics!: {
    avgCompletionTime?: number;
    completionRate?: number;
    dropOffPoints?: Record<string, number>;  // pageId -> count
    lastUpdated?: Date;
  };

  // Compliance
  @Prop({ 
    type: Object,
    default: {}
  })
  compliance?: {
    regulatoryRequirements?: string[];  // ['FINRA', 'SEC']
    lastReviewDate?: Date;
    nextReviewDate?: Date;
    approvedBy?: string;  // User ID
    notes?: string;
  };
}

export const InterviewTemplateV2Schema = SchemaFactory.createForClass(InterviewTemplateV2);