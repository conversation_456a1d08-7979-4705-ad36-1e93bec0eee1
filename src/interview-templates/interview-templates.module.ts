import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ClsModule } from 'nestjs-cls';
import {
  InterviewTemplate,
  InterviewTemplateSchema,
} from './schemas/v1/interview.template';
import {
  InterviewTemplateV2,
  InterviewTemplateV2Schema,
} from './schemas/v2/interview.template';
import {
  InterviewPageDefV2,
  InterviewPageDefV2Schema,
} from './schemas/v2/interview-page-def.schema';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { FlowEvaluationService } from 'src/interviews/services/v2/navigation/interview-flow-evaluation.service';

// V1 Imports
import { InterviewTemplatesV1Controller } from './controllers/v1/interview-templates-v1.controller';
import { InterviewTemplatesV1Service } from './services/v1/interview-templates.service';
import { CustomQuestionsInterviewTemplatesV1Service } from './services/v1/custom-question-templates-v1.service';

// V2 Imports
import { InterviewTemplatesV2Controller } from './controllers/v2/interview-templates-v2.controller';
import { InterviewTemplatesV2Service } from './services/v2/interview-templates.service';
import { InterviewComposerService } from './services/v2/interview-composer.service';

// Shared Imports
import { InterviewTemplatesSharedService } from './services/shared/interview-templates-shared.service';
import { TemplateValidationService } from './services/shared/template-validation.service';
import { SystemTemplateService } from './services/shared/system-template.service';

// Variables Imports
import { VariablesController } from './controllers/shared/variables.controller';
import { VariablesService } from './variables/variables.service';
import { ClientContextVariableProvider } from './variables/client-context-variable.provider';
import { AccountContextVariableProvider } from './variables/account-context-variable.provider';
import { CurrentAccountVariableProvider } from './variables/current-account-variable.provider';

// Legacy imports for backward compatibility (services only)
import { InterviewTemplatesService } from './interview-templates.service';
import { CustomQuestionsInterviewTemplatesService } from './custom-question-templates.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      // V1 schemas
      { name: InterviewTemplate.name, schema: InterviewTemplateSchema },
      // V2 schemas
      { name: InterviewTemplateV2.name, schema: InterviewTemplateV2Schema },
      { name: InterviewPageDefV2.name, schema: InterviewPageDefV2Schema },
    ]),
    forwardRef(() => OrganisationsModule),
    ClsModule,
  ],
  controllers: [
    // Versioned controllers (legacy controller removed to avoid conflicts)
    // Unversioned requests will route to V1 due to defaultVersion: '1' in main.ts
    InterviewTemplatesV1Controller,
    InterviewTemplatesV2Controller,
    VariablesController,
  ],
  providers: [
    // Legacy services (maintain for backward compatibility)
    InterviewTemplatesService,
    CustomQuestionsInterviewTemplatesService,
    
    // V1 services
    InterviewTemplatesV1Service,
    CustomQuestionsInterviewTemplatesV1Service,
    
    // Shared services
    InterviewTemplatesSharedService,
    TemplateValidationService,
    
    // Variables services
    VariablesService,
    ClientContextVariableProvider,
    AccountContextVariableProvider,
    CurrentAccountVariableProvider,
    
    // V2 services
    InterviewTemplatesV2Service,
    InterviewComposerService,
    FlowEvaluationService,
  ],
  exports: [
    // Export both legacy and new services for backward compatibility
    InterviewTemplatesService,
    InterviewTemplatesV1Service,
    InterviewTemplatesV2Service,
    InterviewTemplatesSharedService,
    InterviewComposerService,
  ],
})
export class InterviewTemplatesModule {}