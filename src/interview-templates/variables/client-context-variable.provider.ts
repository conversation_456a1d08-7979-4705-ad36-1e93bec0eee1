import { Injectable } from '@nestjs/common';
import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

@Injectable()
export class ClientContextVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      // Client Status & Preferences
      'client.status.isExistingClient': {
        description: 'Whether client already exists in CRM system',
        type: VariableType.BOOLEAN,
        example: true,
      },
      
      // Contact Information
      'client.contact.firstName': {
        description: 'Client first name from profile',
        type: VariableType.STRING,
        example: '<PERSON>',
      },
      'client.contact.lastName': {
        description: 'Client last name from profile',
        type: VariableType.STRING,
        example: 'Smith',
      },
      'client.contact.email': {
        description: 'Client email address',
        type: VariableType.EMAIL,
        example: '<EMAIL>',
      },
 
      'advisor.firstName': {
        description: 'Primary advisor first name',
        type: VariableType.STRING,
        example: '<PERSON>',
      },
      'advisor.lastName': {
        description: 'Primary advisor last name',
        type: VariableType.STRING,
        example: 'Wilson',
      },
    };
  }
}