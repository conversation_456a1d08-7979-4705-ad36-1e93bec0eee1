import { Injectable } from '@nestjs/common';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { getPageVariableProvider } from './providers.mapper';
import { VariableDefinition } from './types/variable-definition.interface';
import { ClientContextVariableProvider } from './client-context-variable.provider';
import { AccountContextVariableProvider } from './account-context-variable.provider';
import { CurrentAccountVariableProvider } from './current-account-variable.provider';

@Injectable()
export class VariablesService {
  constructor(
    private readonly clientContextProvider: ClientContextVariableProvider,
    private readonly accountContextProvider: AccountContextVariableProvider,
    private readonly currentAccountProvider: CurrentAccountVariableProvider,
  ) {}

  findByPage(pageName: PagesEnum): Record<string, VariableDefinition> {
    return getPageVariableProvider(pageName).getAvailableVariables();
  }

  getClientContextVariables(): Record<string, VariableDefinition> {
    return this.clientContextProvider.getAvailableVariables();
  }

  getMultiAccountVariables(): Record<string, VariableDefinition> {
    return this.accountContextProvider.getAvailableVariables();
  }

  getCurrentAccountVariables(): Record<string, VariableDefinition> {
    return this.currentAccountProvider.getAvailableVariables();
  }
}