import { Injectable } from '@nestjs/common';
import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

@Injectable()
export class CurrentAccountVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      // Current Account Context (only available in account-specific templates)
      // Note: account type is already known from template configuration, 
      // so we focus on dynamic properties that vary per account instance
      
      'current_account.ownership': {
        description: 'Ownership type of current account (individual, joint, trust, etc.)',
        type: VariableType.STRING,
        example: 'individual',
      },
      'current_account.label': {
        description: 'Display label for current account',
        type: VariableType.STRING,
        example: 'John\'s Traditional IRA',
      },
      'current_account.advisoryRate': {
        description: 'Advisory fee rate for current account',
        type: VariableType.NUMBER,
        example: 1.25,
      },
    };
  }
}