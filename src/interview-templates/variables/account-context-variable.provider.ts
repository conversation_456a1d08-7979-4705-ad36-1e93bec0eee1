import { Injectable } from '@nestjs/common';
import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

@Injectable()
export class AccountContextVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      // Multi-Account Variables (available in client templates)
      'accounts.count': {
        description: 'Total number of client accounts',
        type: VariableType.NUMBER,
        example: 3,
      },
      'accounts.types': {
        description: 'Array of all account types client has',
        type: VariableType.ARRAY,
        example: ['ira', 'joint_brokerage', 'roth_ira'],
      },
      'accounts.hasRetirement': {
        description: 'Whether client has any retirement accounts (IRA, 401k, Roth)',
        type: VariableType.BOOLEAN,
        example: true,
      },
      'accounts.hasJoint': {
        description: 'Whether client has any joint accounts',
        type: VariableType.BOOLEAN,
        example: true,
      },
      'accounts.hasIndividual': {
        description: 'Whether client has any individual accounts',
        type: VariableType.BOOLEAN,
        example: true,
      },
      'accounts.hasTrust': {
        description: 'Whether client has any trust accounts',
        type: VariableType.BOOLEAN,
        example: false,
      },
      'accounts.hasBusiness': {
        description: 'Whether client has any business accounts',
        type: VariableType.BOOLEAN,
        example: false,
      },
    };
  }
}