import { Type } from 'class-transformer';
import { 
  IsString, 
  IsOptional, 
  IsArray, 
  ValidateNested, 
  IsObject,
  IsNotEmpty
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreatePageDefDto } from './create-template-v2.dto';

export class UpdateCompleteTemplateV2Dto {
  @ApiPropertyOptional({ description: 'Template name' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  templateName?: string;

  @ApiPropertyOptional({ description: 'Template description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    type: [CreatePageDefDto], 
    description: 'Complete array of pages (replaces existing pages)' 
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePageDefDto)
  pages!: CreatePageDefDto[];

  @ApiPropertyOptional({ description: 'Template configuration' })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional({ description: 'Account configuration' })
  @IsOptional()
  @IsObject()
  accountConfig?: any;

  @ApiPropertyOptional({ description: 'Template tags' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}