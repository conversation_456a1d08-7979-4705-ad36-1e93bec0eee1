import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsNumber, Min } from 'class-validator';

export class CreateVersionV2Dto {
  @ApiPropertyOptional({ 
    description: 'Whether to increment major version instead of minor',
    default: false 
  })
  @IsOptional()
  @IsBoolean()
  isMajorVersion?: boolean;
}

export class VersionInfoDto {
  @ApiProperty()
  templateId!: string;

  @ApiProperty()
  templateFamilyId!: string;

  @ApiProperty()
  templateName!: string;

  @ApiProperty()
  majorVersion!: number;

  @ApiProperty()
  minorVersion!: number;

  @ApiProperty({ enum: ['draft', 'published', 'archived', 'deprecated'] })
  status!: string;

  @ApiProperty()
  isLatestPublished!: boolean;

  @ApiPropertyOptional()
  publishedAt?: Date;

  @ApiPropertyOptional()
  deprecatedAt?: Date;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  lastModifiedBy!: string;
}

export class FamilyVersionsResponseDto {
  @ApiProperty()
  templateFamilyId!: string;

  @ApiProperty()
  familyName!: string;

  @ApiProperty({ type: [VersionInfoDto] })
  versions!: VersionInfoDto[];

  @ApiPropertyOptional({ type: VersionInfoDto })
  latestPublished?: VersionInfoDto;

  @ApiPropertyOptional({ type: VersionInfoDto })
  currentDraft?: VersionInfoDto;
}