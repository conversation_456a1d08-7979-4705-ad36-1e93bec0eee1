import { Type } from 'class-transformer';
import { 
  IsString, 
  IsOptional, 
  IsArray, 
  ValidateNested, 
  IsBoolean,
  IsNumber,
  IsObject,
  IsNotEmpty,
  IsEnum,
  Min
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class FlowConditionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  field!: string;

  @ApiProperty({ enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'in', 'not_in', 'exists', 'not_exists'] })
  @IsEnum(['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'in', 'not_in', 'exists', 'not_exists'])
  operator!: string;

  @ApiPropertyOptional()
  @IsOptional()
  value?: any;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  label?: string;
}

export class FlowConditionGroupDto {
  @ApiProperty({ enum: ['AND', 'OR'] })
  @IsEnum(['AND', 'OR'])
  logic!: 'AND' | 'OR';

  @ApiProperty({ type: [FlowConditionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowConditionDto)
  conditions!: FlowConditionDto[];
}

export class NavigationRuleDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  ruleName!: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  priority!: number;

  @ApiProperty({ type: FlowConditionGroupDto })
  @ValidateNested()
  @Type(() => FlowConditionGroupDto)
  when!: FlowConditionGroupDto;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  goToPageName!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;
}


export class PageFlowDto {
  @ApiProperty({ type: [NavigationRuleDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NavigationRuleDto)
  rules!: NavigationRuleDto[];

  @ApiProperty()
  @ValidateNested()
  @Type(() => Object)
  defaultNext!: {
    pageName: string | null;
    label: string;
  };

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  allowBack?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @ValidateNested()
  @Type(() => FlowConditionGroupDto)
  skipIf?: FlowConditionGroupDto;

  @ApiPropertyOptional({ description: 'Whether this page ends the interview flow' })
  @IsOptional()
  @IsBoolean()
  isTerminal?: boolean;
}

export class UploadProcessorConfigDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  config?: any;
}

export class UploadConfigDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  documentId!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  documentName!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  acceptedFormats!: string[];

  @ApiProperty()
  @IsNumber()
  @Min(1)
  maxSizeBytes!: number;

  @ApiProperty()
  @IsBoolean()
  isRequired!: boolean;

  @ApiProperty({
    type: Object,
    properties: {
      type: { type: 'string', enum: ['docusign', 'email', 'custom'] },
      config: { type: 'object' }
    }
  })
  @ValidateNested()
  @Type(() => Object)
  processor!: {
    type: 'docusign' | 'email' | 'custom';
    config?: any;
  };
}

export class InclusionConditionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  field!: string;

  @ApiProperty({ enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists'] })
  @IsEnum(['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'exists', 'not_exists'])
  operator!: string;

  @ApiPropertyOptional()
  @IsOptional()
  value?: any;
}

export class CreatePageDefDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pageName!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  pageTitle!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  pageDescription?: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  defaultOrder!: number;

  @ApiProperty({ 
    enum: [
      'name', 'dob', 'ssn', 'address', 'phone', 
      'employment', 'job', 'company', 'vip', 'us-citizen',
      'conflicts-of-interest', 'primary-beneficiaries', 
      'contingent-beneficiaries', 'custom-questions', 'document_upload'
    ] 
  })
  @IsEnum([
    'name', 'dob', 'ssn', 'address', 'phone', 
    'employment', 'job', 'company', 'vip', 'us-citizen',
    'conflicts-of-interest', 'primary-beneficiaries', 
    'contingent-beneficiaries', 'custom-questions', 'document_upload'
  ])
  pageType!: string;

  @ApiProperty({ type: PageFlowDto })
  @ValidateNested()
  @Type(() => PageFlowDto)
  flow!: PageFlowDto;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isRequired?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  applicableAccountTypes?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  uiSchema?: any;

  @ApiPropertyOptional({
    description: 'Upload configuration for document_upload pages',
    type: UploadConfigDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UploadConfigDto)
  uploadConfig?: UploadConfigDto;

  @ApiPropertyOptional({
    description: 'Conditions for including this page based on context data',
    type: [InclusionConditionDto]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InclusionConditionDto)
  inclusionConditions?: InclusionConditionDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  metadata?: any;
}

export class CreateTemplateV2Dto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  templateName!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    enum: ['client_onboarding', 'account', 'transition', 'custom'],
    description: 'Type of template that determines which context variables are available'
  })
  @IsEnum(['client_onboarding', 'account', 'transition', 'custom'])
  templateType!: 'client_onboarding' | 'account' | 'transition' | 'custom';

  @ApiPropertyOptional({
    enum: ['ira', 'roth', 'brokerage', 'joint', 'offline_trust', 'offline_inherited_ira', 'offline_corporate', 'offline_other', 'offline_simple_ira'],
    description: 'Required when templateType is "account". Specifies which account type this template is for.'
  })
  @IsOptional()
  @IsEnum(['ira', 'roth', 'brokerage', 'joint', 'offline_trust', 'offline_inherited_ira', 'offline_corporate', 'offline_other', 'offline_simple_ira'])
  accountType?: string;

  @ApiProperty({ type: [CreatePageDefDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePageDefDto)
  pages!: CreatePageDefDto[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  accountConfig?: any;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}