import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { VariablesService } from '../../variables/variables.service';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { VariableDefinition } from '../../variables/types/variable-definition.interface';

@ApiTags('Interview Templates - Variables')
@Controller('interview-templates/available-variables')
export class VariablesController {
  constructor(private readonly service: VariablesService) {}

  @Get('client-context')
  @ApiOperation({
    summary: 'Get available client context variables for conditional logic',
    description: 'Returns all client-level variables that can be used in conditional flow rules (status, features, preferences)',
  })
  @ApiResponse({
    status: 200,
    description: 'Available client context variables for conditional logic',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            description: 'Human-readable description of the variable',
          },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'array', 'percentage', 'phone', 'email', 'address', 'currency'],
            description: 'Data type of the variable',
          },
          example: {
            description: 'Example value for documentation',
          },
          isConditional: {
            type: 'boolean',
            description: 'Whether this variable is always available or conditional',
          },
        },
        required: ['description', 'type'],
      },
      example: {
        'client.status.isExistingClient': {
          description: 'Whether client already exists in CRM',
          type: 'boolean',
          example: true,
        },
        'client.features.docusignSelected': {
          description: 'Whether client will use DocuSign',
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  getClientContextVariables(): Record<string, VariableDefinition> {
    return this.service.getClientContextVariables();
  }

  @Get('multi-account-context')
  @ApiOperation({
    summary: 'Get available multi-account variables for client templates',
    description: 'Returns multi-account variables that can be used in client template conditional flow rules (hasRetirement, count, etc.). These are available in client templates but NOT in account-specific templates.',
  })
  @ApiResponse({
    status: 200,
    description: 'Available multi-account variables for client templates',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            description: 'Human-readable description of the variable',
          },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'array', 'percentage', 'phone', 'email', 'address', 'currency'],
            description: 'Data type of the variable',
          },
          example: {
            description: 'Example value for documentation',
          },
        },
        required: ['description', 'type'],
      },
      example: {
        'accounts.hasRetirement': {
          description: 'Whether client has any retirement accounts',
          type: 'boolean',
          example: true,
        },
        'accounts.count': {
          description: 'Total number of client accounts',
          type: 'number',
          example: 3,
        },
      },
    },
  })
  getMultiAccountVariables(): Record<string, VariableDefinition> {
    return this.service.getMultiAccountVariables();
  }

  @Get('current-account-context')
  @ApiOperation({
    summary: 'Get available current account variables for account templates',
    description: 'Returns current account variables that can be used in account-specific template conditional flow rules (current_account.type, current_account.isRetirement, etc.). These are ONLY available in account-specific templates.',
  })
  @ApiResponse({
    status: 200,
    description: 'Available current account variables for account-specific templates',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            description: 'Human-readable description of the variable',
          },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'array', 'percentage', 'phone', 'email', 'address', 'currency'],
            description: 'Data type of the variable',
          },
          example: {
            description: 'Example value for documentation',
          },
        },
        required: ['description', 'type'],
      },
      example: {
        'current_account.type': {
          description: 'Type of current account being processed',
          type: 'string',
          example: 'ira',
        },
        'current_account.isRetirement': {
          description: 'Whether current account is a retirement account',
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  getCurrentAccountVariables(): Record<string, VariableDefinition> {
    return this.service.getCurrentAccountVariables();
  }

  @Get(':pageName')
  @ApiOperation({
    summary: 'Get available variables for a specific interview page',
    description: 'Returns all available template variables with their types and descriptions that can be used in the context of the specified page',
  })
  @ApiParam({
    name: 'pageName',
    description: 'The name of the interview page',
    enum: PagesEnum,
  })
  @ApiResponse({
    status: 200,
    description: 'Available variables for the specified page with type information',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            description: 'Human-readable description of the variable',
          },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'array', 'percentage', 'phone', 'email', 'address', 'currency'],
            description: 'Data type of the variable',
          },
          example: {
            description: 'Example value for documentation',
          },
          isConditional: {
            type: 'boolean',
            description: 'Whether this variable is always available or conditional',
          },
        },
        required: ['description', 'type'],
      },
      example: {
        firstName: {
          description: 'First name of the client',
          type: 'string',
          example: 'John',
        },
        lastName: {
          description: 'Last name of the client',
          type: 'string',
          example: 'Smith',
        },
        age: {
          description: 'Calculated age based on date of birth',
          type: 'number',
          example: 38,
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Page not found or no variables available',
  })
  getVariables(@Param('pageName') pageName: PagesEnum): Record<string, VariableDefinition> {
    return this.service.findByPage(pageName);
  }
}