import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Put,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { Roles } from 'src/shared/guards/roles.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { InterviewTemplatesV2Service } from '../../services/v2/interview-templates.service';
import { InterviewComposerService } from '../../services/v2/interview-composer.service';
import { TemplateTypeEnum } from '../../types/template-type.enum';
import { 
  TemplateQueryV2Dto, 
  CreateTemplateV2Dto, 
  PublishTemplateV2Dto, 
  UpdateCompleteTemplateV2Dto,
  CreateVersionV2Dto,
  FamilyVersionsResponseDto
} from '../../dto/v2';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { ClsService } from 'nestjs-cls';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller({ path: '/organisations/:organisationId/interview-templates', version: '2' })
@ApiTags('Interview Templates V2')
export class InterviewTemplatesV2Controller {
  constructor(
    private readonly templateService: InterviewTemplatesV2Service,
    private readonly composerService: InterviewComposerService,
    private readonly cls: ClsService,
  ) {}
  
  @Get()
  @ApiOperation({ summary: 'Get all templates with filtering by type' })
  @ApiQuery({ name: 'templateType', enum: TemplateTypeEnum, required: false })
  @ApiQuery({ name: 'accountType', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  async findAll(
    @Param('organisationId') organisationId: string,
    @Query('templateType') templateType?: string,
    @Query('accountType') accountType?: string,
    @Query('status') status?: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    // Use the service's find method with proper DTO
    const query: TemplateQueryV2Dto = {
      status: status as any,
      templateType,
      accountType,
      page: page ? parseInt(page) : 1,
      limit: limit ? parseInt(limit) : 10,
    };
    
    return this.templateService.find(organisationId, query);
  }

  @Get('defaults')
  @ApiOperation({ summary: 'Get default templates by type' })
  @ApiQuery({ name: 'templateType', enum: TemplateTypeEnum, required: true })
  @ApiQuery({ name: 'accountType', required: false })
  async getDefaults(
    @Param('organisationId') organisationId: string,
    @Query('templateType') templateType: string,
    @Query('accountType') accountType?: string,
  ) {
    // Use the template service to find all templates and filter for defaults
    const query: TemplateQueryV2Dto = {
      templateType,
      accountType,
      status: 'published',
      page: 1,
      limit: 100, // Get enough templates to find the default
    };
    
    const result = await this.templateService.find(organisationId, query);
    
    // Filter to find the default template
    const defaultTemplate = result.templates.find(t => t.isDefaultForType);
    
    if (!defaultTemplate) {
      throw new NotFoundException('No default template found for the specified criteria');
    }
    
    return defaultTemplate;
  }

  @Get('account-templates')
  @ApiOperation({ summary: 'Get all account-specific templates' })
  async getAccountTemplates(
    @Param('organisationId') organisationId: string,
  ) {
    return this.composerService.getAccountTemplates(organisationId);
  }

  @Post(':templateId/create-version')
  @Roles(RolesEnum.Representative, RolesEnum.SuperAdmin)
  @ApiOperation({ 
    summary: 'Create new version from published template',
    description: 'Creates a new draft version based on the published template. Only works with published templates and when no draft exists in the family.'
  })
  async createVersion(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
    @Body() dto: CreateVersionV2Dto,
  ) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.templateService.createVersion(templateId, advisor._id);
  }

  @Get(':templateId')
  async findOne(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
  ) {
    return this.templateService.findById(templateId);
  }

  @Put(':templateId/set-default')
  @Roles(RolesEnum.Representative, RolesEnum.SuperAdmin)
  @ApiOperation({ summary: 'Set template as default for its type' })
  async setAsDefault(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
  ) {
    return this.composerService.setAsDefaultForType(templateId);
  }

  @Get('families/:familyId/versions')
  @ApiOperation({ 
    summary: 'Get all versions in template family',
    description: 'Returns all versions (draft, published, deprecated) for a template family, sorted by version number.'
  })
  async getFamilyVersions(
    @Param('organisationId') organisationId: string,
    @Param('familyId') familyId: string,
  ): Promise<FamilyVersionsResponseDto> {
    const versions = await this.templateService.getFamilyVersions(familyId);
    
    const latestPublished = versions.find(v => v.isLatestPublished && v.status === 'published');
    const currentDraft = versions.find(v => v.status === 'draft');
    const familyName = versions[0]?.templateName || 'Unknown Template';
    
    return {
      templateFamilyId: familyId,
      familyName: familyName.replace(/ \(v\d+\.\d+\)$/, ''), // Remove version suffix
      versions: versions.map(v => {
        const vObj = v.toObject();
        return {
          templateId: v._id.toString(),
          templateFamilyId: v.templateFamilyId,
          templateName: v.templateName,
          majorVersion: v.majorVersion,
          minorVersion: v.minorVersion,
          status: v.status,
          isLatestPublished: v.isLatestPublished,
          publishedAt: v.publishedAt,
          deprecatedAt: v.deprecatedAt,
          createdAt: vObj.createdAt,
          lastModifiedBy: v.lastModifiedBy,
        };
      }),
      latestPublished: latestPublished ? {
        templateId: latestPublished._id.toString(),
        templateFamilyId: latestPublished.templateFamilyId,
        templateName: latestPublished.templateName,
        majorVersion: latestPublished.majorVersion,
        minorVersion: latestPublished.minorVersion,
        status: latestPublished.status,
        isLatestPublished: latestPublished.isLatestPublished,
        publishedAt: latestPublished.publishedAt,
        deprecatedAt: latestPublished.deprecatedAt,
        createdAt: latestPublished.toObject().createdAt,
        lastModifiedBy: latestPublished.lastModifiedBy,
      } : undefined,
      currentDraft: currentDraft ? {
        templateId: currentDraft._id.toString(),
        templateFamilyId: currentDraft.templateFamilyId,
        templateName: currentDraft.templateName,
        majorVersion: currentDraft.majorVersion,
        minorVersion: currentDraft.minorVersion,
        status: currentDraft.status,
        isLatestPublished: currentDraft.isLatestPublished,
        publishedAt: currentDraft.publishedAt,
        deprecatedAt: currentDraft.deprecatedAt,
        createdAt: currentDraft.toObject().createdAt,
        lastModifiedBy: currentDraft.lastModifiedBy,
      } : undefined,
    };
  }

  @Get('families/:familyId/latest')
  @ApiOperation({ 
    summary: 'Get latest published version in family',
    description: 'Returns the currently published version for new interviews in this template family.'
  })
  async getLatestInFamily(
    @Param('organisationId') organisationId: string,
    @Param('familyId') familyId: string,
  ) {
    const template = await this.templateService.getLatestPublishedInFamily(familyId);
    if (!template) {
      throw new NotFoundException(`No published version found for family ${familyId}`);
    }
    return template;
  }

  @Post()
  @Roles(RolesEnum.Representative, RolesEnum.SuperAdmin)
  @ApiOperation({ summary: 'Create a new interview template' })
  async create(
    @Param('organisationId') organisationId: string,
    @Body() dto: CreateTemplateV2Dto,
  ) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.templateService.create(organisationId, dto, advisor._id);
  }

  @Put(':templateId')
  @Roles(RolesEnum.Representative, RolesEnum.SuperAdmin)
  @ApiOperation({ 
    summary: 'Update complete template including pages (draft only)', 
    description: 'Updates the entire template structure including all pages. Only draft templates can be updated. This completely replaces the pages array.'
  })
  async updateComplete(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
    @Body() dto: UpdateCompleteTemplateV2Dto,
  ) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.templateService.updateComplete(templateId, dto, advisor._id);
  }

  @Post(':templateId/publish')
  @Roles(RolesEnum.Representative, RolesEnum.SuperAdmin)
  @ApiOperation({ summary: 'Publish a draft template' })
  async publish(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
    @Body() dto: PublishTemplateV2Dto,
  ) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.templateService.publish(templateId, dto, advisor._id);
  }
}