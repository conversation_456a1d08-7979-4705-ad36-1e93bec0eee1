import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InterviewComposerService } from './interview-composer.service';
import { InterviewTemplateV2 } from '../../schemas/v2/interview.template';
import { ComposeInterviewDto } from 'src/interviews/dto/v2/compose-interview.dto';
import { TemplateTypeEnum } from '../../types/template-type.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';

describe('InterviewComposerService', () => {
  let service: InterviewComposerService;
  let templateModel: jest.Mocked<Model<InterviewTemplateV2>>;

  const mockBaseTemplate = {
    _id: 'base-template-id',
    organisationId: 'org-id',
    templateName: 'Base Template',
    templateType: TemplateTypeEnum.CLIENT_ONBOARDING,
    pages: [
      {
        pageId: 'page-1',
        pageName: 'us_citizen',
        pageTitle: 'US Citizen',
        pageType: 'us_citizen',
        isRequired: true,
        isActive: true,
        defaultOrder: 1,
        flow: {
          rules: [],
          defaultNext: { pageName: 'name' },
          allowBack: false
        }
      },
      {
        pageId: 'page-2',
        pageName: 'name',
        pageTitle: 'Name',
        pageType: 'name',
        isRequired: true,
        isActive: true,
        defaultOrder: 2,
        flow: {
          rules: [],
          defaultNext: null,
          allowBack: true,
          isTerminal: true
        }
      }
    ]
  };

  const mockAccountTemplate = {
    _id: 'account-template-id',
    organisationId: 'org-id',
    templateName: 'IRA Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'ira',
    pages: [
      {
        pageId: 'account-page-1',
        pageName: 'primary_beneficiaries',
        pageTitle: 'Primary Beneficiaries',
        pageType: 'primary_beneficiaries',
        isRequired: true,
        isActive: true,
        defaultOrder: 1,
        flow: {
          rules: [],
          defaultNext: null,
          allowBack: true,
          isTerminal: true
        }
      }
    ]
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewComposerService,
        {
          provide: getModelToken(InterviewTemplateV2.name),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InterviewComposerService>(InterviewComposerService);
    templateModel = module.get(getModelToken(InterviewTemplateV2.name));
  });

  describe('composeInterview', () => {
    it('should compose interview with base template only when no accounts', async () => {
      // Arrange
      const dto: ComposeInterviewDto = {
        clientId: 'client-id',
        organisationId: 'org-id',
        baseTemplateId: 'base-template-id',
        contactType: 'primary',
        accounts: []
      };

      const mockQuery = { session: jest.fn().mockResolvedValue(mockBaseTemplate as any) };
      templateModel.findOne.mockReturnValue(mockQuery);

      // Act
      const result = await service.composeInterview(dto);

      // Assert
      expect(result.composedPages).toHaveLength(2);
      expect(result.composedPages[0]).toMatchObject({
        pageName: 'us_citizen',
        source: 'base_template',
        sourceTemplateId: 'base-template-id',
        visitOrder: 0
      });
      expect(result.composedPages[1]).toMatchObject({
        pageName: 'name',
        source: 'base_template',
        sourceTemplateId: 'base-template-id',
        visitOrder: 1
      });
      expect(result.compositionInfo.baseTemplateId).toBe('base-template-id');
      expect(result.compositionInfo.accountTemplates).toHaveLength(0);
    });

    it('should compose interview with base template and account-specific pages', async () => {
      // Arrange
      const dto: ComposeInterviewDto = {
        clientId: 'client-id',
        organisationId: 'org-id',
        baseTemplateId: 'base-template-id',
        contactType: 'primary',
        accounts: [
          {
            accountId: 'account-123',
            accountType: AccountTypeEnum.Ira,
            accountLabel: 'Test IRA Account',
            templateId: 'account-template-id'
          }
        ]
      };

      const baseQuery = { session: jest.fn().mockResolvedValue(mockBaseTemplate as any) };
      const accountQuery = { session: jest.fn().mockResolvedValue(mockAccountTemplate as any) };
      templateModel.findOne
        .mockReturnValueOnce(baseQuery) // Base template
        .mockReturnValueOnce(accountQuery); // Account template

      // Act
      const result = await service.composeInterview(dto);

      // Assert
      console.log('🧪 COMPOSER TEST - Composed Pages:', JSON.stringify(result.composedPages, null, 2));
      
      // Verify total pages: 2 base + 1 account = 3
      expect(result.composedPages).toHaveLength(3);
      
      // Verify base pages
      expect(result.composedPages[0]).toMatchObject({
        pageName: 'us_citizen',
        source: 'base_template',
        sourceTemplateId: 'base-template-id',
        visitOrder: 0
      });
      expect(result.composedPages[1]).toMatchObject({
        pageName: 'name',
        source: 'base_template',
        sourceTemplateId: 'base-template-id',
        visitOrder: 1
      });
      
      // Verify account-specific page
      expect(result.composedPages[2]).toMatchObject({
        pageName: 'primary_beneficiaries_account-123',
        pageTitle: 'Test IRA Account - Primary Beneficiaries',
        source: 'account_template',
        sourceTemplateId: 'account-template-id',
        visitOrder: 2,
        accountContext: {
          accountId: 'account-123',
          accountType: 'ira',
          accountLabel: 'Test IRA Account'
        }
      });
      
      // Verify composition info
      expect(result.compositionInfo.baseTemplateId).toBe('base-template-id');
      expect(result.compositionInfo.accountTemplates).toHaveLength(1);
      expect(result.compositionInfo.accountTemplates[0]).toMatchObject({
        accountId: 'account-123',
        templateId: 'account-template-id',
        accountType: 'ira'
      });
    });

    it('should generate unique pageIds for account-specific pages', async () => {
      // Arrange
      const dto: ComposeInterviewDto = {
        clientId: 'client-id',
        organisationId: 'org-id',
        baseTemplateId: 'base-template-id',
        contactType: 'primary',
        accounts: [
          {
            accountId: 'account-123',
            accountType: AccountTypeEnum.Ira,
            accountLabel: 'Test IRA Account'
          }
        ]
      };

      const baseQuery = { session: jest.fn().mockResolvedValue(mockBaseTemplate as any) };
      const accountQuery = { session: jest.fn().mockResolvedValue(mockAccountTemplate as any) };
      templateModel.findOne
        .mockReturnValueOnce(baseQuery)
        .mockReturnValueOnce(accountQuery);

      // Act
      const result = await service.composeInterview(dto);

      // Assert
      const accountPage = result.composedPages.find(p => p.source === 'account_template');
      expect(accountPage).toBeDefined();
      expect(accountPage!.pageId).not.toBe('account-page-1'); // Should be new UUID
      expect(accountPage!.pageId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i); // UUID format
    });

    it('should handle missing account template gracefully', async () => {
      // Arrange
      const dto: ComposeInterviewDto = {
        clientId: 'client-id',
        organisationId: 'org-id',
        baseTemplateId: 'base-template-id',
        contactType: 'primary',
        accounts: [
          {
            accountId: 'account-123',
            accountType: AccountTypeEnum.Ira,
            accountLabel: 'Test IRA Account'
          }
        ]
      };

      const baseQuery = { session: jest.fn().mockResolvedValue(mockBaseTemplate as any) };
      const accountQuery = { session: jest.fn().mockResolvedValue(null) };
      templateModel.findOne
        .mockReturnValueOnce(baseQuery) // Base template
        .mockReturnValueOnce(accountQuery); // No account template found

      // Act
      const result = await service.composeInterview(dto);

      // Assert
      expect(result.composedPages).toHaveLength(2); // Only base pages
      expect(result.compositionInfo.accountTemplates).toHaveLength(0);
      
      // Should log warning but not fail
      const accountPages = result.composedPages.filter(p => p.source === 'account_template');
      expect(accountPages).toHaveLength(0);
    });
  });
});