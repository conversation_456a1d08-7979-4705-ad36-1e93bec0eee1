import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { InterviewTemplateV2 } from '../../schemas/v2/interview.template';
import { InterviewPageDefV2 } from '../../schemas/v2/interview-page-def.schema';
import { ComposeInterviewDto } from 'src/interviews/dto/v2/compose-interview.dto';
import { TemplateTypeEnum } from '../../types/template-type.enum';
import { FlowEvaluationService } from 'src/interviews/services/v2/navigation/interview-flow-evaluation.service';
import { InterviewContext } from 'src/interviews/types/v2/client-context.interface';

interface ComposedPage extends InterviewPageDefV2 {
  visitOrder: number;
  source: 'base_template' | 'account_template';
  sourceTemplateId: string;
  accountContext?: {
    accountId: string;
    accountType: string;
    accountLabel: string;
    features: string[];
  };
}

@Injectable()
export class InterviewComposerService {
  private readonly logger = new Logger(InterviewComposerService.name);

  constructor(
    @InjectModel(InterviewTemplateV2.name)
    private templateModel: Model<InterviewTemplateV2>,
    private flowEvaluationService: FlowEvaluationService,
  ) {}

  /**
   * Compose an interview by combining base template with account-specific templates
   */
  async composeInterview(
    dto: ComposeInterviewDto,
    session?: ClientSession
  ): Promise<{
    composedPages: ComposedPage[];
    compositionInfo: any;
    baseTemplate: InterviewTemplateV2;
  }> {
    // 1. Get base template
    const baseTemplate = await this.getBaseTemplate(
      dto.organisationId,
      dto.baseTemplateId,
      session
    );
    
    // 2. Start with base pages
    const composedPages: ComposedPage[] = baseTemplate.pages.map((page, index) => {
      // Ensure base pages have required fields
      const pageObj = (page as any).toObject ? (page as any).toObject() : page;
      return {
        ...pageObj,
        pageId: pageObj.pageId || uuidv4(), // Ensure pageId is present
        pageName: pageObj.pageName || `base_page_${index}`, // Ensure pageName is present
        pageType: pageObj.pageType || pageObj.pageName || `base_page`, // Page type for rendering
        isRequired: pageObj.isRequired !== false, // Default to true if not specified
        visitOrder: index,
        source: 'base_template',
        sourceTemplateId: baseTemplate._id.toString()
      };
    });

    // 3. Track account templates used
    const accountTemplatesUsed: any[] = [];

    // 4. Add account-specific pages
    for (const account of dto.accounts) {
      const accountTemplate = await this.getAccountTemplate(
        dto.organisationId,
        account.accountType,
        account.templateId,
        session
      );

      if (!accountTemplate) {
        this.logger.warn(`No template found for account type ${account.accountType}`);
        continue;
      }

      accountTemplatesUsed.push({
        accountId: account.accountId,
        templateId: accountTemplate._id.toString(),
        accountType: account.accountType
      });

      // Add pages with account context
      const startOrder = composedPages.length;
      const accountPages: ComposedPage[] = accountTemplate.pages.map((page, index) => {
        // Ensure account pages have required fields
        const pageObj = (page as any).toObject ? (page as any).toObject() : page;
        return {
          ...pageObj,
          pageId: uuidv4(), // Generate new page IDs for account pages
          pageName: pageObj.pageName || `account_page_${index}`, // Clean semantic name (no accountId)
          pageType: pageObj.pageType || pageObj.pageName || `account_page`, // Page type for rendering
          pageTitle: `${account.accountLabel} - ${pageObj.pageTitle || 'Account Page'}`,
          isRequired: pageObj.isRequired !== false, // Default to true if not specified
          visitOrder: startOrder + index,
          defaultOrder: 1000 + (startOrder + index) * 10, // Place account pages after base pages
          accountContext: {
            accountId: account.accountId,
            accountType: account.accountType,
            accountLabel: account.accountLabel,
            features: account.features || []
          },
          source: 'account_template',
          sourceTemplateId: accountTemplate._id.toString()
        };
      });

      composedPages.push(...accountPages);
    }

    // 5. Filter pages based on inclusionConditions (for all page types)
    const context: InterviewContext = dto.context || {
      client: {
        status: { isExistingClient: false, skipContactInterview: false, readyToSend: false, featuresSelected: false },
        features: { docusignSelected: false, sendAdv2b: false, doClientProfiling: false },
        contact: { firstName: '', lastName: '', email: '', hasCrmId: false },
        advisor: { firstName: '', lastName: '' },
        organization: { id: dto.organisationId }
      },
      accounts: dto.accounts ? {
        count: dto.accounts.length,
        types: dto.accounts.map(a => a.accountType),
        hasRetirement: dto.accounts.some(a => ['ira', 'roth_ira', '401k'].includes(a.accountType.toLowerCase())),
        hasJoint: dto.accounts.some(a => a.ownership === 'joint'),
        hasIndividual: dto.accounts.some(a => a.ownership === 'individual'),
        hasTrust: dto.accounts.some(a => a.accountType.toLowerCase().includes('trust')),
        hasBusiness: dto.accounts.some(a => a.accountType.toLowerCase().includes('corporate'))
      } : { count: 0, types: [], hasRetirement: false, hasJoint: false, hasIndividual: false, hasTrust: false, hasBusiness: false }
    };
    
    const filteredPages = await this.filterPagesByConditions(composedPages, context);

    // 6. Update page flows to maintain navigation integrity
    const finalPages = this.updatePageFlows(filteredPages);

    // 7. Create composition info
    const compositionInfo = {
      baseTemplateId: baseTemplate._id.toString(),
      accountTemplates: accountTemplatesUsed,
      composedAt: new Date()
    };

    return {
      composedPages: finalPages,
      compositionInfo,
      baseTemplate
    };
  }

  /**
   * Get the base template (client onboarding template)
   */
  private async getBaseTemplate(
    organisationId: string,
    templateId?: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    const query = templateId
      ? { _id: templateId, organisationId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.CLIENT_ONBOARDING,
          isDefaultForType: true,
          status: 'published'
        };

    const template = await this.templateModel
      .findOne(query)
      .session(session);
      
    if (!template) {
      throw new NotFoundException(
        templateId 
          ? `Template ${templateId} not found`
          : 'No default client onboarding template found for organization'
      );
    }

    return template;
  }

  /**
   * Get account-specific template
   */
  private async getAccountTemplate(
    organisationId: string,
    accountType: string,
    templateId?: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2 | null> {
    const query = templateId
      ? { _id: templateId, organisationId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.ACCOUNT,
          accountType,
          isDefaultForType: true,
          status: 'published'
        };

    return this.templateModel
      .findOne(query)
      .session(session);
  }

  /**
   * Update page flows to maintain proper navigation
   * This ensures that:
   * 1. Base template pages flow into account pages
   * 2. Account pages flow back to the next base page or completion
   * 3. All page references are updated with new UUIDs
   */
  private updatePageFlows(pages: ComposedPage[]): ComposedPage[] {
    const pageMap = new Map(pages.map(p => [p.pageId, p]));
    const oldToNewPageIdMap = new Map<string, string>();
    
    // Build mapping of old page names/IDs to new page IDs
    pages.forEach(page => {
      if (page.pageName) {
        oldToNewPageIdMap.set(page.pageName, page.pageId);
      }
    });
    
    return pages.map((page, index) => {
      const nextPage = pages[index + 1];
      const updatedPage = { ...page };
      
      // Update flow if it exists
      if (updatedPage.flow) {
        // Update rules
        if (updatedPage.flow.rules && Array.isArray(updatedPage.flow.rules)) {
          updatedPage.flow.rules = updatedPage.flow.rules.map(rule => {
            // Try to resolve page references
            const targetPageId = this.resolvePageReference(
              rule.goToPageName,
              oldToNewPageIdMap,
              pages
            );
            
            return {
              ...rule,
              goToPageName: rule.goToPageName // Keep pageName reference as-is
            };
          });
        }
        
        // Update defaultNext - keep pageName references, no need to resolve to pageId
        if (updatedPage.flow.defaultNext) {
          // Keep the existing pageName reference as-is
          // The navigation service will resolve pageName to pageId at runtime
          // No changes needed here - defaultNext already uses pageName structure
        }
        
        // If this is the last page and not terminal, mark it as terminal
        if (index === pages.length - 1 && !updatedPage.isTerminal) {
          updatedPage.isTerminal = true;
        }
      }
      
      return updatedPage;
    });
  }

  /**
   * Resolve page reference by name or ID
   */
  private resolvePageReference(
    reference: string,
    oldToNewMap: Map<string, string>,
    pages: ComposedPage[]
  ): string | null {
    // First try direct mapping
    if (oldToNewMap.has(reference)) {
      return oldToNewMap.get(reference)!;
    }
    
    // Then try to find by page name
    const targetPage = pages.find(p => 
      p.pageName === reference || p.pageId === reference
    );
    
    return targetPage?.pageId || null;
  }

  /**
   * Get all account templates for an organization
   */
  async getAccountTemplates(
    organisationId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2[]> {
    return this.templateModel
      .find({
        organisationId,
        templateType: TemplateTypeEnum.ACCOUNT,
        status: 'published'
      })
      .session(session)
      .sort({ accountType: 1, createdAt: -1 });
  }

  /**
   * Set a template as default for its type and account type
   */
  async setAsDefaultForType(
    templateId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    const template = await this.templateModel
      .findById(templateId)
      .session(session);
      
    if (!template) {
      throw new NotFoundException(`Template ${templateId} not found`);
    }
    
    // Unset other defaults of the same type
    const query: any = {
      organisationId: template.organisationId,
      templateType: template.templateType,
      isDefaultForType: true,
      _id: { $ne: templateId }
    };
    
    if (template.templateType === TemplateTypeEnum.ACCOUNT) {
      query.accountType = template.accountType;
    }
    
    await this.templateModel.updateMany(
      query,
      { $set: { isDefaultForType: false } },
      { session }
    );
    
    // Set this template as default
    template.isDefaultForType = true;
    await template.save({ session });
    
    this.logger.log(
      `Set template ${templateId} as default for type ${template.templateType}` +
      (template.accountType ? ` and account type ${template.accountType}` : '')
    );
    
    return template;
  }

  /**
   * Filter pages based on their inclusionConditions using existing evaluation system
   */
  private async filterPagesByConditions(
    pages: ComposedPage[],
    context: InterviewContext
  ): Promise<ComposedPage[]> {
    const filteredPages: ComposedPage[] = [];
    
    for (const page of pages) {
      // If page has no inclusionConditions, include it by default
      if (!page.inclusionConditions || page.inclusionConditions.length === 0) {
        filteredPages.push(page);
        continue;
      }
      
      try {
        // Convert inclusionConditions to ConditionGroup format
        const conditionGroup = {
          logic: 'AND' as const,
          conditions: page.inclusionConditions.map(condition => ({
            field: condition.field,
            operator: condition.operator,
            value: condition.value,
            label: condition.field // Optional label
          }))
        };
        
        // Use existing FlowEvaluationService to evaluate conditions
        const shouldInclude = await this.flowEvaluationService.evaluateConditionGroup(
          conditionGroup,
          {}, // No page answers during composition
          context
        );
        
        if (shouldInclude) {
          this.logger.debug(`Including page ${page.pageName} based on conditions`);
          filteredPages.push(page);
        } else {
          this.logger.debug(`Excluding page ${page.pageName} based on conditions`);
        }
      } catch (error) {
        this.logger.error(`Error evaluating conditions for page ${page.pageName}`, error);
        // Include page by default if evaluation fails
        filteredPages.push(page);
      }
    }
    
    return filteredPages;
  }
}