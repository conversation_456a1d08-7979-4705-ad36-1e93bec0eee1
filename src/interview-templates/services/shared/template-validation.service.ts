import { Injectable, BadRequestException } from '@nestjs/common';
import { NavigationFlowRule, ConditionGroup, FieldCondition } from 'src/interviews/schemas/v2/navigation-rules.schema';

@Injectable()
export class TemplateValidationService {

  /**
   * Validate that conditional flow rules use appropriate variables for template type
   */
  validateConditionalVariables(
    rules: NavigationFlowRule[], 
    templateType: 'client_onboarding' | 'account' | 'transition' | 'custom'
  ): void {
    if (!rules || rules.length === 0) return;

    for (const rule of rules) {
      this.validateConditionGroup(rule.when, templateType, `rule "${rule.ruleName}"`);
    }
  }

  /**
   * Validate condition group variables
   */
  private validateConditionGroup(
    group: ConditionGroup, 
    templateType: string, 
    context: string
  ): void {
    for (const condition of group.conditions) {
      this.validateFieldCondition(condition, templateType, context);
    }
  }

  /**
   * Validate individual field condition variables
   */
  private validateFieldCondition(
    condition: FieldCondition, 
    templateType: string, 
    context: string
  ): void {
    const field = condition.field;

    // Check for current_account variables in non-account templates
    if (field.startsWith('current_account.')) {
      if (templateType !== 'account') {
        throw new BadRequestException(
          `Variable "${field}" can only be used in account-specific templates. ` +
          `Found in ${context} for template type "${templateType}". ` +
          `Use multi-account variables like "accounts.hasRetirement" instead.`
        );
      }
    }

    // Check for multi-account variables in account templates (optional warning)
    if (field.startsWith('accounts.') && templateType === 'account') {
      // Note: This could be a warning rather than an error
      // Multi-account variables might still be useful in account templates
      // for complex conditional logic
    }
  }

  /**
   * Validate skip conditions
   */
  validateSkipConditions(
    skipIf: ConditionGroup | undefined, 
    templateType: 'client_onboarding' | 'account' | 'transition' | 'custom',
    pageName: string
  ): void {
    if (!skipIf) return;
    
    this.validateConditionGroup(skipIf, templateType, `skip condition for page "${pageName}"`);
  }

  /**
   * Get allowed variable prefixes for template type
   */
  getAllowedVariablePrefixes(templateType: string): string[] {
    const baseVariables = ['client.', 'organization.', 'advisor.'];
    
    switch (templateType) {
      case 'account':
        return [...baseVariables, 'current_account.', 'accounts.'];
      case 'client_onboarding':
        return [...baseVariables, 'accounts.'];
      case 'transition':
      case 'custom':
        return [...baseVariables, 'accounts.']; // Could be customized per type
      default:
        return baseVariables;
    }
  }
}