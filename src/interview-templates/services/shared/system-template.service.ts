import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
import { 
  DEFAULT_CLIENT_ONBOARDING_TEMPLATE,
  DEFAULT_ACCOUNT_TEMPLATES 
} from 'src/interview-templates/seeds/default-account-templates.seed';
import { InterviewTemplatesV2Service } from '../v2/interview-templates.service';

@Injectable()
export class SystemTemplateService {
  private readonly logger = new Logger(SystemTemplateService.name);

  constructor(
    @InjectModel(InterviewTemplateV2.name)
    private readonly templateModel: Model<InterviewTemplateV2>,
    private readonly templatesService: InterviewTemplatesV2Service,
  ) {}

  /**
   * Seed default templates for a new organization
   * This should be called when a new organization is created
   */
  async seedDefaultTemplatesForOrganization(
    organisationId: string,
    session?: ClientSession
  ): Promise<void> {
    this.logger.log(`Seeding default templates for organization ${organisationId}`);

    try {
      // Check if organization already has templates
      const existingTemplates = await this.templateModel
        .countDocuments({ organisationId })
        .session(session);

      if (existingTemplates > 0) {
        this.logger.warn(`Organization ${organisationId} already has ${existingTemplates} templates, skipping seeding`);
        return;
      }

      // Seed client onboarding template
      await this.createOrganizationTemplate(
        organisationId,
        DEFAULT_CLIENT_ONBOARDING_TEMPLATE,
        'system-admin',
        session
      );

      // Seed all account templates
      for (const accountTemplate of DEFAULT_ACCOUNT_TEMPLATES) {
        await this.createOrganizationTemplate(
          organisationId,
          accountTemplate,
          'system-admin',
          session
        );
      }

      this.logger.log(`Successfully seeded ${1 + DEFAULT_ACCOUNT_TEMPLATES.length} default templates for organization ${organisationId}`);
    } catch (error) {
      this.logger.error(`Failed to seed default templates for organization ${organisationId}`, {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get all system-level default templates
   * These are the master templates that get copied to new organizations
   */
  getSystemDefaultTemplates(): {
    clientOnboarding: typeof DEFAULT_CLIENT_ONBOARDING_TEMPLATE;
    accountTemplates: typeof DEFAULT_ACCOUNT_TEMPLATES;
  } {
    return {
      clientOnboarding: DEFAULT_CLIENT_ONBOARDING_TEMPLATE,
      accountTemplates: DEFAULT_ACCOUNT_TEMPLATES,
    };
  }

  /**
   * Update an organization's templates from the latest system defaults
   * This can be used to push updates to existing organizations
   */
  async updateOrganizationFromSystemDefaults(
    organisationId: string,
    options: {
      overwriteCustomized?: boolean;
      createMissing?: boolean;
    } = {},
    session?: ClientSession
  ): Promise<{
    created: number;
    updated: number;
    skipped: number;
  }> {
    this.logger.log(`Updating organization ${organisationId} templates from system defaults`);

    const { overwriteCustomized = false, createMissing = true } = options;
    let created = 0; let updated = 0; let skipped = 0;

    try {
      // Get existing organization templates
      const existingTemplates = await this.templateModel
        .find({ 
          organisationId,
          status: 'published'
        })
        .session(session);

      const existingByType = new Map();
      existingTemplates.forEach(template => {
        const key = template.templateType === 'account' 
          ? `account_${template.accountType}` 
          : template.templateType;
        existingByType.set(key, template);
      });

      // Process client onboarding template
      const clientKey = 'client_onboarding';
      const existingClient = existingByType.get(clientKey);
      
      if (!existingClient && createMissing) {
        await this.createOrganizationTemplate(
          organisationId,
          DEFAULT_CLIENT_ONBOARDING_TEMPLATE,
          'system-update',
          session
        );
        created++;
      } else if (existingClient && (overwriteCustomized || existingClient.isDefaultForType)) {
        await this.updateOrganizationTemplate(
          existingClient,
          DEFAULT_CLIENT_ONBOARDING_TEMPLATE,
          'system-update',
          session
        );
        updated++;
      } else {
        skipped++;
      }

      // Process account templates
      for (const accountTemplate of DEFAULT_ACCOUNT_TEMPLATES) {
        const accountKey = `account_${accountTemplate.accountType}`;
        const existingAccount = existingByType.get(accountKey);

        if (!existingAccount && createMissing) {
          await this.createOrganizationTemplate(
            organisationId,
            accountTemplate,
            'system-update',
            session
          );
          created++;
        } else if (existingAccount && (overwriteCustomized || existingAccount.isDefaultForType)) {
          await this.updateOrganizationTemplate(
            existingAccount,
            accountTemplate,
            'system-update',
            session
          );
          updated++;
        } else {
          skipped++;
        }
      }

      this.logger.log(`Template update complete for organization ${organisationId}: created=${created}, updated=${updated}, skipped=${skipped}`);
      
      return { created, updated, skipped };
    } catch (error) {
      this.logger.error(`Failed to update organization ${organisationId} from system defaults`, {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Check if an organization needs default template seeding
   */
  async organizationNeedsDefaultTemplates(
    organisationId: string,
    session?: ClientSession
  ): Promise<boolean> {
    const templateCount = await this.templateModel
      .countDocuments({ organisationId })
      .session(session);
    
    return templateCount === 0;
  }

  // Private helper methods

  private async createOrganizationTemplate(
    organisationId: string,
    templateDefinition: any,
    userId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    // Create template using the V2 service for proper validation
    const created = await this.templatesService.create(
      organisationId,
      {
        ...templateDefinition,
        // Ensure it's marked as default for the organization
        isDefaultForType: true,
      },
      userId,
      session
    );

    // Immediately publish the template
    await this.templatesService.publish(
      created._id.toString(),
      {
        publishNotes: 'System default template',
        makeDefault: true,
      },
      userId,
      session
    );

    return created;
  }

  private async updateOrganizationTemplate(
    existingTemplate: InterviewTemplateV2,
    newDefinition: any,
    userId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    // Create new version of the template
    const updated = await this.templatesService.updateComplete(
      existingTemplate._id.toString(),
      {
        ...newDefinition,
        versionNotes: 'Updated from system defaults',
      },
      userId,
      session
    );

    // Publish the updated template
    await this.templatesService.publish(
      updated._id.toString(),
      {
        publishNotes: 'Updated system default template',
        makeDefault: true,
      },
      userId,
      session
    );

    return updated;
  }
}