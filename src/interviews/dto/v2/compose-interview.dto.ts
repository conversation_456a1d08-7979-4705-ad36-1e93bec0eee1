import { IsString, IsOptional, IsArray, ValidateNested, IsEnum, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { InterviewContext } from 'src/interviews/types/v2/client-context.interface';

class AccountTemplateDto {
  @ApiProperty()
  @IsString()
  accountId!: string;

  @ApiProperty({ enum: AccountTypeEnum })
  @IsEnum(AccountTypeEnum)
  accountType!: AccountTypeEnum;

  @ApiProperty()
  @IsString()
  accountLabel!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  ownership?: string; // 'individual', 'joint', etc.

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  features?: string[]; // Account-specific features
}

// Remove individual context DTOs - use InterviewContext directly

export class ComposeInterviewDto {
  @ApiProperty()
  @IsString()
  clientId!: string;

  @ApiProperty()
  @IsString()
  organisationId!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  baseTemplateId?: string;

  @ApiProperty({ type: [AccountTemplateDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountTemplateDto)
  accounts!: AccountTemplateDto[];

  @ApiProperty({ enum: ['primary', 'secondary'] })
  @IsString()
  contactType!: 'primary' | 'secondary';

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  advisorId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  context?: InterviewContext;
}