import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SubmitPageV2Dto {
  @ApiProperty({ description: 'Unique page name (e.g., "primary_address", "employment_status")' })
  @IsString()
  @IsNotEmpty()
  pageName!: string;

  @ApiProperty({ description: 'Page answers (will be sent to CRM, not stored)' })
  @IsNotEmpty()
  answers!: Record<string, any>;

  @ApiPropertyOptional({ description: 'Page metadata' })
  @IsOptional()
  metadata?: {
    timeSpent?: number;
    deviceType?: string;
    timestamp?: Date;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
  };

  @ApiPropertyOptional({ description: 'Uploaded file for document_upload pages', type: 'string', format: 'binary' })
  @IsOptional()
  uploadedFile?: Express.Multer.File;
}