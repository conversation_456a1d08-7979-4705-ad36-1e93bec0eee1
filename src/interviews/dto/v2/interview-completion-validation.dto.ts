import { ApiProperty } from '@nestjs/swagger';

export type ValidationErrorType = 
  | 'UNSYNCED_PAGE' 
  | 'MANDATORY_PAGE_INCOMPLETE' 
  | 'PAGE_IN_PROGRESS' 
  | 'SYNC_FAILED'
  | 'REQUIRED_DOCUMENT_MISSING'
  | 'DOCUMENT_VALIDATION_FAILED';

export class ValidationError {
  @ApiProperty({
    description: 'Type of validation error',
    enum: [
      'UNSYNCED_PAGE', 
      'MANDATORY_PAGE_INCOMPLETE', 
      'PAGE_IN_PROGRESS', 
      'SYNC_FAILED',
      'REQUIRED_DOCUMENT_MISSING',
      'DOCUMENT_VALIDATION_FAILED'
    ],
  })
  type!: ValidationErrorType;

  @ApiProperty({ description: 'Page ID that has the error' })
  pageId!: string;

  @ApiProperty({ description: 'Page name for easier identification' })
  pageName!: string;

  @ApiProperty({ description: 'Human-readable error message' })
  message!: string;

  @ApiProperty({ description: 'Current page status', required: false })
  pageStatus?: string;

  @ApiProperty({ description: 'Current sync status', required: false })
  syncStatus?: string;
}

export type ValidationWarningType = 
  | 'PAGE_FILLED_BUT_SKIPPABLE' 
  | 'PARTIAL_COMPLETION'
  | 'DOCUMENT_CONFIG_MISSING'
  | 'OPTIONAL_DOCUMENT_MISSING'
  | 'ORPHANED_DOCUMENT_UPLOAD';

export class ValidationWarning {
  @ApiProperty({
    description: 'Type of validation warning',
    enum: [
      'PAGE_FILLED_BUT_SKIPPABLE', 
      'PARTIAL_COMPLETION',
      'DOCUMENT_CONFIG_MISSING',
      'OPTIONAL_DOCUMENT_MISSING',
      'ORPHANED_DOCUMENT_UPLOAD'
    ],
  })
  type!: ValidationWarningType;

  @ApiProperty({ description: 'Page ID that has the warning' })
  pageId!: string;

  @ApiProperty({ description: 'Page name for easier identification' })
  pageName!: string;

  @ApiProperty({ description: 'Warning message' })
  message!: string;
}

export class PageValidationInfo {
  @ApiProperty({ description: 'Page ID' })
  pageId!: string;

  @ApiProperty({ description: 'Page name' })
  pageName!: string;

  @ApiProperty({ description: 'Current page status' })
  status!: string;

  @ApiProperty({ description: 'Current sync status' })
  syncStatus!: string;

  @ApiProperty({ description: 'Is this page required in the current flow' })
  isRequired!: boolean;

  @ApiProperty({ description: 'Visit order (-1 if not visited)' })
  visitOrder!: number;
}

export class InterviewCompletionValidationDto {
  @ApiProperty({ description: 'Whether the interview is ready for completion' })
  isValid!: boolean;

  @ApiProperty({ description: 'Overall completion percentage' })
  completionPercentage!: number;

  @ApiProperty({ 
    description: 'List of validation errors that prevent completion',
    type: [ValidationError]
  })
  errors!: ValidationError[];

  @ApiProperty({ 
    description: 'List of validation warnings (non-blocking)',
    type: [ValidationWarning]
  })
  warnings!: ValidationWarning[];

  @ApiProperty({ 
    description: 'List of page names that are required in current flow',
    type: [String]
  })
  requiredPages!: string[];

  @ApiProperty({ 
    description: 'List of page names that are completed',
    type: [String]
  })
  completedPages!: string[];

  @ApiProperty({ 
    description: 'List of page names that are not synced',
    type: [String]
  })
  unsyncedPages!: string[];

  @ApiProperty({ 
    description: 'Detailed information about all pages',
    type: [PageValidationInfo]
  })
  pageDetails!: PageValidationInfo[];

  @ApiProperty({ description: 'Summary message for user' })
  summary!: string;
}

export class ValidateInterviewCompletionDto {
  @ApiProperty({ description: 'Interview ID to validate' })
  interviewId!: string;

  @ApiProperty({ 
    description: 'Whether to include detailed page information',
    default: false,
    required: false
  })
  includeDetails?: boolean;
}