import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { InterviewCoreService } from './v2/core/interview-core.service';
import { InterviewPagesService } from './v2/pages/interview-pages.service';
import { InterviewDocumentsService } from './v2/documents/interview-documents.service';
import { InterviewEnvelopeService } from './v2/envelope/interview-envelope.service';
import { InterviewNotificationService } from './v2/notifications/interview-notification.service';
import { InterviewV2QueueService } from './v2/queue/interview-queue.service';
import { CreateInterviewV2Dto } from '../dto/v2/create-interview-v2.dto';
import { SubmitPageV2Dto } from '../dto/v2/submit-page-v2.dto';
import { UploadDocumentDto } from '../dto/v1/upload-document.dto';
import { FilterQuery, ClientSession } from 'mongoose';
import { InterviewV2 } from '../schemas/v2/interview.schema';
import { RequiredDocumentDto } from '../dto/v1/required-document.dto';
import { InterviewResponseV2Dto } from '../dto/v2/interview-response-v2.dto';
import { InterviewCompletionParams } from '../types/v1/interview-data.type';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

@Injectable()
export class InterviewV2Facade {
  private readonly logger = new Logger(InterviewV2Facade.name);

  constructor(
    private readonly core: InterviewCoreService,
    public readonly pages: InterviewPagesService, // expose for backward compatibility
    private readonly docs: InterviewDocumentsService,
    private readonly envelope: InterviewEnvelopeService,
    private readonly notify: InterviewNotificationService,
    private readonly queue: InterviewV2QueueService,
  ) {}

  // CRUD operations
  create(dto: CreateInterviewV2Dto, session?: ClientSession) {
    return this.core.create(dto, session);
  }

  find(filter: FilterQuery<InterviewV2>) {
    return this.core.find(filter);
  }

  findAll() {
    return this.core.find({});
  }

  findOne(filter: FilterQuery<InterviewV2>, session?: ClientSession) {
    return this.core.findOne(filter, session);
  }

  findById(id: string, session?: ClientSession) {
    return this.core.findById(id, session);
  }

  remove(id: string, session?: ClientSession) {
    return this.core.remove(id, session);
  }

  // Page operations (V2 specific - different from V1)
  // Note: V2 uses different page update mechanisms through page instances
  // These legacy V1-style methods are not applicable to V2 architecture
  
  // updatePages - Not implemented in V2 (use individual page submissions)
  // updateInterviewPages - Not implemented in V2 (use individual page submissions)

  addPageToInterview(id: string, pageName: PagesEnum) {
    return this.pages.addPage(id, pageName);
  }

  removePageFromInterview(id: string, pageName: string) {
    return this.pages.removePage(id, pageName);
  }

  validateAllPagesAreSynced(interviewId: string) {
    return this.pages.validateAllSynced(interviewId);
  }

  // Document operations
  async upload(interviewId: string, files: Express.Multer.File[], dto?: UploadDocumentDto, session?: ClientSession) {
    const interview = await this.core.findById(interviewId, session);
    return this.docs.upload(interview as any, files, dto, session);
  }

  addRequiredDocument(interviewId: string, dto: RequiredDocumentDto) {
    return this.docs.addRequiredDocument(interviewId, dto);
  }

  removeRequiredDocument(interviewId: string, dto: RequiredDocumentDto) {
    return this.docs.removeRequiredDocument(interviewId, dto);
  }

  getAccountOpeningAdvisoryFiles(accounts: any[], organisationId?: string) {
    return this.docs.getAccountOpeningAdvisoryFiles(accounts, organisationId);
  }

  getEnvelopeFiles(accountOwnership: any, organisationId: string, primaryContact: any, secondaryContact: any) {
    return this.envelope.getEnvelopeFiles(accountOwnership, organisationId, primaryContact, secondaryContact);
  }

  // Envelope operations
  createDocusignEnvelope(interviewId: string, session?: ClientSession) {
    return this.envelope.createDocusignEnvelope(interviewId, session);
  }

  prepareDocusignEnvelope(interviewId: string, session: ClientSession) {
    return this.envelope.prepareDocusignEnvelope(interviewId, session);
  }

  // Notification operations
  sendDesktopInterviewEmail(id: string) {
    return this.notify.sendDesktopInterviewEmail(id);
  }

  sendNonCitizenEmail(id: string) {
    return this.notify.sendNonCitizenEmail(id);
  }

  sendNotification(interviewId: string, session: ClientSession) {
    return this.notify.sendNotification(interviewId, session);
  }


  enqueueInterviewCompletionFlow(interviewId: string) {
    return this.queue.queueInterviewCompletion(interviewId);
  }

  markInterviewAsComplete(interviewId: string, session?: ClientSession) {
    return this.queue.queueInterviewCompletion(interviewId);
  }

  // Complete workflow
  async complete(id: string, session?: ClientSession): Promise<InterviewV2> {
    // V2 interviews don't need page sync validation in the same way
    // This check should be implemented differently for v2
    
    const interview = await this.findOne({ _id: id }, session);
    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    // For v2, just queue the completion flow with the interview ID
    await this.enqueueInterviewCompletionFlow(id);
    
    // Mark interview as complete
    interview.isComplete = true;
    interview.completedAt = new Date();
    interview.status = 'completed';
    
    return await interview.save();
  }

  // Legacy aliases for backward compatibility
  finish(id: string, session?: ClientSession) {
    return this.complete(id, session);
  }
}