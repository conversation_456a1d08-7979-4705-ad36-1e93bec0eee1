import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Interview, InterviewSchema } from '../../schemas/v1/interview.schema';
import { InterviewV2, InterviewV2Schema } from '../../schemas/v2/interview.schema';
import { InterviewPageInstanceV2, InterviewPageInstanceV2Schema } from '../../schemas/v2/interview-page-instance.schema';
import { InterviewAccountInstanceV2, InterviewAccountInstanceV2Schema } from '../../schemas/v2/interview-account-instance.schema';
import { V1PdfDataProvider } from './v1-pdf-data.provider';
import { V2PdfDataProvider } from './v2-pdf-data.provider';
import { PdfDataProviderFactory } from './pdf-data-provider.factory';
import { ClientsModule } from '../../../clients/clients.module';
import { AdvisorsModule } from '../../../advisors/advisors.module';
import { OrganisationsModule } from '../../../organisations/organisations.module';

/**
 * Module providing version-agnostic PDF data services.
 * This module enables clean separation between interview versions and PDF generation.
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      // V1 schemas
      { name: Interview.name, schema: InterviewSchema },
      
      // V2 schemas
      { name: InterviewV2.name, schema: InterviewV2Schema },
      { name: InterviewPageInstanceV2.name, schema: InterviewPageInstanceV2Schema },
      { name: InterviewAccountInstanceV2.name, schema: InterviewAccountInstanceV2Schema },
    ]),
    
    // Dependencies
    forwardRef(() => ClientsModule),
    forwardRef(() => AdvisorsModule),
    forwardRef(() => OrganisationsModule),
  ],
  providers: [
    V1PdfDataProvider,
    V2PdfDataProvider,
    PdfDataProviderFactory,
  ],
  exports: [
    V1PdfDataProvider,
    V2PdfDataProvider,
    PdfDataProviderFactory,
  ],
})
export class PdfDataModule {}