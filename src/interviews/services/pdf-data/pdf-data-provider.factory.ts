import { Injectable, BadRequestException, Inject } from '@nestjs/common';
import { PdfDataProvider } from './pdf-data-provider.interface';
import { V1PdfDataProvider } from './v1-pdf-data.provider';
import { V2PdfDataProvider } from './v2-pdf-data.provider';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

/**
 * Factory service that creates the appropriate PDF data provider based on interview version.
 * This enables version-agnostic PDF generation while maintaining clean separation of concerns.
 */
@Injectable()
export class PdfDataProviderFactory {
  constructor(
    private readonly v1Provider: V1PdfDataProvider,
    private readonly v2Provider: V2PdfDataProvider,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  /**
   * Creates the appropriate PDF data provider based on API version
   * @param apiVersion - The interview API version ('v1' or 'v2')
   * @returns PDF data provider for the specified version
   */
  create(apiVersion: string): PdfDataProvider {
    this.logger.info(`[PdfDataProviderFactory] Creating PDF data provider for version: ${apiVersion}`);
    
    switch (apiVersion?.toLowerCase()) {
      case 'v1':
        this.logger.info(`[PdfDataProviderFactory] Returning V1 PDF data provider`);
        return this.v1Provider;
      case 'v2':
        this.logger.info(`[PdfDataProviderFactory] Returning V2 PDF data provider`);
        return this.v2Provider;
      default:
        throw new BadRequestException(`Unsupported interview API version: ${apiVersion}`);
    }
  }

  /**
   * Auto-detects version and creates appropriate provider
   * @param interviewId - Interview ID to determine version from
   * @returns Promise resolving to appropriate PDF data provider
   */
  async createForInterview(interviewId: string): Promise<PdfDataProvider> {
    // TODO: Implement auto-detection logic by querying both collections
    // For now, assume we can determine version from interview ID pattern or collection check
    
    // This could be enhanced to automatically detect version:
    // 1. Try V2 collection first (newer)
    // 2. Fall back to V1 if not found
    // 3. Cache version detection for performance
    
    throw new Error('Auto-detection not implemented. Use create(apiVersion) instead.');
  }
}