import { InterviewDataWithCrmInfo } from '../../types/v1/interview-data.type';

/**
 * Type alias for PDF-compatible interview data
 * This maintains compatibility with existing PDF mappers while allowing for different provider implementations
 */
export type PdfCompatibleInterviewData = InterviewDataWithCrmInfo;

/**
 * Interface for providing interview data compatible with PDF generation system.
 * This abstraction allows different interview versions to provide the same data structure
 * for PDF mappers without breaking the existing PDF generation logic.
 */
export interface PdfDataProvider {
  /**
   * Retrieves interview data in the format expected by PDF mappers.
   * Handles all necessary data collection, transformation, and CRM enrichment.
   * 
   * @param interviewId - The interview ID
   * @param session - Optional MongoDB session for transactions
   * @returns Promise resolving to PDF-compatible interview data structure
   */
  getInterviewDataForPdf(
    interviewId: string, 
    session?: any
  ): Promise<PdfCompatibleInterviewData>;
}