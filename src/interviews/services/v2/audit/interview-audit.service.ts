import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InterviewAuditV2 } from 'src/audits/schemas/interview-audit.schema';

import { InterviewAuditEventType } from 'src/audits/types/audit-event-types.enum';

export interface PageNavigationAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  pageId: string;
  pageName: string;
  navigatedTo?: string | null;
  visitOrder: number;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface PageCompletedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  pageId: string;
  pageName: string;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface PageVisitedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  pageId: string;
  pageName: string;
  fromPage?: string;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface InterviewStartedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  templateId: string;
  contactType: string;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface InterviewAbandonedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  lastPageId?: string;
  lastPageName?: string;
  completionPercentage?: number;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface NavigationBranchedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  fromPage: string;
  toPage: string;
  branchCondition: string;
  ruleName?: string;
  metadata?: any;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface DataSyncedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
  pageId: string;
  pageName: string;
  syncJobId?: string;
  duration?: number;
  success: boolean;
  errorMessage?: string;
  metadata?: any;
}

export interface InterviewCompletedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
}

@Injectable()
export class InterviewV2AuditService {
  constructor(
    @InjectModel(InterviewAuditV2.name)
    private readonly auditModel: Model<InterviewAuditV2>,
  ) {}

  async logPageNavigation(data: PageNavigationAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.NAVIGATION_BRANCHED,
      pageId: data.pageId,
      pageName: data.pageName,
      eventData: {
        toPage: data.navigatedTo,
        visitOrder: data.visitOrder,
        isBackNavigation: data.visitOrder < 0,
      },
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logPageVisited(data: PageVisitedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.PAGE_VISITED,
      pageId: data.pageId,
      pageName: data.pageName,
      eventData: {
        fromPage: data.fromPage,
      },
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logPageCompleted(data: PageCompletedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.PAGE_COMPLETED,
      pageId: data.pageId,
      pageName: data.pageName,
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logInterviewStarted(data: InterviewStartedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.INTERVIEW_STARTED,
      eventData: {
        templateId: data.templateId,
        contactType: data.contactType,
      },
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logInterviewCompleted(data: InterviewCompletedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.INTERVIEW_COMPLETED,
    });
  }

  async logInterviewAbandoned(data: InterviewAbandonedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.INTERVIEW_ABANDONED,
      pageId: data.lastPageId,
      pageName: data.lastPageName,
      eventData: {
        completionPercentage: data.completionPercentage,
      },
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logNavigationBranched(data: NavigationBranchedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.NAVIGATION_BRANCHED,
      eventData: {
        fromPage: data.fromPage,
        toPage: data.toPage,
        branchCondition: data.branchCondition,
        ruleName: data.ruleName,
      },
      sessionId: data.sessionId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  }

  async logDataSynced(data: DataSyncedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      organisationId: data.organisationId,
      eventType: InterviewAuditEventType.DATA_SYNCED,
      pageId: data.pageId,
      pageName: data.pageName,
      eventData: {
        syncJobId: data.syncJobId,
        duration: data.duration,
        success: data.success,
        errorMessage: data.errorMessage,
      },
    });
  }

  // Legacy methods for backward compatibility
  async logCrmSyncSuccess(
    interviewId: string,
    pageId: string,
    syncDuration: number,
    clientId?: string,
    pageName?: string,
    syncJobId?: string,
    organisationId?: string,
  ): Promise<void> {
    await this.logDataSynced({
      interviewId,
      clientId: clientId || '',
      organisationId: organisationId || '',
      pageId,
      pageName: pageName || '',
      syncJobId,
      duration: syncDuration,
      success: true,
    });
  }

  async logCrmSyncFailure(
    interviewId: string,
    pageId: string,
    error: string,
    clientId?: string,
    pageName?: string,
    syncJobId?: string,
    organisationId?: string,
  ): Promise<void> {
    await this.logDataSynced({
      interviewId,
      clientId: clientId || '',
      organisationId: organisationId || '',
      pageId,
      pageName: pageName || '',
      syncJobId,
      success: false,
      errorMessage: error,
    });
  }

  async getInterviewAuditTrail(interviewId: string): Promise<InterviewAuditV2[]> {
    return this.auditModel
      .find({ interviewId })
      .sort({ createdAt: 1 })
      .exec();
  }
}