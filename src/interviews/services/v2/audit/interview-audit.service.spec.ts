import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { InterviewV2AuditService } from './interview-audit.service';
import { InterviewAuditEventType } from 'src/audits/types/audit-event-types.enum';
import { InterviewAuditV2 } from 'src/audits/schemas/interview-audit.schema';

describe('InterviewV2AuditService', () => {
  let service: InterviewV2AuditService;
  let mockAuditModel: any;

  beforeEach(async () => {
    mockAuditModel = {
      create: jest.fn().mockResolvedValue({}),
      find: jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue([]),
        }),
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewV2AuditService,
        {
          provide: getModelToken(InterviewAuditV2.name),
          useValue: mockAuditModel,
        },
      ],
    }).compile();

    service = module.get<InterviewV2AuditService>(InterviewV2AuditService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('logInterviewStarted', () => {
    it('should create audit entry for interview started', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-789',
        templateId: 'template-abc',
        contactType: 'primary',
        sessionId: 'session-123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
      };

      await service.logInterviewStarted(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.INTERVIEW_STARTED,
        eventData: {
          templateId: auditData.templateId,
          contactType: auditData.contactType,
        },
        sessionId: auditData.sessionId,
        ipAddress: auditData.ipAddress,
        userAgent: auditData.userAgent,
      });
    });
  });

  describe('logPageCompleted', () => {
    it('should create audit entry for page completed', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-123',
        pageId: 'page-789',
        pageName: 'name-page',
        sessionId: 'session-123',
      };

      await service.logPageCompleted(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.PAGE_COMPLETED,
        pageId: auditData.pageId,
        pageName: auditData.pageName,
        sessionId: auditData.sessionId,
        ipAddress: undefined,
        userAgent: undefined,
      });
    });
  });

  describe('logNavigationBranched', () => {
    it('should create audit entry for conditional navigation', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-123',
        fromPage: 'employment',
        toPage: 'job-details',
        branchCondition: 'employed',
        ruleName: 'employment-flow-rule',
      };

      await service.logNavigationBranched(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.NAVIGATION_BRANCHED,
        eventData: {
          fromPage: auditData.fromPage,
          toPage: auditData.toPage,
          branchCondition: auditData.branchCondition,
          ruleName: auditData.ruleName,
        },
        sessionId: undefined,
        ipAddress: undefined,
        userAgent: undefined,
      });
    });
  });

  describe('logDataSynced', () => {
    it('should create audit entry for successful CRM sync', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-123',
        pageId: 'page-789',
        pageName: 'name-page',
        syncJobId: 'sync-job-123',
        duration: 1500,
        success: true,
      };

      await service.logDataSynced(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.DATA_SYNCED,
        pageId: auditData.pageId,
        pageName: auditData.pageName,
        eventData: {
          syncJobId: auditData.syncJobId,
          duration: auditData.duration,
          success: auditData.success,
          errorMessage: undefined,
        },
      });
    });

    it('should create audit entry for failed CRM sync', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-123',
        pageId: 'page-789',
        pageName: 'name-page',
        syncJobId: 'sync-job-123',
        duration: 500,
        success: false,
        errorMessage: 'CRM API timeout',
      };

      await service.logDataSynced(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.DATA_SYNCED,
        pageId: auditData.pageId,
        pageName: auditData.pageName,
        eventData: {
          syncJobId: auditData.syncJobId,
          duration: auditData.duration,
          success: auditData.success,
          errorMessage: auditData.errorMessage,
        },
      });
    });
  });

  describe('logInterviewCompleted', () => {
    it('should create audit entry for interview completion', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-789',
      };

      await service.logInterviewCompleted(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.INTERVIEW_COMPLETED,
      });
    });
  });

  describe('logInterviewAbandoned', () => {
    it('should create audit entry for interview abandonment', async () => {
      const auditData = {
        interviewId: 'interview-123',
        clientId: 'client-456',
        organisationId: 'org-123',
        lastPageId: 'page-789',
        lastPageName: 'employment',
        completionPercentage: 65,
        metadata: { reason: 'timeout' },
      };

      await service.logInterviewAbandoned(auditData);

      expect(mockAuditModel.create).toHaveBeenCalledWith({
        interviewId: auditData.interviewId,
        clientId: auditData.clientId,
        organisationId: auditData.organisationId,
        eventType: InterviewAuditEventType.INTERVIEW_ABANDONED,
        pageId: auditData.lastPageId,
        pageName: auditData.lastPageName,
        eventData: {
          completionPercentage: auditData.completionPercentage,
        },
        sessionId: undefined,
        ipAddress: undefined,
        userAgent: undefined,
      });
    });
  });

  describe('getInterviewAuditTrail', () => {
    it('should retrieve audit trail for interview', async () => {
      const interviewId = 'interview-123';
      const mockAuditTrail = [
        { eventType: 'interview_started', createdAt: new Date() },
        { eventType: 'page_completed', createdAt: new Date() },
        { eventType: 'interview_completed', createdAt: new Date() },
      ];

      mockAuditModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockAuditTrail),
        }),
      });

      const result = await service.getInterviewAuditTrail(interviewId);

      expect(mockAuditModel.find).toHaveBeenCalledWith({ interviewId });
      expect(result).toEqual(mockAuditTrail);
    });
  });
});