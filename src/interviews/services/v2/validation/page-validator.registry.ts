import { Injectable, BadRequestException } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { Logger } from '@nestjs/common';

export interface PageValidator {
  validate(answers: Record<string, any>): Promise<void>;
}

@Injectable()
export class PageValidatorRegistry {
  private readonly logger = new Logger(PageValidatorRegistry.name);
  private validators = new Map<string, PageValidator>();
  
  register(pageType: string, validator: PageValidator) {
    this.validators.set(pageType, validator);
  }
  
  async validate(pageType: string, answers: Record<string, any>): Promise<void> {
    const validator = this.validators.get(pageType);
    
    if (!validator) {
      // Default validation for unknown page types
      if (!answers || Object.keys(answers).length === 0) {
        // Special case for document_upload pages
        if (pageType === 'document_upload') {
          this.logger.debug(`Document upload page - validation handled separately`);
          return;
        }
        throw new BadRequestException(`Page type ${pageType} requires answers`);
      }
      this.logger.debug(`Using basic validation for unknown page type: ${pageType}`);
      return;
    }
    
    await validator.validate(answers);
  }
}

// Base class for DTO-based validators
export class DtoBasedValidator<T extends object> implements PageValidator {
  constructor(
    private readonly DtoClass: new () => T,
    private readonly logger: Logger
  ) {}

  async validate(answers: Record<string, any>): Promise<void> {
    try {
      const transformedAnswers = plainToClass(this.DtoClass, answers);
      const validationErrors = await validate(transformedAnswers);
      
      if (validationErrors.length > 0) {
        const errorMessages = this.extractValidationErrorMessages(validationErrors);
        throw new BadRequestException(`Validation failed: ${errorMessages}`);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      // If validation fails unexpectedly, fall back to basic validation
      this.logger.warn(`Failed to validate with DTO, falling back to basic validation: ${error.message}`);
      if (!answers || Object.keys(answers).length === 0) {
        throw new BadRequestException(`Answers are required but none were provided`);
      }
    }
  }

  private extractValidationErrorMessages(validationErrors: any[]): string {
    const messages: string[] = [];
    
    for (const error of validationErrors) {
      // Direct constraints on the field
      if (error.constraints) {
        messages.push(...Object.values(error.constraints).map(String));
      }
      
      // Nested validation errors (for ValidateNested fields)
      if (error.children && error.children.length > 0) {
        const nestedMessages = this.extractValidationErrorMessages(error.children);
        if (nestedMessages) {
          messages.push(`${error.property}: ${nestedMessages}`);
        }
      }
    }
    
    return messages.join(', ');
  }
}