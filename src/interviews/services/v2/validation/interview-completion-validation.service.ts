import {
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
import { FlowEvaluationService } from '../navigation/interview-flow-evaluation.service';
import { InterviewDocumentsService } from '../documents/interview-documents.service';
import { InterviewV2StatusType, InterviewPageInstanceStatusEnum, InterviewPageSyncStatusEnum } from 'src/interviews/types/v2/interview-v2-queue-job.enum';
import {
  InterviewCompletionValidationDto,
  ValidationError,
  ValidationWarning,
  PageValidationInfo,
} from 'src/interviews/dto/v2/interview-completion-validation.dto';

interface PageAnswers {
  [pageName: string]: Record<string, any>;
}

interface FlowSimulationResult {
  requiredPages: string[];
  navigationPath: string[];
  skippedPages: string[];
}

@Injectable()
export class InterviewCompletionValidationService {
  private readonly logger = new Logger(InterviewCompletionValidationService.name);

  constructor(
    @InjectModel('InterviewV2')
    private readonly interviewModel: Model<InterviewV2>,

    @InjectModel('InterviewPageInstanceV2')
    private readonly pageInstanceModel: Model<InterviewPageInstanceV2>,

    private readonly flowEvaluationService: FlowEvaluationService,
    private readonly documentsService: InterviewDocumentsService,
  ) {}

  /**
   * Validates if an interview is ready for completion
   * Checks sync status, mandatory pages, and flow requirements
   */
  async validateInterviewCompletion(
    interviewId: string,
    includeDetails: boolean = false,
    session?: ClientSession,
  ): Promise<InterviewCompletionValidationDto> {
    // Get interview with template
    const interview = await this.getInterviewWithTemplate(interviewId, session);
    
    // Get all page instances
    const pageInstances = await this.getPageInstances(interviewId, session);
    
    // Get all answers from page instances
    const pageAnswers = await this.collectPageAnswers(pageInstances);
    
    // Simulate navigation flow to determine required pages
    const flowResult = await this.simulateNavigationFlow(interview, pageAnswers);
    
    // Validate sync status and completion
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    // 1. Validate sync status of completed pages
    const syncValidation = this.validateSyncStatus(pageInstances);
    errors.push(...syncValidation.errors);
    warnings.push(...syncValidation.warnings);
    
    // 2. Validate mandatory pages are completed
    const mandatoryValidation = this.validateMandatoryPages(
      flowResult.requiredPages,
      pageInstances
    );
    errors.push(...mandatoryValidation.errors);
    warnings.push(...mandatoryValidation.warnings);
    
    // 3. Check for pages in transitional states
    const transitionValidation = this.validateTransitionalStates(pageInstances);
    errors.push(...transitionValidation.errors);
    
    // 4. Validate document upload requirements
    const documentValidation = await this.validateDocumentUploads(
      interview,
      pageInstances,
      flowResult.requiredPages
    );
    errors.push(...documentValidation.errors);
    warnings.push(...documentValidation.warnings);
    
    // Build response
    const completedPages = pageInstances
      .filter(p => p.status === InterviewPageInstanceStatusEnum.COMPLETED)
      .map(p => p.pageName);
      
    const unsyncedPages = pageInstances
      .filter(p => p.status === 'completed' && p.syncStatus !== InterviewV2StatusType.SYNCED)
      .map(p => p.pageName);
    
    const pageDetails = includeDetails 
      ? this.buildPageDetails(pageInstances, flowResult.requiredPages)
      : [];
    
    const completionPercentage = this.calculateCompletionPercentage(
      flowResult.requiredPages,
      completedPages
    );
    
    const summary = this.buildSummaryMessage(errors, warnings, completionPercentage);
    
    return {
      isValid: errors.length === 0,
      completionPercentage,
      errors,
      warnings,
      requiredPages: flowResult.requiredPages,
      completedPages,
      unsyncedPages,
      pageDetails,
      summary,
    };
  }

  /**
   * Get interview with populated template
   */
  private async getInterviewWithTemplate(
    interviewId: string,
    session?: ClientSession,
  ): Promise<InterviewV2> {
    const interview = await this.interviewModel
      .findById(interviewId)
      .populate('template')
      .session(session);

    if (!interview) {
      throw new NotFoundException(`Interview ${interviewId} not found`);
    }

    return interview;
  }

  /**
   * Get all page instances for interview
   */
  private async getPageInstances(
    interviewId: string,
    session?: ClientSession,
  ): Promise<InterviewPageInstanceV2[]> {
    return this.pageInstanceModel
      .find({ interviewId })
      .session(session)
      .sort({ visitOrder: 1 });
  }

  /**
   * Collect answers from all page instances
   * This is a simplified approach - in reality we'd need to get answers from a different source
   */
  private async collectPageAnswers(
    pageInstances: InterviewPageInstanceV2[],
  ): Promise<PageAnswers> {
    // TODO: This is a placeholder. In the real system, we need to:
    // 1. Get answers from wherever they're stored (queue messages, separate collection, etc.)
    // 2. For now, we'll return empty answers and simulate based on completion status
    
    const answers: PageAnswers = {};
    
    // For pages that are completed, we assume they have valid answers
    // This is a simplification for the validation logic
    pageInstances
      .filter(p => p.status === InterviewPageInstanceStatusEnum.COMPLETED)
      .forEach(p => {
        answers[p.pageName] = { completed: true };
      });
    
    return answers;
  }

  /**
   * Simulate navigation flow to determine which pages are actually required
   */
  private async simulateNavigationFlow(
    interview: InterviewV2,
    pageAnswers: PageAnswers,
  ): Promise<FlowSimulationResult> {
    const template = interview.template as unknown as InterviewTemplateV2;
    const pages = template.toObject().pages.filter(p => p.isActive);
    
    if (pages.length === 0) {
      return { requiredPages: [], navigationPath: [], skippedPages: [] };
    }
    
    // Start from first page
    const sortedPages = pages.sort((a, b) => a.defaultOrder - b.defaultOrder);
    const startPage = template.startPageId 
      ? pages.find(p => p.pageId === template.startPageId)
      : sortedPages[0];
    
    if (!startPage) {
      return { requiredPages: [], navigationPath: [], skippedPages: [] };
    }
    
    const requiredPages: string[] = [];
    const navigationPath: string[] = [];
    const skippedPages: string[] = [];
    const visitedPages = new Set<string>();
    
    let currentPageName = startPage.pageName;
    let maxIterations = pages.length * 2; // Prevent infinite loops
    
    while (currentPageName && maxIterations > 0) {
      maxIterations--;
      
      if (visitedPages.has(currentPageName)) {
        // Avoid infinite loops
        break;
      }
      
      visitedPages.add(currentPageName);
      const currentPage = pages.find(p => p.pageName === currentPageName);
      
      if (!currentPage) {
        break;
      }
      
      navigationPath.push(currentPageName);
      
      // Check if page should be skipped
      let shouldSkip = false;
      if (currentPage.flow.skipIf) {
        try {
          const answers = pageAnswers[currentPageName] || {};
          shouldSkip = await this.flowEvaluationService.evaluateConditionGroup(
            currentPage.flow.skipIf,
            answers
          );
        } catch (error) {
          this.logger.warn(`Failed to evaluate skip condition for ${currentPageName}: ${error.message}`);
        }
      }
      
      if (shouldSkip) {
        skippedPages.push(currentPageName);
      } else {
        // Only add to required pages if the page is marked as required
        if (currentPage.isRequired !== false) {
          requiredPages.push(currentPageName);
        }
      }
      
      // Determine next page
      let nextPageName: string | null = null;
      
      if (currentPage.flow.isTerminal) {
        break;
      }
      
      // Evaluate flow rules
      if (currentPage.flow.rules && currentPage.flow.rules.length > 0) {
        try {
          const answers = pageAnswers[currentPageName] || {};
          const evaluationResult = await this.flowEvaluationService.evaluateFlowRules(
            currentPage.flow.rules,
            answers
          );
          
          if (evaluationResult) {
            nextPageName = evaluationResult.targetPageName;
          }
        } catch (error) {
          this.logger.warn(`Failed to evaluate flow rules for ${currentPageName}: ${error.message}`);
        }
      }
      
      // Use default next if no rules matched
      if (!nextPageName && currentPage.flow.defaultNext) {
        nextPageName = currentPage.flow.defaultNext.pageName;
      }
      
      currentPageName = nextPageName;
    }
    
    return { requiredPages, navigationPath, skippedPages };
  }

  /**
   * Validate that all completed pages are properly synced
   */
  private validateSyncStatus(
    pageInstances: InterviewPageInstanceV2[],
  ): { errors: ValidationError[]; warnings: ValidationWarning[] } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    pageInstances
      .filter(p => p.status === InterviewPageInstanceStatusEnum.COMPLETED)
      .forEach(page => {
        switch (page.syncStatus) {
          case InterviewPageSyncStatusEnum.FAILED:
            errors.push({
              type: 'SYNC_FAILED',
              pageId: page.pageId,
              pageName: page.pageName,
              message: `Page "${page.pageName}" failed to sync with CRM and must be retried`,
              pageStatus: page.status,
              syncStatus: page.syncStatus,
            });
            break;
            
          case InterviewPageSyncStatusEnum.PENDING:
            errors.push({
              type: 'UNSYNCED_PAGE',
              pageId: page.pageId,
              pageName: page.pageName,
              message: `Page "${page.pageName}" is not yet synced with CRM`,
              pageStatus: page.status,
              syncStatus: page.syncStatus,
            });
            break;
            
          case InterviewPageSyncStatusEnum.SYNCING:
            errors.push({
              type: 'PAGE_IN_PROGRESS',
              pageId: page.pageId,
              pageName: page.pageName,
              message: `Page "${page.pageName}" is currently syncing, please wait`,
              pageStatus: page.status,
              syncStatus: page.syncStatus,
            });
            break;
            
          case InterviewV2StatusType.SYNCED:
            // This is good, no action needed
            break;
            
          default:
            warnings.push({
              type: 'PARTIAL_COMPLETION',
              pageId: page.pageId,
              pageName: page.pageName,
              message: `Page "${page.pageName}" has unknown sync status: ${page.syncStatus}`,
            });
        }
      });
    
    return { errors, warnings };
  }

  /**
   * Validate that all mandatory pages are completed
   */
  private validateMandatoryPages(
    requiredPages: string[],
    pageInstances: InterviewPageInstanceV2[],
  ): { errors: ValidationError[]; warnings: ValidationWarning[] } {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    const completedPageNames = new Set(
      pageInstances
        .filter(p => p.status === InterviewPageInstanceStatusEnum.COMPLETED)
        .map(p => p.pageName)
    );
    
    const pageInstanceMap = new Map(
      pageInstances.map(p => [p.pageName, p])
    );
    
    // Check for required pages that are not completed
    requiredPages.forEach(pageName => {
      if (!completedPageNames.has(pageName)) {
        const pageInstance = pageInstanceMap.get(pageName);
        
        errors.push({
          type: 'MANDATORY_PAGE_INCOMPLETE',
          pageId: pageInstance?.pageId || pageName,
          pageName,
          message: `Required page "${pageName}" is not completed`,
          pageStatus: pageInstance?.status || InterviewPageInstanceStatusEnum.PENDING,
          syncStatus: pageInstance?.syncStatus || InterviewPageSyncStatusEnum.PENDING,
        });
      }
    });
    
    // Check for completed pages that are no longer required (warnings)
    pageInstances
      .filter(p => p.status === 'completed' && !requiredPages.includes(p.pageName))
      .forEach(page => {
        warnings.push({
          type: 'PAGE_FILLED_BUT_SKIPPABLE',
          pageId: page.pageId,
          pageName: page.pageName,
          message: `Page "${page.pageName}" was completed but is now skippable due to conditional logic`,
        });
      });
    
    return { errors, warnings };
  }

  /**
   * Check for pages in transitional states that block completion
   * Note: Currently no additional transitional states beyond sync status
   */
  private validateTransitionalStates(
    pageInstances: InterviewPageInstanceV2[],
  ): { errors: ValidationError[] } {
    const errors: ValidationError[] = [];
    
    // Currently no additional transitional states to check
    // Sync-related states are handled by validateSyncStatus
    
    return { errors };
  }

  /**
   * Build detailed page information
   */
  private buildPageDetails(
    pageInstances: InterviewPageInstanceV2[],
    requiredPages: string[],
  ): PageValidationInfo[] {
    return pageInstances.map(page => ({
      pageId: page.pageId,
      pageName: page.pageName,
      status: page.status,
      syncStatus: page.syncStatus,
      isRequired: requiredPages.includes(page.pageName),
      visitOrder: page.visitOrder,
    }));
  }

  /**
   * Calculate completion percentage
   */
  private calculateCompletionPercentage(
    requiredPages: string[],
    completedPages: string[],
  ): number {
    if (requiredPages.length === 0) {
      return 100;
    }
    
    const completedRequiredPages = requiredPages.filter(pageName =>
      completedPages.includes(pageName)
    );
    
    return Math.round((completedRequiredPages.length / requiredPages.length) * 100);
  }

  /**
   * Build user-friendly summary message
   */
  private buildSummaryMessage(
    errors: ValidationError[],
    warnings: ValidationWarning[],
    completionPercentage: number,
  ): string {
    if (errors.length === 0) {
      const warningText = warnings.length > 0 
        ? ` (${warnings.length} warnings)`
        : '';
      return `Interview is ready for completion${warningText}`;
    }
    
    const errorCounts = {
      UNSYNCED_PAGE: 0,
      MANDATORY_PAGE_INCOMPLETE: 0,
      PAGE_IN_PROGRESS: 0,
      SYNC_FAILED: 0,
    };
    
    errors.forEach(error => {
      errorCounts[error.type]++;
    });
    
    const messages: string[] = [];
    
    if (errorCounts.MANDATORY_PAGE_INCOMPLETE > 0) {
      messages.push(`${errorCounts.MANDATORY_PAGE_INCOMPLETE} required page(s) not completed`);
    }
    
    if (errorCounts.UNSYNCED_PAGE > 0) {
      messages.push(`${errorCounts.UNSYNCED_PAGE} page(s) not synced`);
    }
    
    if (errorCounts.PAGE_IN_PROGRESS > 0) {
      messages.push(`${errorCounts.PAGE_IN_PROGRESS} page(s) still syncing`);
    }
    
    if (errorCounts.SYNC_FAILED > 0) {
      messages.push(`${errorCounts.SYNC_FAILED} page(s) failed to sync`);
    }
    
    return `Interview ${completionPercentage}% complete. Issues: ${messages.join(', ')}`;
  }

  /**
   * Validate document upload requirements for document_upload pages
   * Checks if required documents have been uploaded for each document upload page
   */
  private async validateDocumentUploads(
    interview: InterviewV2,
    pageInstances: InterviewPageInstanceV2[],
    requiredPages: string[],
  ): Promise<{ errors: ValidationError[]; warnings: ValidationWarning[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    try {
      // Get template to access page definitions
      const template = interview.template as unknown as InterviewTemplateV2;
      const templatePages = template.toObject().pages;
      
      // Get current document upload status
      const documentStatus = await this.documentsService.getV2DocumentUploadStatus(
        interview._id.toString()
      );
      
      // Create a map of uploaded documents by document ID
      const uploadedDocuments = new Map(
        documentStatus.uploads.map(upload => [upload.documentId, upload])
      );
      
      // Check each required page for document upload requirements
      requiredPages.forEach(pageName => {
        const pageInstance = pageInstances.find(p => p.pageName === pageName);
        if (!pageInstance) return;
        
        // Find template definition for this page
        const templatePage = templatePages.find(p => 
          p.pageName === pageName || p.pageId === pageInstance.pageId
        );
        
        // Only validate document_upload pages
        if (!templatePage || templatePage.pageType !== 'document_upload') {
          return;
        }
        
        // Check if page has upload configuration
        if (!templatePage.uploadConfig) {
          warnings.push({
            type: 'DOCUMENT_CONFIG_MISSING',
            pageId: pageInstance.pageId,
            pageName: pageInstance.pageName,
            message: `Document upload page "${pageName}" is missing upload configuration`,
          });
          return;
        }
        
        const { documentId, isRequired } = templatePage.uploadConfig;
        
        // Check if document is required and uploaded
        if (isRequired) {
          const uploadRecord = uploadedDocuments.get(documentId);
          
          if (!uploadRecord) {
            // Document is required but not uploaded
            errors.push({
              type: 'REQUIRED_DOCUMENT_MISSING',
              pageId: pageInstance.pageId,
              pageName: pageInstance.pageName,
              message: `Required document "${templatePage.uploadConfig.documentName || documentId}" has not been uploaded for page "${pageName}"`,
              pageStatus: pageInstance.status,
              syncStatus: pageInstance.syncStatus,
            });
          } else {
            // Document uploaded - could add additional validation here
            // e.g., check file size, format, processor status, etc.
            this.logger.debug(
              `Document ${documentId} uploaded successfully for page ${pageName}`,
              {
                fileName: uploadRecord.metadata?.fileName,
                processor: uploadRecord.processor,
                uploadedAt: uploadRecord.uploadedAt,
              }
            );
          }
        } else {
          // Optional document - check if uploaded and warn if missing
          const uploadRecord = uploadedDocuments.get(documentId);
          if (!uploadRecord) {
            warnings.push({
              type: 'OPTIONAL_DOCUMENT_MISSING',
              pageId: pageInstance.pageId,
              pageName: pageInstance.pageName,
              message: `Optional document "${templatePage.uploadConfig.documentName || documentId}" has not been uploaded for page "${pageName}"`,
            });
          }
        }
      });
      
      // Check for orphaned uploads (documents uploaded but page no longer required)
      documentStatus.uploads.forEach(upload => {
        const correspondingPage = pageInstances.find(p => 
          p.pageId === upload.pageId && requiredPages.includes(p.pageName)
        );
        
        if (!correspondingPage) {
          warnings.push({
            type: 'ORPHANED_DOCUMENT_UPLOAD',
            pageId: upload.pageId || 'unknown',
            pageName: upload.pageId || 'unknown',
            message: `Document "${upload.documentId}" was uploaded but its page is no longer required in the current flow`,
          });
        }
      });
      
    } catch (error) {
      this.logger.error(
        `Failed to validate document uploads for interview ${interview._id}`,
        {
          error: error.message,
          stack: error.stack,
        }
      );
      
      // Add a generic error if document validation fails
      errors.push({
        type: 'DOCUMENT_VALIDATION_FAILED',
        pageId: 'system',
        pageName: 'system',
        message: `Failed to validate document uploads: ${error.message}`,
        pageStatus: InterviewPageInstanceStatusEnum.PENDING,
        syncStatus: InterviewPageSyncStatusEnum.PENDING,
      });
    }
    
    return { errors, warnings };
  }
}