import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { ClientSession, Connection, Model } from 'mongoose';
import { QueueLogService } from 'src/queue-log/queue-log.service';
import { QueueLogStatusEnum } from 'src/queue-log/enums/queue-log-status.enum';
import { ResourceOptionsEnum } from 'src/shared/types/queue-log/queue';
import { SaveJobsDto } from 'src/queue-log/dto/save-jobs.dto';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';

// Import V2-specific constants and types
import { INTERVIEW_V2_QUEUE } from '../../../constants/interview-v2-queue.constant';
import { InterviewV2QueueJobType, InterviewV2StatusType } from '../../../types/v2/interview-v2-queue-job.enum';
import { InterviewV2Service } from '../interview-v2.service';
import { SubmitPageV2Dto } from '../../../dto/v2/submit-page-v2.dto';
import { InterviewV2AuditService } from '../audit/interview-audit.service';

// --- Type Definitions ---
export interface InterviewV2JobDataBase {
  interviewId: string;
  organisationId: string; // Required for V2 jobs
}

export interface InterviewV2JobData extends InterviewV2JobDataBase {
  submitPageDto?: SubmitPageV2Dto;
}

export interface UpdateInterviewV2PageStatusJobData extends InterviewV2JobDataBase {
  submitPageDto: SubmitPageV2Dto; // Make non-optional for this type
  pageStatus: InterviewPageStatusEnum;
}

// Context fetched during processing
interface JobV2Context {
  organisation: Organisation;
  advisor: AdvisorWithRole;
  interview: any; // V2 interview type
}

@Processor(INTERVIEW_V2_QUEUE.NAME, {
  concurrency: 3, // Higher concurrency for V2 as operations are lighter (streaming)
})
export class InterviewV2Processor extends WorkerHost {
  private readonly loggerContext = InterviewV2Processor.name;

  constructor(
    @Inject(forwardRef(() => InterviewV2Service))
    private readonly interviewV2Service: InterviewV2Service,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectConnection() private readonly connection: Connection,
    @Inject(forwardRef(() => QueueLogService))
    private readonly queueLogService: QueueLogService,
    
    private readonly auditService: InterviewV2AuditService,
  ) {
    super();
  }

  /**
   * Processes a single V2 job attempt within a transaction.
   * Throws an error on failure, letting BullMQ handle retries.
   */
  async process(
    job: Job<InterviewV2JobData | UpdateInterviewV2PageStatusJobData>,
  ): Promise<any> {
    const { interviewId } = job.data;
    const jobName = job.name;
    this.logger.log(
      `Processing V2 job ${job.id} (${jobName}), attempt ${job.attemptsMade + 1} for interview ${interviewId}`,
      this.loggerContext,
    );

    if (!interviewId) {
      // Fail fast if basic data is missing
      throw new Error(`V2 Job ${job.id}: Interview ID not provided`);
    }

    let session: ClientSession | null = null;
    try {
      session = await this.connection.startSession();
      session.startTransaction({
        readConcern: { level: 'snapshot' },
        writeConcern: { w: 'majority' },
      });

      this.logger.debug(
        `V2 Job ${job.id} (${jobName}): Transaction started for interview ${interviewId}`,
        this.loggerContext,
      );

      // 1. Fetch necessary data within the transaction
      const interview = await this.interviewV2Service.findById(
        interviewId,
        session,
      );
      if (!interview) {
        // No point retrying if interview doesn't exist
        await session.abortTransaction();
        throw new Error(
          `V2 Job ${job.id} (${jobName}): Interview not found: ${interviewId}. Aborting transaction.`,
        );
      }

      const context = await this.setupContext(interview, session);

      // 2. Execute job-specific logic
      let result: any;
      switch (jobName.toLowerCase()) {
        case InterviewV2QueueJobType.UPDATE_PAGE:
          result = await this.handleUpdatePages(
            job.data as UpdateInterviewV2PageStatusJobData,
            context,
            session,
          );
          break;
        case InterviewV2QueueJobType.UPDATE_PAGE_STATUS_V2:
          result = await this.handleUpdatePageStatusV2(
            job.data as any,
            context,
            session,
          );
          break;
        case InterviewV2QueueJobType.MARK_COMPLETE:
          result = await this.handleMarkInterviewAsComplete(context, session);
          break;
        case InterviewV2QueueJobType.SEND_NOTIFICATION:
          result = await this.handleSendNotification(context, session);
          break;
        case InterviewV2QueueJobType.PREPARE_DOCUSIGN_ENVELOPE:
          result = await this.handlePrepareDocusignEnvelope(context, session);
          break;
        // CRITICAL FIX: Add missing V2 completion job handlers
        case InterviewV2QueueJobType.COMPLETE_INTERVIEW_V2:
        case InterviewV2QueueJobType.COMPLETE_INTERVIEW_V2.toUpperCase():
          result = await this.handleCompleteInterviewV2(context, session);
          break;
        case InterviewV2QueueJobType.SEND_COMPLETION_NOTIFICATION_V2:
        case InterviewV2QueueJobType.SEND_COMPLETION_NOTIFICATION_V2.toUpperCase():
          result = await this.handleSendCompletionNotificationV2(context, session);
          break;
        default:
          // Fail fast for unsupported types
          await session.abortTransaction();
          throw new Error(`V2 Job ${job.id}: Unsupported job type: ${jobName}`);
      }

      // 3. Commit transaction
      await session.commitTransaction();
      this.logger.debug(
        `V2 Job ${job.id} (${jobName}): Transaction committed successfully for interview ${interviewId}`,
        this.loggerContext,
      );
      return result;
    } catch (error) {
      // Log the error for this attempt
      this.logger.error(
        `V2 Job ${job.id} (${jobName}): Failed attempt ${job.attemptsMade + 1} for interview ${interviewId}. Error: ${error.message}`,
        this.loggerContext,
        error.stack, // Include stack trace for detailed debugging
      );

      // Abort transaction if it's still active and session exists
      if (session?.inTransaction()) {
        try {
          await session.abortTransaction();
          this.logger.warn(
            `V2 Job ${job.id} (${jobName}): Transaction aborted due to error for interview ${interviewId}`,
            this.loggerContext,
          );
        } catch (abortError) {
          this.logger.error(
            `V2 Job ${job.id} (${jobName}): Failed to abort transaction for interview ${interviewId} after error. Abort Error: ${abortError.message}`,
            this.loggerContext,
            abortError.stack,
          );
        }
      }
      // Rethrow the error to signal failure to BullMQ, triggering retry logic
      throw error;
    } finally {
      // Ensure session is always ended
      if (session) {
        await session.endSession();
      }
    }
  }

  /**
   * Sets up the context (advisor, organisation) for the V2 job.
   * Uses interview's direct references instead of expecting populated client.
   * @param interview The V2 interview object (already fetched).
   * @param session The database session.
   * @returns JobV2Context containing advisor, organisation, and interview.
   * @throws Error if required context data is missing.
   */
  private async setupContext(
    interview: any, // V2 interview type
    session: ClientSession,
  ): Promise<JobV2Context> {
    // Use interview's direct advisor and organisation references
    if (!interview.advisor || !interview.organisationId) {
      throw new Error(
        `V2 Interview ${interview._id}: Interview context incomplete (missing advisor or organisationId).`,
      );
    }

    const advisor = await this.advisorsCrudService.findOne(
      { _id: interview.advisor },
      session,
    );
    if (!advisor) {
      throw new Error(
        `V2 Interview ${interview._id}: Advisor (${interview.advisor}) not found.`,
      );
    }

    const organisation = await this.organisationsService.findOne(
      interview.organisationId.toString(),
      session,
    );
    if (!organisation) {
      throw new Error(
        `V2 Interview ${interview._id}: Organisation (${interview.organisationId}) not found.`,
      );
    }

    return { advisor, organisation, interview };
  }

  // --- Job-Specific Handlers ---

  private async handleUpdatePages(
    data: UpdateInterviewV2PageStatusJobData,
    context: JobV2Context,
    session: ClientSession,
  ) {
    const { interviewId, submitPageDto, pageStatus } = data;
    this.logger.debug(
      `Handling ${InterviewV2QueueJobType.UPDATE_PAGE} for V2 interview ${interviewId}`,
      this.loggerContext,
    );
    if (!submitPageDto || !pageStatus) {
      throw new Error(
        `V2 Interview ${interviewId}: Missing data for UPDATE_PAGE job.`,
      );
    }

    // Update interview page status (for navigation/progress tracking)
    return this.interviewV2Service.updateInterviewPageStatus(
      interviewId,
      submitPageDto.pageName,
      pageStatus,
      session,
    );
  }

  private async handleMarkInterviewAsComplete(
    context: JobV2Context,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewV2QueueJobType.MARK_COMPLETE} for V2 interview ${interviewId}`,
      this.loggerContext,
    );
    return this.interviewV2Service.markComplete(interviewId, session);
  }

  private async handleSendNotification(
    context: JobV2Context,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewV2QueueJobType.SEND_NOTIFICATION} for V2 interview ${interviewId}`,
      this.loggerContext,
    );
    // Ensure interviewV2Service.sendNotification is idempotent
    return this.interviewV2Service.sendNotification(interviewId, InterviewV2StatusType.NOTIFICATION, session);
  }

  private async handlePrepareDocusignEnvelope(
    context: JobV2Context,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewV2QueueJobType.PREPARE_DOCUSIGN_ENVELOPE} for V2 interview ${interviewId}`,
      this.loggerContext,
    );
    // Ensure interviewV2Service.prepareDocusignEnvelope is idempotent
    return this.interviewV2Service.prepareDocusignEnvelope(interviewId, session);
  }

  private async handleUpdatePageStatusV2(
    data: any,
    context: JobV2Context,
    session: ClientSession,
  ) {
    const { interviewId, pageName, pageStatus } = data;
    this.logger.debug(
      `Handling UPDATE_PAGE_STATUS_V2 for V2 interview ${interviewId}, page ${pageName}`,
      this.loggerContext,
    );

    // Update page instance status (this will be executed after CRM sync succeeds)
    return this.interviewV2Service.updateInterviewPageStatus(
      interviewId,
      pageName,
      pageStatus, // Should be InterviewV2StatusType.SYNCED
      session,
    );
  }


  // --- Worker Event Handlers ---

  @OnWorkerEvent('completed')
  async onCompleted(job: Job<InterviewV2JobData | UpdateInterviewV2PageStatusJobData>) {
    this.logger.log(
      `V2 Job ${job.id} (${job.name}) completed successfully after ${job.attemptsMade + 1} attempts for interview ${job.data.interviewId}`,
      this.loggerContext,
    );

    // Log completion (best effort)
    const payload: SaveJobsDto = {
      jobId: job.id.toString(),
      resource: ResourceOptionsEnum.INTERVIEW,
      queueName: INTERVIEW_V2_QUEUE.NAME,
      organisationId: job.data.organisationId,
      status: QueueLogStatusEnum.COMPLETED,
      result: `V2 Job ${job.name} completed successfully.`,
    };

    try {
      await this.queueLogService.save(payload);
    } catch (logError) {
      this.logger.error(
        `V2 Job ${job.id} (${job.name}): Failed to save completion log for interview ${job.data.interviewId}. Error: ${logError.message}`,
        this.loggerContext,
        logError.stack,
      );
    }
  }

  @OnWorkerEvent('failed')
  async onFailed(
    job: Job<InterviewV2JobData | UpdateInterviewV2PageStatusJobData>,
    error: Error, // The error that caused the final failure
  ) {
    // This runs after all BullMQ retry attempts have failed
    this.logger.error(
      `V2 Job ${job.id} (${job.name}) ultimately failed after ${job.attemptsMade} attempts for interview ${job.data.interviewId}. Final Error: ${error.message}`,
      this.loggerContext,
      error.stack, // Log the final error stack
    );

    const { interviewId, organisationId } = job.data;

    // --- Best Effort: Mark interview page as FAILED (if applicable) ---
    if (
      job.name.toLowerCase() === InterviewV2QueueJobType.UPDATE_PAGE &&
      'submitPageDto' in job.data
    ) {
      const submitDto = (job.data as UpdateInterviewV2PageStatusJobData)
        .submitPageDto;
      try {
        this.logger.warn(
          `V2 Job ${job.id} (${job.name}): Attempting final action to mark interview ${interviewId} page status as FAILED.`,
          this.loggerContext,
        );
        // Run outside a transaction for simplicity in the failure handler
        await this.interviewV2Service.updateInterviewPageStatus(
          interviewId,
          submitDto.pageName,
          InterviewPageStatusEnum.FAILED,
          undefined, // No session/transaction
        );
        this.logger.warn(
          `V2 Job ${job.id} (${job.name}): Successfully marked interview ${interviewId} page status as FAILED during final failure handling.`,
          this.loggerContext,
        );
      } catch (updateError) {
        this.logger.error(
          `V2 Job ${job.id} (${job.name}): CRITICAL - Failed to mark interview ${interviewId} page status as FAILED during final failure handling. Update Error: ${updateError.message}`,
          this.loggerContext,
          updateError.stack,
        );
        // Consider adding monitoring/alerting here for manual intervention
      }
    }

    // --- Best Effort: Log the final failure ---
    const payload: SaveJobsDto = {
      jobId: job.id.toString(),
      resource: ResourceOptionsEnum.INTERVIEW,
      queueName: INTERVIEW_V2_QUEUE.NAME,
      organisationId,
      status: QueueLogStatusEnum.FAILED,
      error: `V2 Final failure after ${job.attemptsMade} attempts: ${error.message}`, // Add context to logged error
    };

    try {
      await this.queueLogService.save(payload);
    } catch (logError) {
      this.logger.error(
        `V2 Job ${job.id} (${job.name}): Failed to save final failure log for interview ${interviewId}. Log Error: ${logError.message}`,
        this.loggerContext,
        logError.stack,
      );
    }
  }

  // CRITICAL FIX: Add missing V2 completion job handlers
  private async handleCompleteInterviewV2(
    context: JobV2Context,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling COMPLETE_INTERVIEW_V2 for interview ${interviewId}`,
      this.loggerContext,
    );
    
    // Complete interview and trigger DocuSign envelope creation
    return this.interviewV2Service.completeInterview(interviewId, session);
  }

  private async handleSendCompletionNotificationV2(
    context: JobV2Context,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling SEND_COMPLETION_NOTIFICATION_V2 for interview ${interviewId}`,
      this.loggerContext,
    );
    
    // Send completion notification to advisor and client
    return this.interviewV2Service.sendCompletionNotification(interviewId, session);
  }

}