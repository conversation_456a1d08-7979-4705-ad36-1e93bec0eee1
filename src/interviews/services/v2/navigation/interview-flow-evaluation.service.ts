import { Injectable, Logger } from '@nestjs/common';
import { 
  ConditionGroup, 
  FieldCondition,
  NavigationFlowRule 
} from 'src/interviews/schemas/v2/navigation-rules.schema';
import { InterviewContext } from 'src/interviews/types/v2/client-context.interface';

export interface FlowEvaluationResult {
  targetPageName: string;
  ruleName: string;
  ruleId: string;
}

@Injectable()
export class FlowEvaluationService {
  private readonly logger = new Logger(FlowEvaluationService.name);

  /**
   * Evaluate flow rules and return the target page
   */
  async evaluateFlowRules(
    rules: NavigationFlowRule[],
    answers: Record<string, any>,
    context?: InterviewContext,
  ): Promise<FlowEvaluationResult | null> {
    // Sort by priority
    const sortedRules = [...rules]
      .filter(r => r.isActive)
      .sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
      try {
        const matches = await this.evaluateConditionGroup(rule.when, answers, context);
        
        if (matches) {
          this.logger.debug(`Rule matched: ${rule.ruleName}`);
          return {
            targetPageName: rule.goToPageName,
            ruleName: rule.ruleName,
            ruleId: rule.ruleId,
          };
        }
      } catch (error) {
        this.logger.error(`Error evaluating rule ${rule.ruleName}`, {
          error: error.message,
          rule,
        });
      }
    }

    return null;
  }

  /**
   * Evaluate a condition group
   */
  async evaluateConditionGroup(
    group: ConditionGroup,
    answers: Record<string, any>,
    context?: InterviewContext,
  ): Promise<boolean> {
    const results = await Promise.all(
      group.conditions.map(condition => 
        this.evaluateCondition(condition, answers, context)
      )
    );

    return group.logic === 'AND'
      ? results.every(r => r === true)
      : results.some(r => r === true);
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(
    condition: FieldCondition,
    answers: Record<string, any>,
    context?: InterviewContext,
  ): Promise<boolean> {
    const fieldValue = this.getFieldValue(condition.field, answers, context);

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
        
      case 'not_equals':
        return fieldValue !== condition.value;
        
      case 'contains':
        if (Array.isArray(fieldValue)) {
          return fieldValue.includes(condition.value);
        }
        return String(fieldValue).includes(String(condition.value));
        
      case 'not_contains':
        if (Array.isArray(fieldValue)) {
          return !fieldValue.includes(condition.value);
        }
        return !String(fieldValue).includes(String(condition.value));
        
      case 'in':
        return Array.isArray(condition.value) && 
               condition.value.includes(fieldValue);
        
      case 'not_in':
        return Array.isArray(condition.value) && 
               !condition.value.includes(fieldValue);
        
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
        
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
        
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
        
      case 'not_exists':
        return fieldValue === undefined || fieldValue === null;
        
      default:
        this.logger.warn(`Unknown operator: ${condition.operator}`);
        return false;
    }
  }

  /**
   * Get field value from answers or context using dot notation
   */
  private getFieldValue(field: string, answers: Record<string, any>, context?: InterviewContext): any {
    // Check if field references context variables
    if (field.startsWith('client.') && context?.client) {
      return this.getNestedValue(context.client, field.substring(7)); // Remove 'client.' prefix
    }
    
    if (field.startsWith('accounts.') && context?.accounts) {
      return this.getNestedValue(context.accounts, field.substring(9)); // Remove 'accounts.' prefix
    }
    
    if (field.startsWith('current_account.') && context?.accounts?.current_account) {
      return this.getNestedValue(context.accounts.current_account, field.substring(16)); // Remove 'current_account.' prefix
    }
    
    if (field.startsWith('organization.') && context?.client?.organization) {
      return this.getNestedValue(context.client.organization, field.substring(13)); // Remove 'organization.' prefix
    }
    
    if (field.startsWith('advisor.') && context?.client?.advisor) {
      return this.getNestedValue(context.client.advisor, field.substring(8)); // Remove 'advisor.' prefix
    }
    
    // Fall back to page answers
    return this.getNestedValue(answers, field);
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => current?.[prop], obj);
  }
}