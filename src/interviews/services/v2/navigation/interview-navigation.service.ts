// src/interviews/services/v2/interview-v2-navigation.service.ts
import {
    Injectable,
    NotFoundException,
    BadRequestException,
    Logger,
  } from '@nestjs/common';
  import { InjectModel } from '@nestjs/mongoose';
  import { Model, ClientSession } from 'mongoose';
  import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
  import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
  import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
  import { SubmitPageV2Dto } from 'src/interviews/dto/v2/submit-page-v2.dto';
  import { PageNavigationResultV2Dto } from 'src/interviews/dto/v2/page-navigation-result-v2.dto';
  import { NavigationStateV2Dto } from 'src/interviews/dto/v2/navigation-state-v2.dto';
  import { FlowEvaluationService } from 'src/interviews/services/v2/navigation/interview-flow-evaluation.service';
  import { InterviewV2QueueService } from 'src/interviews/services/v2/queue/interview-queue.service';
  import { InterviewV2AuditService } from 'src/interviews/services/v2/audit/interview-audit.service';
  import { ClientContextService } from 'src/interviews/services/v2/context/client-context.service';
  import { InterviewDocumentsService } from 'src/interviews/services/v2/documents/interview-documents.service';
import { InterviewPageInstanceStatusEnum, InterviewStatusEnum } from 'src/interviews/types/v2/interview-v2-queue-job.enum';
  import { validate } from 'class-validator';
  import { plainToClass } from 'class-transformer';
  
  // Import all page DTOs statically
  import { 
    NamePageUpdateDto,
    AddressPageUpdateDto,
    EmploymentPageUpdateDto,
    SsnPageUpdateDto,
    DobPageUpdateDto,
    PhonePageUpdateDto,
    UsCitizenPageUpdateDto,
    CompanyPageUpdateDto,
    JobPageUpdateDto,
    VipPageUpdateDto,
    ConflictsPageUpdateDto,
    BeneficiariesPageUpdateDto,
  } from 'src/shared/types/pages/dto';
  
  @Injectable()
  export class InterviewV2NavigationService {
    private readonly logger = new Logger(InterviewV2NavigationService.name);
  
    constructor(
      @InjectModel('InterviewV2')
      private readonly interviewModel: Model<InterviewV2>,
  
      @InjectModel('InterviewPageInstanceV2')
      private readonly pageInstanceModel: Model<InterviewPageInstanceV2>,
  
      private readonly flowEvaluationService: FlowEvaluationService,
      private readonly queueService: InterviewV2QueueService,
      private readonly auditService: InterviewV2AuditService,
      private readonly clientContextService: ClientContextService,
      private readonly documentsService: InterviewDocumentsService,
    ) {}
  
    /**
     * Submit page answers and get immediate navigation result
     * This is the main entry point for frontend page submissions
     */
    async submitPageAndNavigate(
      interviewId: string,
      dto: SubmitPageV2Dto,
      session?: ClientSession,
    ): Promise<PageNavigationResultV2Dto> {
      // Validate interview and page
      const interview = await this.validateInterviewAndPage(interviewId, dto.pageName);
      
      // Check if interview is sealed
      if (interview.sealed) {
        throw new BadRequestException('Cannot modify sealed interview');
      }

      // Validate page order - users can only submit the current page in the flow
      await this.validatePageOrder(interviewId, dto.pageName);

      // Validate page answers structure and required fields
      await this.validatePageAnswers(interview, dto.pageName, dto.answers);
  
      // Determine account context for smart page resolution
      const currentAccountId = await this.getCurrentAccountContext(interviewId, dto.pageName);
      
      // Get page instance (should already exist from composition)
      let pageInstance = await this.findPageInstance(
        interviewId,
        dto.pageName,
        currentAccountId,
        session
      );

      // Handle document upload if this is a document_upload page
      if (pageInstance.pageType === 'document_upload') {
        await this.handleDocumentUpload(interview, pageInstance, dto, session);
      }
  
      // Update page instance status
      const visitOrder = await this.getNextVisitOrder(interviewId);
      pageInstance = await this.updatePageInstanceStatus(
        pageInstance,
        visitOrder,
        session
      );
  
      // Compute navigation synchronously
      const navigationResult = await this.computeNavigation(
        interview,
        dto.pageName,
        dto.answers
      );
  
      // Update navigation path
      await this.updateNavigationPath(
        pageInstance,
        navigationResult,
        session
      );
      
      // If navigation indicates completion (terminal page reached), mark interview as complete
      if (navigationResult.isComplete) {
        await this.interviewModel.findByIdAndUpdate(
          interviewId,
          {
            $set: {
              isComplete: true,
              status: InterviewStatusEnum.COMPLETED,
              completedAt: new Date(),
            },
          },
          { session },
        );
        
        // Queue the completion flow (creates DocuSign envelopes, final CRM sync, etc.)
        await this.queueService.queueInterviewCompletion(interviewId);
      }
  
      // Queue CRM sync asynchronously (don't block user navigation)
      await this.queueService.queueCrmSync(interview, pageInstance, dto);
  
      // Audit page completion and navigation
      await this.auditService.logPageCompleted({
        interviewId,
        clientId: interview.client.toString(),
        organisationId: interview.organisationId.toString(),
        pageId: pageInstance.pageId,
        pageName: pageInstance.pageName,
        sessionId: dto.metadata?.sessionId,
        ipAddress: dto.metadata?.ipAddress,
        userAgent: dto.metadata?.userAgent,
      });

      // If there's navigation to next page, audit the navigation
      if (navigationResult.nextPageName) {
        await this.auditService.logPageNavigation({
          interviewId,
          clientId: interview.client.toString(),
          organisationId: interview.organisationId.toString(),
          pageId: pageInstance.pageId,
          pageName: pageInstance.pageName,
          navigatedTo: navigationResult.nextPageName,
          visitOrder,
          sessionId: dto.metadata?.sessionId,
          ipAddress: dto.metadata?.ipAddress,
          userAgent: dto.metadata?.userAgent,
        });
      }

      // If this is conditional navigation, audit the branch
      if (navigationResult.branchTaken && navigationResult.branchTaken !== 'default') {
        await this.auditService.logNavigationBranched({
          interviewId,
          clientId: interview.client.toString(),
          organisationId: interview.organisationId.toString(),
          fromPage: dto.pageName,
          toPage: navigationResult.nextPageName || 'completed',
          branchCondition: navigationResult.branchTaken,
          ruleName: navigationResult.branchTaken,
          sessionId: dto.metadata?.sessionId,
          ipAddress: dto.metadata?.ipAddress,
          userAgent: dto.metadata?.userAgent,
        });
      }
    
  
      return navigationResult;
    }
  
    /**
     * Get current navigation state for an interview
     */
    async getCurrentNavigationState(
      interviewId: string,
    ): Promise<NavigationStateV2Dto> {
      const interview = await this.interviewModel
        .findById(interviewId)
        .populate('template');
  
      if (!interview) {
        throw new NotFoundException('Interview not found');
      }
      
      // Navigation state is now built from page instances as the authoritative source
  
      // Get all visited pages
      const visitedPages = await this.pageInstanceModel
        .find({ 
          interviewId,
          visitOrder: { $gt: 0 }
        })
        .sort({ visitOrder: 1 })
        .select('pageId pageName visitOrder status navigatedTo');
  
      // Determine current page
      const lastVisited = visitedPages[visitedPages.length - 1];
      let currentPageName: string | null;
      
      if (interview.isComplete) {
        // If interview is complete, current page should be the last completed page or null
        currentPageName = lastVisited?.pageName || null;
      } else {
        // If interview is not complete, current page is where we should navigate to next
        currentPageName = lastVisited?.navigatedTo || await this.getStartPageName(interview);
      }
  
      // Check if can go back - find current page instance and get flow from template
      let canGoBack = true;
      if (currentPageName) {
        const currentPageInstance = await this.pageInstanceModel.findOne({
          interviewId,
          pageName: currentPageName
        });
        
        if (currentPageInstance) {
          const currentPageDef = await this.getPageDefinitionFromInstance(currentPageInstance, interview);
          canGoBack = currentPageDef?.flow?.allowBack ?? true;
        }
      }
  
      return {
        currentPageName,
        visitedPages: visitedPages.map(p => ({
          pageId: p.pageId,
          pageName: p.pageName,
          visitOrder: p.visitOrder,
          status: p.status,
        })),
        nextPageName: interview.isComplete ? null : currentPageName,
        isComplete: interview.isComplete,
        canGoBack: !interview.isComplete && canGoBack,
      };
    }
  
    /**
     * Navigate back to previous page
     */
    async navigateBack(
      interviewId: string,
    ): Promise<PageNavigationResultV2Dto> {
      const state = await this.getCurrentNavigationState(interviewId);
      
      if (!state.canGoBack || state.visitedPages.length < 2) {
        throw new BadRequestException('Cannot navigate back');
      }
  
      // Find the previous page
      const currentIndex = state.visitedPages.findIndex(
        p => p.pageName === state.currentPageName
      );
      
      if (currentIndex === -1) {
        // Current page not yet completed, go back to last completed page
        if (state.visitedPages.length === 0) {
          throw new BadRequestException('Already at first page');
        }
      } else if (currentIndex <= 0) {
        throw new BadRequestException('Already at first page');
      }
  
      const previousPage = currentIndex === -1 
        ? state.visitedPages[state.visitedPages.length - 1]  // Last completed page
        : state.visitedPages[currentIndex - 1];              // Previous page in sequence
  
      // Get interview for clientId and organisationId
      const interview = await this.interviewModel.findById(interviewId).select('client organisationId');
      
      // Update the navigation state by modifying the last visited page's navigatedTo field
      const lastVisitedPage = await this.pageInstanceModel.findOne({
        interviewId,
        visitOrder: Math.max(...state.visitedPages.map(p => p.visitOrder))
      });
      
      if (lastVisitedPage) {
        lastVisitedPage.navigatedTo = previousPage.pageName;
        lastVisitedPage.navigationContext = {
          branchTaken: 'back_navigation',
          timestamp: new Date(),
        };
        await lastVisitedPage.save();
      }
      
      // Audit the back navigation
      await this.auditService.logPageNavigation({
        interviewId,
        clientId: interview!.client.toString(),
        organisationId: interview!.organisationId.toString(),
        pageId: state.currentPageName!,
        pageName: state.currentPageName!,
        navigatedTo: previousPage.pageName,
        visitOrder: -1, // Indicates back navigation
        metadata: { action: 'back' },
      });

      // Also log page visited for the destination page
      await this.auditService.logPageVisited({
        interviewId,
        clientId: interview!.client.toString(),
        organisationId: interview!.organisationId.toString(),
        pageId: previousPage.pageId,
        pageName: previousPage.pageName,
        fromPage: state.currentPageName!,
      });
  
      return {
        nextPageName: previousPage.pageName,
        isComplete: false,
        navigationPath: state.visitedPages.map(p => p.pageName),
      };
    }
  
    /**
     * Get page definition for display
     */
    async getPageDefinition(
      interviewId: string,
      pageId: string,
    ): Promise<any> {
      const interview = await this.interviewModel
        .findById(interviewId)
        .populate('template');
  
      if (!interview) {
        throw new NotFoundException('Interview not found');
      }
  
      // Find page instance by pageId (all pages should exist as instances)
      const pageInstance = await this.pageInstanceModel.findOne({
        interviewId,
        pageId,
      });

      if (!pageInstance) {
        throw new NotFoundException(`Page instance ${pageId} not found in interview`);
      }

      // Get page definition using componentType
      const pageDefinition = await this.getPageDefinitionFromInstance(pageInstance, interview);
  
      // Log page visit for audit trail
      const interviewForAudit = await this.interviewModel.findById(interviewId).select('client organisationId');
      if (interviewForAudit) {
        await this.auditService.logPageVisited({
          interviewId,
          clientId: interviewForAudit.client.toString(),
          organisationId: interviewForAudit.organisationId.toString(),
          pageId,
          pageName: pageInstance.pageName,
          fromPage: undefined, // Could be enhanced to track previous page
        });
      }
      
      return {
        ...pageDefinition,
        accountContext: pageInstance.accountContext,
      };
    }
  
    // ===== Private Helper Methods =====
  
    private async validateInterviewAndPage(
      interviewId: string,
      pageName: string,
    ): Promise<InterviewV2> {
      const interview = await this.interviewModel
        .findById(interviewId)
        .populate('template');
  
      if (!interview) {
        throw new NotFoundException('Interview not found');
      }
  
      // Check if page exists in composed page instances (authoritative source for V2)
      // We just check if ANY page instance with this name exists (base or account-specific)
      const pageInstanceExists = await this.pageInstanceModel.findOne({
        interviewId,
        pageName
      });
      
      if (!pageInstanceExists) {
        throw new BadRequestException(`Page ${pageName} not found in interview`);
      }
  
      return interview;
    }
  
    private async findPageInstance(
      interviewId: string,
      pageName: string,
      accountId?: string,
      session?: ClientSession,
    ): Promise<InterviewPageInstanceV2> {
      // Build query - if accountId provided, look for account-specific page
      const query: any = { interviewId, pageName };
      if (accountId) {
        query['accountContext.accountId'] = accountId;
      } else {
        // For base pages, explicitly look for pages without account context
        query.accountContext = { $exists: false };
      }

      const pageInstance = await this.pageInstanceModel
        .findOne(query)
        .session(session);

      if (!pageInstance) {
        // Fallback: if no specific page found, try finding any page with this name
        const fallbackInstance = await this.pageInstanceModel
          .findOne({ interviewId, pageName })
          .session(session);
          
        if (!fallbackInstance) {
          throw new BadRequestException(`Page instance ${pageName} not found - all pages should be pre-created during composition`);
        }
        
        return fallbackInstance;
      }

      return pageInstance;
    }

    /**
     * Determine current account context for smart page resolution
     * This enables the same pageName to exist for multiple accounts
     */
    private async getCurrentAccountContext(interviewId: string, pageName: string): Promise<string | undefined> {
      // Check if this page name has account-specific instances
      const accountPageInstances = await this.pageInstanceModel
        .find({ 
          interviewId, 
          pageName,
          accountContext: { $exists: true }
        })
        .sort({ createdAt: 1 });

      if (accountPageInstances.length === 0) {
        // No account-specific pages, this is a base page
        return undefined;
      }

      if (accountPageInstances.length === 1) {
        // Single account page - use its account ID
        return accountPageInstances[0].accountContext?.accountId;
      }

      // Multiple account pages - need to determine which one to use
      // Strategy: Use the first unvisited account page, or the most recently visited
      const unvisitedAccountPage = accountPageInstances.find(p => p.visitOrder === -1);
      if (unvisitedAccountPage) {
        return unvisitedAccountPage.accountContext?.accountId;
      }

      // All visited - use the most recently visited
      const mostRecentlyVisited = accountPageInstances
        .filter(p => p.visitOrder > 0)
        .sort((a, b) => b.visitOrder - a.visitOrder)[0];
      
      return mostRecentlyVisited?.accountContext?.accountId;
    }
  
    private async getNextVisitOrder(interviewId: string): Promise<number> {
      const lastInstance = await this.pageInstanceModel
        .findOne({ 
          interviewId,
          visitOrder: { $gt: 0 }
        })
        .sort({ visitOrder: -1 });
  
      return (lastInstance?.visitOrder || 0) + 1;
    }
    
    /**
     * Find the next unvisited account-specific page to continue interview flow
     * Returns clean page name (no account ID embedded)
     */
    private async findNextUnvisitedAccountPage(interviewId: string): Promise<string | null> {
      const unvisitedAccountPage = await this.pageInstanceModel
        .findOne({
          interviewId,
          accountContext: { $exists: true, $ne: null },
          visitOrder: -1, // Not visited yet
        })
        .sort({ createdAt: 1 }); // Get the first account page created
        
      return unvisitedAccountPage?.pageName || null;
    }
    
    /**
     * Get page definition from instance using pageType
     */
    private async getPageDefinitionFromInstance(
      pageInstance: InterviewPageInstanceV2, 
      interview: InterviewV2
    ): Promise<any> {
      // Use pageType to find the base page definition
      const template = interview.template as unknown as InterviewTemplateV2;
      const pages = template.toObject().pages;
      
      // First try to find by pageName for exact match (handles cases where multiple pages have same pageType)
      let pageDefinition = pages.find(p => p.pageName === pageInstance.pageName);
      
      // If not found by pageName, fall back to pageType
      if (!pageDefinition) {
        pageDefinition = pages.find(p => p.pageType === pageInstance.pageType);
      }
      
      if (!pageDefinition) {
        // For account-specific pages, create a synthetic page definition
        // This handles cases where the pageType doesn't exist in base template
        this.logger.debug(`Creating synthetic page definition for account-specific pageType: ${pageInstance.pageType}`);
        
        pageDefinition = {
          pageId: pageInstance.pageId,
          pageName: pageInstance.pageName,
          pageType: pageInstance.pageType,
          pageTitle: `Account-specific: ${pageInstance.pageType}`,
          isActive: true,
          isRequired: true,
          flow: {
            rules: [],
            defaultNext: null,
            allowBack: true,
            isTerminal: true // Account pages are often terminal
          }
        };
      }
      
      return pageDefinition;
    }
  
    private async updatePageInstanceStatus(
      pageInstance: InterviewPageInstanceV2,
      visitOrder: number,
      session?: ClientSession,
    ): Promise<InterviewPageInstanceV2> {
      pageInstance.visitOrder = visitOrder;
      pageInstance.status = InterviewPageInstanceStatusEnum.COMPLETED;
      
      await pageInstance.save({ session });
      return pageInstance;
    }
  
    private async computeNavigation(
      interview: InterviewV2,
      currentPageName: string,
      answers: Record<string, any>,
    ): Promise<PageNavigationResultV2Dto> {
      // Get the current page instance which has complete information
      const currentPageInstance = await this.pageInstanceModel.findOne({
        interviewId: interview._id,
        pageName: currentPageName,
      });

      if (!currentPageInstance) {
        throw new NotFoundException('Current page instance not found');
      }

      // Get the page definition from instances (handles both base and account-specific pages)
      const currentPage = await this.getPageDefinitionFromInstance(currentPageInstance, interview);

      // Build interview context for conditional evaluation
      let context;
      try {
        // Get current account ID directly from page instance
        const currentAccountId = currentPageInstance.accountContext?.accountId;
        
        // Log account context availability for debugging
        if (currentAccountId) {
          this.logger.debug(`Processing account-specific page ${currentPageName} for account ${currentAccountId}`);
        }
        
        // Build context using client service (no direct schema access)
        context = await this.clientContextService.buildInterviewContext(
          interview.client.toString(),
          interview.organisationId.toString(),
          currentAccountId
        );
      } catch (error) {
        this.logger.warn('Failed to build interview context for conditional evaluation', {
          interviewId: interview._id,
          error: error.message,
        });
      }
  
      // Helper function to validate and return pageName using page instances
      const validatePageName = async (pageName: string | null): Promise<string | null> => {
        if (!pageName) return null;
        const targetPageInstance = await this.pageInstanceModel.findOne({
          interviewId: interview._id,
          pageName
        });
        return targetPageInstance ? pageName : null;
      };
  
      // Check if this is a terminal page, but first check for unvisited account pages
      if (currentPage.flow.isTerminal) {
        // Even on terminal pages, check if there are unvisited account-specific pages
        const accountPageName = await this.findNextUnvisitedAccountPage(interview._id);
        if (accountPageName) {
          return {
            nextPageName: accountPageName,
            isComplete: false,
            branchTaken: 'account_flow',
          };
        }
        
        // No account pages left, truly terminal
        return {
          nextPageName: null,
          isComplete: true,
        };
      }
  
      // Skip evaluation for skip conditions
      if (currentPage.flow.skipIf) {
        const shouldSkip = await this.flowEvaluationService.evaluateConditionGroup(
          currentPage.flow.skipIf,
          answers,
          context
        );
  
        if (shouldSkip) {
          // Skip to default next
          return {
            nextPageName: await validatePageName(currentPage.flow.defaultNext?.pageName),
            isComplete: false,
            branchTaken: 'skipped',
          };
        }
      }
  
      // Evaluate navigation rules
      let nextPageName: string | null = null;
      let branchTaken: string | undefined;
  
      if (currentPage.flow.rules && currentPage.flow.rules.length > 0) {
        const evaluationResult = await this.flowEvaluationService.evaluateFlowRules(
          currentPage.flow.rules,
          answers,
          context
        );
  
        if (evaluationResult) {
          nextPageName = await validatePageName(evaluationResult.targetPageName);
          branchTaken = evaluationResult.ruleName;
        }
      }
  
      // Use default next if no rules matched
      if (!nextPageName && currentPage.flow.defaultNext) {
        nextPageName = await validatePageName(currentPage.flow.defaultNext.pageName);
        branchTaken = 'default';
      }
      
      // If still no next page, check for unvisited account-specific pages
      if (!nextPageName) {
        nextPageName = await this.findNextUnvisitedAccountPage(interview._id);
        if (nextPageName) {
          branchTaken = 'account_flow';
        }
      }
  
      // Check if next page exists and if we're complete
      const isComplete = !nextPageName;
  
      return {
        nextPageName,
        isComplete,
        branchTaken,
      };
    }
  
    private async updateNavigationPath(
      pageInstance: InterviewPageInstanceV2,
      navigationResult: PageNavigationResultV2Dto,
      session?: ClientSession,
    ): Promise<void> {
      pageInstance.navigatedTo = navigationResult.nextPageName || undefined;
      pageInstance.navigationContext = {
        branchTaken: navigationResult.branchTaken,
        timestamp: new Date(),
      };
  
      await pageInstance.save({ session });
    }
  
  
    private async getStartPageName(interview: InterviewV2): Promise<string | undefined> {
      // Find the first page instance based on visitOrder (should be the start page)
      const firstPageInstance = await this.pageInstanceModel
        .findOne({ 
          interviewId: interview._id,
          visitOrder: { $gt: 0 } // Only visited pages
        })
        .sort({ visitOrder: 1 });

      if (firstPageInstance) {
        return firstPageInstance.pageName;
      }

      // For unvisited interviews, find the page with lowest defaultOrder from template
      const template = interview.template as unknown as InterviewTemplateV2;
      const templatePages = template.toObject().pages;
      const sortedPages = templatePages
        .filter(p => p.isActive)
        .sort((a, b) => a.defaultOrder - b.defaultOrder);
      
      if (sortedPages.length > 0) {
        const startPageName = sortedPages[0].pageName;
        
        // Verify this page instance exists
        const pageInstance = await this.pageInstanceModel.findOne({
          interviewId: interview._id,
          pageName: startPageName
        });
        
        if (pageInstance) {
          return startPageName;
        }
      }

      // Final fallback: find any page instance without accountContext (base page)
      const firstBasePageInstance = await this.pageInstanceModel
        .findOne({ 
          interviewId: interview._id,
          accountContext: { $exists: false }
        })
        .sort({ createdAt: 1 });

      return firstBasePageInstance?.pageName;
    }
  
    /**
     * Validate that the submitted page is the current page in the flow
     * Prevents users from skipping pages or submitting out of order
     */
    private async validatePageOrder(interviewId: string, submittedPageName: string): Promise<void> {
      const currentState = await this.getCurrentNavigationState(interviewId);
      
      // Allow resubmitting the current page or any previously visited page
      const isCurrentPage = currentState.currentPageName === submittedPageName;
      const isPreviouslyVisited = currentState.visitedPages.some(p => p.pageName === submittedPageName);
      
      if (!isCurrentPage && !isPreviouslyVisited) {
        throw new BadRequestException(
          `Cannot submit page '${submittedPageName}'. Current page is '${currentState.currentPageName}'. ` +
          `You can only submit the current page or go back to previously visited pages.`
        );
      }
    }

    /**
     * Validate page answers using existing DTOs with class-validator
     * This leverages our existing validation infrastructure for maintainability
     */
    private async validatePageAnswers(interview: InterviewV2, pageName: string, answers: Record<string, any>): Promise<void> {
      // Determine account context for smart page resolution
      const currentAccountId = await this.getCurrentAccountContext(interview._id.toString(), pageName);
      
      // Find the page instance to get the pageType (authoritative source for V2)
      const pageInstance = await this.findPageInstance(
        interview._id.toString(),
        pageName,
        currentAccountId
      );
      
      // Use pageType for validation - this is clean and robust
      const pageType = pageInstance.pageType;

      // Map page types to their corresponding DTOs using static imports
      let DtoClass: any = null;
      let transformedAnswers: any = null;

      try {
        switch (pageType) {
          case 'name':
            DtoClass = NamePageUpdateDto;
            transformedAnswers = plainToClass(NamePageUpdateDto, answers);
            break;
            
          case 'address':
            DtoClass = AddressPageUpdateDto;
            transformedAnswers = plainToClass(AddressPageUpdateDto, answers);
            break;
            
          case 'employment':
            DtoClass = EmploymentPageUpdateDto;
            transformedAnswers = plainToClass(EmploymentPageUpdateDto, answers);
            break;
            
          case 'ssn':
            DtoClass = SsnPageUpdateDto;
            transformedAnswers = plainToClass(SsnPageUpdateDto, answers);
            break;
            
          case 'dob':
            DtoClass = DobPageUpdateDto;
            transformedAnswers = plainToClass(DobPageUpdateDto, answers);
            break;
            
          case 'phone':
            DtoClass = PhonePageUpdateDto;
            transformedAnswers = plainToClass(PhonePageUpdateDto, answers);
            break;
            
          case 'us_citizen':
            DtoClass = UsCitizenPageUpdateDto;
            transformedAnswers = plainToClass(UsCitizenPageUpdateDto, answers);
            break;
            
          case 'company':
            DtoClass = CompanyPageUpdateDto;
            transformedAnswers = plainToClass(CompanyPageUpdateDto, answers);
            break;
            
          case 'job':
            DtoClass = JobPageUpdateDto;
            transformedAnswers = plainToClass(JobPageUpdateDto, answers);
            break;
            
          case 'vip':
            DtoClass = VipPageUpdateDto;
            transformedAnswers = plainToClass(VipPageUpdateDto, answers);
            break;
            
          case 'conflicts-of-interest':
          case 'conflict_of_interest':
            DtoClass = ConflictsPageUpdateDto;
            transformedAnswers = plainToClass(ConflictsPageUpdateDto, answers);
            break;

          // Account-specific page types - use proper DTOs
          case 'primary_beneficiaries':
          case 'contingent_beneficiaries':
          case 'beneficiaries':
            DtoClass = BeneficiariesPageUpdateDto;
            transformedAnswers = plainToClass(BeneficiariesPageUpdateDto, answers);
            break;
            
          case 'document_upload':
            // Document upload pages don't require traditional answer validation
            // They require file upload validation which is handled in handleDocumentUpload
            this.logger.debug(`Document upload page ${pageName} - validation handled separately`);
            return;
            
          default:
            // For unknown page types, just ensure answers is not empty
            if (!answers || Object.keys(answers).length === 0) {
              throw new BadRequestException(`Page ${pageName} (${pageType}) requires answers but none were provided`);
            }
            this.logger.debug(`Using basic validation for unknown page type: ${pageType}`);
            return;
        }

        // Perform validation using class-validator
        if (DtoClass && transformedAnswers) {
          const validationErrors = await validate(transformedAnswers);
          
          if (validationErrors.length > 0) {
            const errorMessages = this.extractValidationErrorMessages(validationErrors);
            throw new BadRequestException(`Validation failed for ${pageName} (${pageType}) page: ${errorMessages}`);
          }
        }
      } catch (error) {
        if (error instanceof BadRequestException) {
          throw error;
        }
        // If validation fails unexpectedly, fall back to basic validation
        this.logger.warn(`Failed to validate page type ${pageType}, falling back to basic validation: ${error.message}`);
        if (!answers || Object.keys(answers).length === 0) {
          throw new BadRequestException(`Page ${pageName} (${pageType}) requires answers but none were provided`);
        }
      }
    }

    /**
     * Handle document upload for document_upload pages
     */
    private async handleDocumentUpload(
      interview: InterviewV2,
      pageInstance: InterviewPageInstanceV2,
      dto: SubmitPageV2Dto,
      session?: ClientSession
    ): Promise<void> {
      // Get page definition directly from populated template
      const template = interview.template as any;
      const pageDefinition = template.pages?.find(
        (page: any) => page.pageName === pageInstance.pageName
      );

      if (!pageDefinition || pageDefinition.pageType !== 'document_upload') {
        throw new BadRequestException('Page is not a document upload page');
      }

      // Check if a document has already been uploaded for this page
      const existingUpload = interview.documentUploads?.find(
        (upload: any) => upload.pageId === pageDefinition.pageId
      );

      // If document is already uploaded, we're good to go
      if (existingUpload) {
        this.logger.log(`Document already uploaded for page ${pageInstance.pageName} (${existingUpload.documentId})`);
        return;
      }

      // Check if upload is required
      if (pageDefinition.uploadConfig?.isRequired && !dto.uploadedFile) {
        throw new BadRequestException('Document upload is required for this page');
      }

      // Process upload if file provided
      if (dto.uploadedFile) {
        this.logger.log(`Processing document upload for page ${pageInstance.pageName} in interview ${interview._id}`);
        
        await this.documentsService.processV2DocumentUpload(
          interview._id.toString(),
          pageDefinition,
          dto.uploadedFile,
          session
        );

        this.logger.log(`Successfully processed document upload for ${pageDefinition.uploadConfig?.documentId}`);
      }
    }

    /**
     * Get page definition from interview template
     */
    private async getPageDefinitionFromTemplate(
      templateId: string,
      pageName: string
    ): Promise<any> {
      // templateId is actually the interview template ID, not interview ID
      // Use the template model directly
      const templateModel = this.interviewModel.db.model('InterviewTemplateV2');
      
      const template = await templateModel.findById(templateId);
      if (!template) {
        throw new NotFoundException('Interview template not found');
      }

      // Find the page in the template
      const pageDefinition = template.pages?.find(
        (page: any) => page.pageName === pageName
      );

      return pageDefinition;
    }

    /**
     * Extract validation error messages, including nested validation errors
     */
    private extractValidationErrorMessages(validationErrors: any[]): string {
      const messages: string[] = [];
      
      for (const error of validationErrors) {
        // Direct constraints on the field
        if (error.constraints) {
          messages.push(...Object.values(error.constraints).map(String));
        }
        
        // Nested validation errors (for ValidateNested fields)
        if (error.children && error.children.length > 0) {
          const nestedMessages = this.extractValidationErrorMessages(error.children);
          if (nestedMessages) {
            messages.push(`${error.property}: ${nestedMessages}`);
          }
        }
      }
      
      return messages.join(', ');
    }

  }