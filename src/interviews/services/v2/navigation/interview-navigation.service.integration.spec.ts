import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { InterviewV2NavigationService } from './interview-navigation.service';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { FlowEvaluationService } from './interview-flow-evaluation.service';
import { InterviewV2QueueService } from '../queue/interview-queue.service';
import { InterviewV2AuditService } from '../audit/interview-audit.service';
import { ClientContextService } from '../context/client-context.service';
import { SubmitPageV2Dto } from 'src/interviews/dto/v2/submit-page-v2.dto';
import { PageNavigationResultV2Dto } from 'src/interviews/dto/v2/page-navigation-result-v2.dto';
import { NavigationStateV2Dto } from 'src/interviews/dto/v2/navigation-state-v2.dto';
import { InterviewPageInstanceStatusEnum, InterviewStatusEnum } from 'src/interviews/types/v2/interview-v2-queue-job.enum';

/**
 * Integration Tests for Complex Navigation Scenarios
 * 
 * These tests cover the most complex real-world navigation flows including:
 * 1. Multi-account navigation with conditional branching
 * 2. Complex conditional navigation with skip conditions
 * 3. Back navigation with restrictions
 * 4. Terminal page handling with pending account pages
 * 5. Error scenarios and edge cases
 */
describe('InterviewV2NavigationService - Complex Integration Scenarios', () => {
  let service: InterviewV2NavigationService;
  let interviewModel: jest.Mocked<Model<InterviewV2>>;
  let pageInstanceModel: jest.Mocked<Model<InterviewPageInstanceV2>>;
  let flowEvaluationService: jest.Mocked<FlowEvaluationService>;
  let queueService: jest.Mocked<InterviewV2QueueService>;
  let auditService: jest.Mocked<InterviewV2AuditService>;
  let clientContextService: jest.Mocked<ClientContextService>;

  // Mock data factories
  const createMockInterview = (overrides: Partial<any> = {}): any => ({
    _id: 'interview-123',
    client: 'client-123',
    organisationId: 'org-123',
    advisor: 'advisor-123',
    status: InterviewStatusEnum.PENDING,
    isComplete: false,
    sealed: false,
    template: {
      _id: 'template-123',
      pages: [],
      toObject: () => ({ pages: [] })
    },
    ...overrides,
  });

  const createMockPageInstance = (overrides: Partial<any> = {}): any => ({
    _id: 'page-instance-123',
    interviewId: 'interview-123',
    pageId: 'page-123',
    pageName: 'test_page',
    pageType: 'test',
    visitOrder: -1,
    status: InterviewPageInstanceStatusEnum.PENDING,
    navigatedTo: undefined,
    navigationContext: undefined,
    accountContext: undefined,
    save: jest.fn().mockResolvedValue(true),
    ...overrides,
  });

  const createMockPageDefinition = (overrides: Partial<any> = {}): any => ({
    pageId: 'page-123',
    pageName: 'test_page',
    pageType: 'test',
    pageTitle: 'Test Page',
    isActive: true,
    isRequired: true,
    flow: {
      rules: [],
      defaultNext: null,
      allowBack: true,
      isTerminal: false,
      skipIf: undefined,
    },
    ...overrides,
  });

  // Mock complete ClientContext and AccountContext
  const createMockClientContext = (overrides: Partial<any> = {}) => ({
    status: {
      isExistingClient: false,
      skipContactInterview: false,
      readyToSend: false,
      featuresSelected: false,
    },
    features: {
      docusignSelected: false,
      sendAdv2b: false,
      doClientProfiling: false,
    },
    contact: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      hasCrmId: false,
    },
    advisor: {
      firstName: 'Jane',
      lastName: 'Advisor',
      tier: undefined,
    },
    organization: {
      id: 'org-123',
      tier: undefined,
    },
    ...overrides,
  });

  const createMockAccountContext = (overrides: Partial<any> = {}) => ({
    count: 0,
    types: [],
    hasRetirement: false,
    hasJoint: false,
    hasIndividual: false,
    hasTrust: false,
    hasBusiness: false,
    current_account: undefined,
    ...overrides,
  });

  // Enhanced helper functions for creating chainable query mocks that properly support all method chains
  const createChainableMock = (finalResult: any) => {
    const chainMock = {
      find: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      sort: jest.fn(),
      select: jest.fn(),
      populate: jest.fn(),
      session: jest.fn(),
      exec: jest.fn(),
      
      // Promise methods for direct awaiting
      then: jest.fn(),
      catch: jest.fn(),
      finally: jest.fn(),
    };
    
    // Make all query methods return the same chainable object
    Object.keys(chainMock).forEach(key => {
      if (typeof chainMock[key] === 'function' && 
          !['exec', 'then', 'catch', 'finally'].includes(key)) {
        chainMock[key].mockReturnValue(chainMock);
      }
    });
    
    // Configure exec to return a resolved promise
    chainMock.exec.mockResolvedValue(finalResult);
    
    // Configure promise methods to handle direct awaiting
    const promise = Promise.resolve(finalResult);
    chainMock.then.mockImplementation((onResolve, onReject) => promise.then(onResolve, onReject));
    chainMock.catch.mockImplementation((onReject) => promise.catch(onReject));
    chainMock.finally.mockImplementation((onFinally) => promise.finally?.(onFinally) || promise);
    
    return chainMock;
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewV2NavigationService,
        {
          provide: getModelToken('InterviewV2'),
          useValue: {
            findById: jest.fn(),
            findByIdAndUpdate: jest.fn(),
          },
        },
        {
          provide: getModelToken('InterviewPageInstanceV2'),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: FlowEvaluationService,
          useValue: {
            evaluateConditionGroup: jest.fn(),
            evaluateFlowRules: jest.fn(),
          },
        },
        {
          provide: InterviewV2QueueService,
          useValue: {
            queueInterviewCompletion: jest.fn(),
            queueCrmSync: jest.fn(),
          },
        },
        {
          provide: InterviewV2AuditService,
          useValue: {
            logPageCompleted: jest.fn(),
            logPageNavigation: jest.fn(),
            logNavigationBranched: jest.fn(),
            logPageVisited: jest.fn(),
          },
        },
        {
          provide: ClientContextService,
          useValue: {
            buildInterviewContext: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InterviewV2NavigationService>(InterviewV2NavigationService);
    interviewModel = module.get(getModelToken('InterviewV2'));
    pageInstanceModel = module.get(getModelToken('InterviewPageInstanceV2'));
    flowEvaluationService = module.get(FlowEvaluationService);
    queueService = module.get(InterviewV2QueueService);
    auditService = module.get(InterviewV2AuditService);
    clientContextService = module.get(ClientContextService);
  });

  describe('Complex Multi-Account Navigation Flow', () => {
    it('should handle navigation through multiple account-specific pages with conditional branches', async () => {
      // Setup: Interview with multiple accounts and complex conditional flow
      const interview = createMockInterview({
        template: {
          _id: 'template-123',
          pages: [
            createMockPageDefinition({
              pageName: 'us_citizen',
              pageType: 'us_citizen',
              flow: {
                rules: [
                  {
                    ruleId: 'rule-123',
                    name: 'non_citizen_flow',
                    condition: { field: 'isCitizen', operator: 'equals', value: false },
                    target: { pageName: 'non_citizen_docs' }
                  }
                ],
                defaultNext: { pageName: 'account_beneficiaries' },
                allowBack: true,
              }
            }),
            createMockPageDefinition({
              pageName: 'account_beneficiaries',
              pageType: 'beneficiaries',
              flow: {
                rules: [],
                defaultNext: null,
                allowBack: true,
                isTerminal: true,
              }
            })
          ],
          toObject: () => ({
            pages: [
              createMockPageDefinition({
                pageName: 'us_citizen',
                pageType: 'us_citizen',
                flow: {
                  rules: [
                    {
                      ruleId: 'rule-123',
                      name: 'non_citizen_flow',
                      condition: { field: 'isCitizen', operator: 'equals', value: false },
                      target: { pageName: 'non_citizen_docs' }
                    }
                  ],
                  defaultNext: { pageName: 'account_beneficiaries' },
                  allowBack: true,
                }
              }),
              createMockPageDefinition({
                pageName: 'account_beneficiaries',
                pageType: 'beneficiaries',
                flow: {
                  rules: [],
                  defaultNext: null,
                  allowBack: true,
                  isTerminal: true,
                }
              })
            ]
          })
        }
      });

      // Mock current page (us_citizen)
      const currentPageInstance = createMockPageInstance({
        pageName: 'us_citizen',
        pageType: 'us_citizen',
        visitOrder: -1,
      });

      // Mock first account beneficiaries page
      const account1BeneficiariesPage = createMockPageInstance({
        pageName: 'account_beneficiaries',
        pageType: 'beneficiaries',
        visitOrder: -1,
        accountContext: { accountId: 'account-1' }
      });

      // Mock second account beneficiaries page
      const account2BeneficiariesPage = createMockPageInstance({
        pageName: 'account_beneficiaries',
        pageType: 'beneficiaries',
        visitOrder: -1,
        accountContext: { accountId: 'account-2' }
      });

      // Setup mocks
      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // validateInterviewAndPage
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // findPageInstance
        .mockReturnValueOnce(createChainableMock(account1BeneficiariesPage) as any) // validatePageName in computeNavigation
        .mockReturnValueOnce(createChainableMock(account1BeneficiariesPage) as any) // findNextUnvisitedAccountPage
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find
        .mockReturnValueOnce(createChainableMock([]) as any) // getNextVisitOrder
        .mockReturnValueOnce(createChainableMock([account1BeneficiariesPage, account2BeneficiariesPage]) as any); // getCurrentAccountContext

      // Mock flow evaluation - citizen should go to account beneficiaries
      flowEvaluationService.evaluateFlowRules.mockResolvedValue({
        targetPageName: 'account_beneficiaries',
        ruleName: 'default',
        ruleId: 'rule-default'
      });

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext({ 
          contact: { ...createMockClientContext().contact }
        }),
        accounts: createMockAccountContext({
          count: 2,
          types: ['ira', 'roth_ira'],
          hasRetirement: true
        })
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'us_citizen',
        answers: { isCitizen: true },
        metadata: { sessionId: 'session-123' }
      };

      // Execute
      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      // Verify
      expect(result).toEqual({
        nextPageName: 'account_beneficiaries',
        isComplete: false,
        branchTaken: 'default',
      });

      expect(queueService.queueCrmSync).toHaveBeenCalledWith(interview, currentPageInstance, submitDto);
      expect(auditService.logPageCompleted).toHaveBeenCalled();
      expect(auditService.logPageNavigation).toHaveBeenCalled();
    });

    it('should complete interview when all account pages are visited but handle remaining unvisited account pages', async () => {
      const interview = createMockInterview({
        template: {
          pages: [
            createMockPageDefinition({
              pageName: 'account_beneficiaries',
              pageType: 'beneficiaries',
              flow: { isTerminal: true, allowBack: true }
            })
          ],
          toObject: () => ({
            pages: [
              createMockPageDefinition({
                pageName: 'account_beneficiaries',
                pageType: 'beneficiaries',
                flow: { isTerminal: true, allowBack: true }
              })
            ]
          })
        }
      });

      const currentPageInstance = createMockPageInstance({
        pageName: 'account_beneficiaries',
        pageType: 'beneficiaries',
        accountContext: { accountId: 'account-1' }
      });

      const unvisitedAccountPage = createMockPageInstance({
        pageName: 'account_beneficiaries',
        pageType: 'beneficiaries',
        visitOrder: -1, // Not visited yet
        accountContext: { accountId: 'account-2' }
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // validateInterviewAndPage
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // findPageInstance  
        .mockReturnValueOnce(createChainableMock(unvisitedAccountPage) as any) // findNextUnvisitedAccountPage
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext(),
        accounts: createMockAccountContext()
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'account_beneficiaries',
        answers: { beneficiaries: [] }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      // Should navigate to next unvisited account page, not complete
      expect(result).toEqual({
        nextPageName: 'account_beneficiaries',
        isComplete: false,
        branchTaken: 'account_flow',
      });

      expect(interviewModel.findByIdAndUpdate).not.toHaveBeenCalled();
      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
    });
  });

  describe('Complex Conditional Navigation with Skip Conditions', () => {
    it('should handle skip conditions and conditional branching in combination', async () => {
      const interview = createMockInterview({
        template: {
          pages: [
            createMockPageDefinition({
              pageName: 'employment',
              pageType: 'employment',
              flow: {
                skipIf: {
                  operator: 'and',
                  conditions: [
                    { field: 'isRetired', operator: 'equals', value: true }
                  ]
                },
                rules: [
                  {
                    ruleId: 'rule-456',
                    name: 'self_employed_flow',
                    condition: { field: 'employmentType', operator: 'equals', value: 'self_employed' },
                    target: { pageName: 'business_details' }
                  }
                ],
                defaultNext: { pageName: 'income_details' },
                allowBack: true,
              }
            })
          ],
          toObject: () => ({
            pages: [
              createMockPageDefinition({
                pageName: 'employment',
                pageType: 'employment',
                flow: {
                  skipIf: {
                    operator: 'and',
                    conditions: [
                      { field: 'isRetired', operator: 'equals', value: true }
                    ]
                  },
                  rules: [
                    {
                      ruleId: 'rule-456',
                      name: 'self_employed_flow',
                      condition: { field: 'employmentType', operator: 'equals', value: 'self_employed' },
                      target: { pageName: 'business_details' }
                    }
                  ],
                  defaultNext: { pageName: 'income_details' },
                  allowBack: true,
                }
              })
            ]
          })
        }
      });

      const currentPageInstance = createMockPageInstance({
        pageName: 'employment',
        pageType: 'employment',
      });

      const targetPageInstance = createMockPageInstance({
        pageName: 'income_details',
        pageType: 'income',
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // validateInterviewAndPage
        .mockReturnValueOnce(createChainableMock(currentPageInstance) as any) // findPageInstance
        .mockReturnValueOnce(createChainableMock(targetPageInstance) as any) // validatePageName
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      // Mock skip condition evaluation - should skip this page
      flowEvaluationService.evaluateConditionGroup.mockResolvedValue(true);

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext(),
        accounts: createMockAccountContext()
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'employment',
        answers: { isRetired: true }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      expect(result).toEqual({
        nextPageName: 'income_details',
        isComplete: false,
        branchTaken: 'skipped',
      });

      expect(flowEvaluationService.evaluateConditionGroup).toHaveBeenCalledWith(
        expect.objectContaining({
          operator: 'and',
          conditions: [{ field: 'isRetired', operator: 'equals', value: true }]
        }),
        { isRetired: true },
        expect.any(Object)
      );
    });

    it('should evaluate complex conditional rules when skip conditions are not met', async () => {
      const interview = createMockInterview({
        template: {
          pages: [
            createMockPageDefinition({
              pageName: 'employment',
              pageType: 'employment',
              flow: {
                skipIf: {
                  operator: 'and',
                  conditions: [
                    { field: 'isRetired', operator: 'equals', value: true }
                  ]
                },
                rules: [
                  {
                    ruleId: 'rule-789',
                    name: 'self_employed_flow',
                    condition: { field: 'employmentType', operator: 'equals', value: 'self_employed' },
                    target: { pageName: 'business_details' }
                  },
                  {
                    ruleId: 'rule-790',
                    name: 'unemployed_flow',
                    condition: { field: 'employmentType', operator: 'equals', value: 'unemployed' },
                    target: { pageName: 'unemployment_benefits' }
                  }
                ],
                defaultNext: { pageName: 'income_details' },
                allowBack: true,
              }
            })
          ],
          toObject: () => ({
            pages: [
              createMockPageDefinition({
                pageName: 'employment',
                pageType: 'employment',
                flow: {
                  skipIf: {
                    operator: 'and',
                    conditions: [
                      { field: 'isRetired', operator: 'equals', value: true }
                    ]
                  },
                  rules: [
                    {
                      ruleId: 'rule-789',
                      name: 'self_employed_flow',
                      condition: { field: 'employmentType', operator: 'equals', value: 'self_employed' },
                      target: { pageName: 'business_details' }
                    },
                    {
                      ruleId: 'rule-790',
                      name: 'unemployed_flow',
                      condition: { field: 'employmentType', operator: 'equals', value: 'unemployed' },
                      target: { pageName: 'unemployment_benefits' }
                    }
                  ],
                  defaultNext: { pageName: 'income_details' },
                  allowBack: true,
                }
              })
            ]
          })
        }
      });

      const currentPageInstance = createMockPageInstance({
        pageName: 'employment',
        pageType: 'employment',
      });

      const targetPageInstance = createMockPageInstance({
        pageName: 'business_details',
        pageType: 'business',
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(currentPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(currentPageInstance) // findPageInstance
        .mockResolvedValueOnce(targetPageInstance) // validatePageName
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      // Mock skip condition evaluation - should NOT skip
      flowEvaluationService.evaluateConditionGroup.mockResolvedValue(false);
      
      // Mock flow rules evaluation - should match self-employed rule
      flowEvaluationService.evaluateFlowRules.mockResolvedValue({
        targetPageName: 'business_details',
        ruleName: 'self_employed_flow',
        ruleId: 'rule-789'
      });

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext(),
        accounts: createMockAccountContext()
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'employment',
        answers: { 
          isRetired: false,
          employmentType: 'self_employed',
          businessName: 'My Business'
        }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      expect(result).toEqual({
        nextPageName: 'business_details',
        isComplete: false,
        branchTaken: 'self_employed_flow',
      });

      expect(auditService.logNavigationBranched).toHaveBeenCalledWith({
        interviewId: 'interview-123',
        clientId: 'client-123',
        organisationId: 'org-123',
        fromPage: 'employment',
        toPage: 'business_details',
        branchCondition: 'self_employed_flow',
        ruleName: 'self_employed_flow',
        sessionId: undefined,
        ipAddress: undefined,
        userAgent: undefined,
      });
    });
  });

  describe('Back Navigation with Flow Restrictions', () => {
    it('should handle back navigation when allowBack is true', async () => {
      const interview = createMockInterview();

      const visitedPages = [
        createMockPageInstance({
          pageName: 'us_citizen',
          pageType: 'us_citizen',
          visitOrder: 1,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        }),
        createMockPageInstance({
          pageName: 'name',
          pageType: 'name',
          visitOrder: 2,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        }),
        createMockPageInstance({
          pageName: 'address',
          pageType: 'address',
          visitOrder: 3,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        })
      ];

      const currentPageDefinition = createMockPageDefinition({
        pageName: 'address',
        pageType: 'address',
        flow: { allowBack: true }
      });

      interviewModel.findById
        .mockReturnValueOnce(createChainableMock(interview) as any)
        .mockResolvedValueOnce(interview); // For audit

      pageInstanceModel.find
        .mockReturnValueOnce(createChainableMock(visitedPages) as any) // getCurrentNavigationState
        .mockReturnValueOnce(createChainableMock(visitedPages) as any); // getCurrentNavigationState again

      pageInstanceModel.findOne
        .mockResolvedValueOnce(visitedPages[2]) // Current page instance
        .mockResolvedValueOnce(visitedPages[2]); // For updating navigation

      // Mock template with page definition
      interview.template.toObject = () => ({
        pages: [currentPageDefinition]
      });

      const result = await service.navigateBack('interview-123');

      expect(result).toEqual({
        nextPageName: 'name',
        isComplete: false,
        navigationPath: ['us_citizen', 'name', 'address'],
      });

      expect(auditService.logPageNavigation).toHaveBeenCalledWith({
        interviewId: 'interview-123',
        clientId: 'client-123',
        organisationId: 'org-123',
        pageId: 'address',
        pageName: 'address',
        navigatedTo: 'name',
        visitOrder: -1,
        metadata: { action: 'back' },
      });
    });

    it('should prevent back navigation when allowBack is false', async () => {
      const interview = createMockInterview();

      const visitedPages = [
        createMockPageInstance({
          pageName: 'us_citizen',
          pageType: 'us_citizen',
          visitOrder: 1,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        }),
        createMockPageInstance({
          pageName: 'critical_info',
          pageType: 'critical',
          visitOrder: 2,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        })
      ];

      const currentPageDefinition = createMockPageDefinition({
        pageName: 'critical_info',
        pageType: 'critical',
        flow: { allowBack: false } // Back navigation not allowed
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.find.mockReturnValue(createChainableMock(visitedPages) as any);

      pageInstanceModel.findOne.mockReturnValue(createChainableMock(visitedPages[1]) as any);

      interview.template.toObject = () => ({
        pages: [currentPageDefinition]
      });

      await expect(service.navigateBack('interview-123')).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('Terminal Page Handling with Complex Scenarios', () => {
    it('should complete interview when terminal page is reached and no account pages remain', async () => {
      const interview = createMockInterview();

      const currentPageInstance = createMockPageInstance({
        pageName: 'final_confirmation',
        pageType: 'confirmation',
      });

      const terminalPageDefinition = createMockPageDefinition({
        pageName: 'final_confirmation',
        pageType: 'confirmation',
        flow: {
          isTerminal: true,
          allowBack: true,
        }
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(currentPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(currentPageInstance) // findPageInstance
        .mockResolvedValueOnce(null) // findNextUnvisitedAccountPage - no more account pages
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find
        .mockReturnValueOnce(createChainableMock([]) as any) // getNextVisitOrder
        .mockReturnValueOnce(createChainableMock([]) as any); // getCurrentAccountContext - no account pages

      interview.template.toObject = () => ({
        pages: [terminalPageDefinition]
      });

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext(),
        accounts: createMockAccountContext()
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'final_confirmation',
        answers: { confirmed: true }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      expect(result).toEqual({
        nextPageName: null,
        isComplete: true,
      });

      expect(interviewModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'interview-123',
        {
          $set: {
            isComplete: true,
            status: InterviewStatusEnum.COMPLETED,
            completedAt: expect.any(Date),
          },
        },
        { session: undefined },
      );

      expect(queueService.queueInterviewCompletion).toHaveBeenCalledWith('interview-123');
    });

    it('should continue to account pages even from terminal pages when account pages remain unvisited', async () => {
      const interview = createMockInterview();

      const currentPageInstance = createMockPageInstance({
        pageName: 'primary_contact_complete',
        pageType: 'completion',
      });

      const unvisitedAccountPage = createMockPageInstance({
        pageName: 'account_beneficiaries',
        pageType: 'beneficiaries',
        visitOrder: -1,
        accountContext: { accountId: 'account-1' }
      });

      const terminalPageDefinition = createMockPageDefinition({
        pageName: 'primary_contact_complete',
        pageType: 'completion',
        flow: {
          isTerminal: true,
          allowBack: false,
        }
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(currentPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(currentPageInstance) // findPageInstance
        .mockResolvedValueOnce(unvisitedAccountPage) // findNextUnvisitedAccountPage
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      interview.template.toObject = () => ({
        pages: [terminalPageDefinition]
      });

      clientContextService.buildInterviewContext.mockResolvedValue({
        client: createMockClientContext(),
        accounts: createMockAccountContext({
          count: 1,
          types: ['ira'],
          hasRetirement: true
        })
      });

      const submitDto: SubmitPageV2Dto = {
        pageName: 'primary_contact_complete',
        answers: { confirmCompletion: true }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      expect(result).toEqual({
        nextPageName: 'account_beneficiaries',
        isComplete: false,
        branchTaken: 'account_flow',
      });

      // Interview should NOT be marked complete
      expect(interviewModel.findByIdAndUpdate).not.toHaveBeenCalled();
      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
    });
  });

  describe('Error Scenarios and Edge Cases', () => {
    it('should handle sealed interview attempts gracefully', async () => {
      const sealedInterview = createMockInterview({
        sealed: true
      });

      const currentPageInstance = createMockPageInstance({
        pageName: 'name',
        pageType: 'name',
      });

      interviewModel.findById.mockReturnValue(createChainableMock(sealedInterview) as any);

      pageInstanceModel.findOne.mockReturnValue(createChainableMock(currentPageInstance) as any);

      const submitDto: SubmitPageV2Dto = {
        pageName: 'name',
        answers: { firstName: 'John', lastName: 'Doe' }
      };

      await expect(service.submitPageAndNavigate('interview-123', submitDto))
        .rejects.toThrow('Cannot modify sealed interview');
    });

    it('should handle page order validation failures', async () => {
      const interview = createMockInterview();

      const visitedPages = [
        createMockPageInstance({
          pageName: 'us_citizen',
          pageType: 'us_citizen',
          visitOrder: 1,
          status: InterviewPageInstanceStatusEnum.COMPLETED,
        })
      ];

      interviewModel.findById
        .mockReturnValueOnce(createChainableMock(interview) as any)
        .mockReturnValueOnce(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(visitedPages[0])
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock(visitedPages) as any);

      const submitDto: SubmitPageV2Dto = {
        pageName: 'address', // Trying to skip 'name' page
        answers: { street: '123 Main St' }
      };

      await expect(service.submitPageAndNavigate('interview-123', submitDto))
        .rejects.toThrow(BadRequestException);
    });

    it('should handle missing page instances gracefully', async () => {
      const interview = createMockInterview();

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne.mockReturnValue(createChainableMock(null) as any); // No page instance found

      const submitDto: SubmitPageV2Dto = {
        pageName: 'nonexistent_page',
        answers: {}
      };

      await expect(service.submitPageAndNavigate('interview-123', submitDto))
        .rejects.toThrow('Page nonexistent_page not found in interview');
    });

    it('should handle flow evaluation service failures gracefully', async () => {
      const interview = createMockInterview({
        template: {
          pages: [
            createMockPageDefinition({
              pageName: 'complex_flow',
              pageType: 'complex',
              flow: {
                rules: [
                  {
                    ruleId: 'rule-complex',
                    name: 'complex_rule',
                    condition: { field: 'complexField', operator: 'complex_operation', value: 'complex_value' },
                    target: { pageName: 'target_page' }
                  }
                ],
                defaultNext: { pageName: 'default_page' },
              }
            })
          ],
          toObject: () => ({
            pages: [
              createMockPageDefinition({
                pageName: 'complex_flow',
                pageType: 'complex',
                flow: {
                  rules: [
                    {
                      ruleId: 'rule-complex',
                      name: 'complex_rule',
                      condition: { field: 'complexField', operator: 'complex_operation', value: 'complex_value' },
                      target: { pageName: 'target_page' }
                    }
                  ],
                  defaultNext: { pageName: 'default_page' },
                }
              })
            ]
          })
        }
      });

      const currentPageInstance = createMockPageInstance({
        pageName: 'complex_flow',
        pageType: 'complex',
      });

      const defaultPageInstance = createMockPageInstance({
        pageName: 'default_page',
        pageType: 'default',
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(currentPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(currentPageInstance) // findPageInstance
        .mockResolvedValueOnce(defaultPageInstance) // validatePageName for default
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      // Mock flow evaluation failure
      flowEvaluationService.evaluateFlowRules.mockRejectedValue(
        new Error('Flow evaluation failed')
      );

      clientContextService.buildInterviewContext.mockRejectedValue(
        new Error('Context building failed')
      );

      const submitDto: SubmitPageV2Dto = {
        pageName: 'complex_flow',
        answers: { complexField: 'complex_value' }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      // Should fall back to default navigation
      expect(result).toEqual({
        nextPageName: 'default_page',
        isComplete: false,
        branchTaken: 'default',
      });
    });

    it('should handle context building failures gracefully', async () => {
      const interview = createMockInterview({
        template: {
          pages: [createMockPageDefinition()],
          toObject: () => ({ pages: [createMockPageDefinition()] })
        }
      });

      const currentPageInstance = createMockPageInstance();

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(currentPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(currentPageInstance) // findPageInstance
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      // Mock context service failure
      clientContextService.buildInterviewContext.mockRejectedValue(
        new Error('Failed to build context')
      );

      // Flow evaluation should proceed without context
      flowEvaluationService.evaluateFlowRules.mockResolvedValue(null);

      const submitDto: SubmitPageV2Dto = {
        pageName: 'test_page',
        answers: { test: 'value' }
      };

      const result = await service.submitPageAndNavigate('interview-123', submitDto);

      // Should handle gracefully and not throw
      expect(result).toBeDefined();
      expect(result.isComplete).toBe(false);
    });
  });

  describe('Page Validation Complex Scenarios', () => {
    it('should validate complex page answers with nested validation errors', async () => {
      const interview = createMockInterview();
      
      const beneficiariesPageInstance = createMockPageInstance({
        pageName: 'beneficiaries',
        pageType: 'beneficiaries',
      });

      interviewModel.findById.mockReturnValue(createChainableMock(interview) as any);

      pageInstanceModel.findOne
        .mockResolvedValueOnce(beneficiariesPageInstance) // validateInterviewAndPage
        .mockResolvedValueOnce(beneficiariesPageInstance) // findPageInstance
        .mockReturnValueOnce(createChainableMock(null) as any); // getStartPageName

      pageInstanceModel.find.mockReturnValue(createChainableMock([]) as any);

      // Submit invalid beneficiaries data
      const submitDto: SubmitPageV2Dto = {
        pageName: 'beneficiaries',
        answers: {
          beneficiaries: [
            {
              // Missing required fields
              firstName: '',
              lastName: '',
              relationship: '',
              percentage: 150 // Invalid percentage > 100
            }
          ]
        }
      };

      await expect(service.submitPageAndNavigate('interview-123', submitDto))
        .rejects.toThrow(BadRequestException);
    });
  });
}); 