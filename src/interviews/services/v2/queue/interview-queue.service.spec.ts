import { Test, TestingModule } from '@nestjs/testing';
import { InterviewV2QueueService, PageSubmissionData } from './interview-queue.service';
import { FlowProducer, Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { INTERVIEW_V2_QUEUE } from 'src/interviews/constants/interview-v2-queue.constant';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { ClientsSharedService } from 'src/clients/services/shared/clients-shared.service';
import { CRMEnum } from 'src/shared/types/integrations';

jest.mock('bullmq');

describe('InterviewV2QueueService', () => {
  let service: InterviewV2QueueService;
  let interviewQueue: jest.Mocked<Queue>;
  let clientQueue: jest.Mocked<Queue>;
  let organisationsService: jest.Mocked<OrganisationsService>;
  let clientsSharedService: jest.Mocked<ClientsSharedService>;
  let flowProducer: jest.Mocked<FlowProducer>;

  const mockFlowProducer = {
    add: jest.fn(),
    getJob: jest.fn(),
    getJobs: jest.fn(),
    addBulk: jest.fn(),
  };

  const mockInterviewQueue = {
    add: jest.fn(),
    getJob: jest.fn(),
    getJobs: jest.fn(),
    opts: { connection: { host: 'localhost', port: 6379 } },
  };

  const mockClientQueue = {
    add: jest.fn(),
    getJob: jest.fn(),
    getJobs: jest.fn(),
  };

  const mockOrganisationsService = {
    findOne: jest.fn(),
  };

  const mockClientsSharedService = {
    validateClientExists: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    // Mock FlowProducer constructor
    (FlowProducer as jest.MockedClass<typeof FlowProducer>).mockImplementation(() => mockFlowProducer as any);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewV2QueueService,
        {
          provide: getQueueToken(INTERVIEW_V2_QUEUE.NAME),
          useValue: mockInterviewQueue,
        },
        {
          provide: getQueueToken(CLIENT_QUEUE.NAME),
          useValue: mockClientQueue,
        },
        {
          provide: OrganisationsService,
          useValue: mockOrganisationsService,
        },
        {
          provide: ClientsSharedService,
          useValue: mockClientsSharedService,
        },
      ],
    }).compile();

    service = module.get<InterviewV2QueueService>(InterviewV2QueueService);
  });

  describe('FlowProducer initialization', () => {
    it('should initialize FlowProducer with correct connection options', () => {
      expect(FlowProducer).toHaveBeenCalledWith({
        connection: expect.objectContaining({
          host: 'localhost',
          port: 6379,
        }),
      });
    });
  });

  describe('queuePageSubmission', () => {
    const mockPageSubmissionData: PageSubmissionData = {
      interviewId: 'interview-123',
      pageInstanceId: 'page-instance-123',
      pageId: 'page-123',
      pageName: 'name',
      answers: { name: 'John Doe' },
      clientId: 'client-123',
      contactType: 'primary' as const,
      metadata: { sessionId: 'session-123' },
    };

    it('should queue page submission successfully', async () => {
      const mockOrganisation = {
        _id: 'org-123',
        selectedCRM: CRMEnum.Redtail,
      };
      
      mockClientsSharedService.validateClientExists.mockResolvedValue({
        organisationId: 'org-123',
      } as any);
      mockOrganisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mockInterviewQueue.add.mockResolvedValue(undefined as any);

      await service.queuePageSubmission(mockPageSubmissionData);

      expect(mockClientsSharedService.validateClientExists).toHaveBeenCalledWith('client-123');
      expect(mockOrganisationsService.findOne).toHaveBeenCalledWith('org-123');
      expect(mockInterviewQueue.add).toHaveBeenCalledWith(
        'SYNC_PAGE_TO_CRM_V2',
        expect.objectContaining({
          ...mockPageSubmissionData,
          organisationId: 'org-123',
          crmType: CRMEnum.Redtail,
        }),
        expect.objectContaining({
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        }),
      );
    });

    it('should handle missing CRM configuration gracefully', async () => {
      const mockOrganisation = {
        _id: 'org-123',
        selectedCRM: null,
      };
      
      mockClientsSharedService.validateClientExists.mockResolvedValue({
        organisationId: 'org-123',
      } as any);
      mockOrganisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      // Should not throw error, just log warning
      await expect(service.queuePageSubmission(mockPageSubmissionData)).resolves.toBeUndefined();
      expect(mockInterviewQueue.add).not.toHaveBeenCalled();
    });

    it('should handle queue error gracefully', async () => {
      const mockOrganisation = {
        _id: 'org-123',
        selectedCRM: CRMEnum.Redtail,
      };
      
      mockClientsSharedService.validateClientExists.mockResolvedValue({
        organisationId: 'org-123',
      } as any);
      mockOrganisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mockInterviewQueue.add.mockRejectedValue(new Error('Queue error'));

      // Should not throw error, just log it
      await expect(service.queuePageSubmission(mockPageSubmissionData)).resolves.toBeUndefined();
    });
  });

  describe('queueInterviewCompletion', () => {
    const mockInterviewId = 'interview-123';

    it('should queue interview completion flow successfully', async () => {
      mockFlowProducer.add.mockResolvedValue(undefined as any);

      await service.queueInterviewCompletion(mockInterviewId);

      expect(mockFlowProducer.add).toHaveBeenCalledWith({
        name: 'SEND_COMPLETION_NOTIFICATION_V2',
        queueName: INTERVIEW_V2_QUEUE.NAME,
        data: { interviewId: mockInterviewId },
        children: [
          {
            name: 'COMPLETE_INTERVIEW_V2',
            queueName: INTERVIEW_V2_QUEUE.NAME,
            data: { interviewId: mockInterviewId },
            opts: {
              attempts: 3,
            },
          },
        ],
        opts: expect.objectContaining({
          attempts: 3,
          delay: 2000,
        }),
      });
    });

    it('should handle flow producer error gracefully', async () => {
      const error = new Error('Flow producer error');
      mockFlowProducer.add.mockRejectedValue(error);

      // Should not throw error, just log it
      await expect(service.queueInterviewCompletion(mockInterviewId)).resolves.toBeUndefined();
    });
  });

  describe('queueCrmSync', () => {
    const mockJobData = {
      interviewId: 'interview-123',
      pageId: 'page-123',
      pageType: 'name',
      answers: { name: 'John Doe' },
      contactType: 'primary' as const,
      clientId: 'client-123',
      organisationId: 'org-123',
      advisorId: 'advisor-123',
    };

    it('should log that V2 CRM sync is bypassed', async () => {
      // This method is disabled and only logs
      await service.queueCrmSync(mockJobData);
      
      // No assertions needed as method only logs
      expect(true).toBe(true);
    });
  });

  describe('getCrmQueueName', () => {
    it('should return correct queue name for Redtail', () => {
      const result = service.getCrmQueueName(CRMEnum.Redtail);
      expect(result).toBe('redtail');
    });

    it('should return correct queue name for Wealthbox', () => {
      const result = service.getCrmQueueName(CRMEnum.Wealthbox);
      expect(result).toBe('wealthbox');
    });

    it('should return correct queue name for Salesforce', () => {
      const result = service.getCrmQueueName(CRMEnum.Salesforce);
      expect(result).toBe('salesforce');
    });

    it('should return correct queue name for Practifi', () => {
      const result = service.getCrmQueueName(CRMEnum.Practifi);
      expect(result).toBe('practifi');
    });

    it('should throw error for unsupported CRM type', () => {
      expect(() => service.getCrmQueueName('UNSUPPORTED' as any)).toThrow('Unsupported CRM type: UNSUPPORTED');
    });
  });

  describe('service initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have all required methods', () => {
      expect(service).toHaveProperty('queuePageSubmission');
      expect(service).toHaveProperty('queueInterviewCompletion');
      expect(service).toHaveProperty('queueCrmSync');
      expect(service).toHaveProperty('getCrmQueueName');
    });
  });
});