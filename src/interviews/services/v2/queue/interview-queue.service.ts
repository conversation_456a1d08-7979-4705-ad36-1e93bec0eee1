// src/interviews/services/v2/interview-v2-queue.service.ts
import { 
  Injectable, 
  Inject, 
  forwardRef, 
  Logger 
} from '@nestjs/common';
import { FlowProducer, Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { INTERVIEW_V2_QUEUE } from 'src/interviews/constants/interview-v2-queue.constant';
import { InterviewV2QueueJobType, InterviewV2StatusType } from 'src/interviews/types/v2/interview-v2-queue-job.enum';
import { CRM_JOBS, CRM_QUEUE } from 'src/integrations/crm/constants/crm.constants';
import { CRMEnum } from 'src/shared/types/integrations';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { SubmitPageV2Dto } from 'src/interviews/dto/v2/submit-page-v2.dto';



@Injectable()
export class InterviewV2QueueService {
  private readonly logger = new Logger(InterviewV2QueueService.name);
  private readonly flowProducer: FlowProducer;
  
  constructor(
    @InjectQueue(INTERVIEW_V2_QUEUE.NAME) 
    private readonly interviewQueue: Queue,
    
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
  ) {
    this.flowProducer = new FlowProducer({ 
      connection: this.interviewQueue.opts.connection 
    });
  }


  /**
   * Queue interview completion tasks
   */
  async queueInterviewCompletion(interviewId: string): Promise<void> {
    const jobId = `complete-${interviewId}-${Date.now()}`;

    try {
      // CRITICAL FIX: Swap parent/child order - children execute FIRST in BullMQ
      await this.flowProducer.add({
        name: InterviewV2QueueJobType.SEND_COMPLETION_NOTIFICATION_V2,
        queueName: INTERVIEW_V2_QUEUE.NAME,
        data: { interviewId },
        children: [
          {
            name: InterviewV2QueueJobType.COMPLETE_INTERVIEW_V2,
            queueName: INTERVIEW_V2_QUEUE.NAME,
            data: { interviewId },
            opts: {
              attempts: 3,
            },
          },
        ],
        opts: {
          jobId,
          attempts: 3,
          delay: 2000, // Delay notification to run after completion
        },
      });

      this.logger.log(`Queued interview completion: ${jobId}`);
    } catch (error) {
      this.logger.error('Failed to queue interview completion', {
        error: error.message,
        interviewId,
      });
    }
  }


  /**
   * Queue CRM sync for V2 interviews
   * Uses FlowProducer to create parent-child job relationship
   */
  async queueCrmSync(
    interview: InterviewV2,
    pageInstance: InterviewPageInstanceV2,
    dto: SubmitPageV2Dto,
  ): Promise<void> {
    try {
      // Use pageType from page instance - clean and robust
      const pageType = pageInstance.pageType;

      // Skip CRM sync for document upload pages - they don't have data to sync
      if (pageType === 'document_upload') {
        this.logger.debug(`Skipping CRM sync for document upload page ${dto.pageName}`);
        return;
      }

      // Get organisation to determine CRM type
      const organisation = await this.organisationsService.findOne(
        interview.organisationId.toString(),
      );

      if (!organisation?.selectedCRM) {
        this.logger.warn('No CRM configured for organisation');
        return;
      }
      
      const organisationId = organisation._id.toString();
      const crmQueueName = this.getCrmQueueName(organisation.selectedCRM);

      await this.flowProducer.add({
        name: InterviewV2QueueJobType.UPDATE_PAGE_STATUS_V2,
        data: {
          interviewId: interview._id.toString(),
          pageName: dto.pageName,
          pageStatus: InterviewV2StatusType.SYNCED,
          organisationId,
        },
        queueName: INTERVIEW_V2_QUEUE.NAME,
        children: [
          {
            name: CRM_JOBS.SYNC_PAGE_V2,
            data: {
              interviewId: interview._id.toString(),
              pageInstanceId: pageInstance._id.toString(),
              pageName: dto.pageName, // Unique identifier for CRM mapping
              pageType: pageType, // Page type for CRM mapping
              answers: dto.answers,
              clientId: interview.client.toString(),
              contactType: interview.contactType,
              accountId: pageInstance.accountContext?.accountId,
              metadata: dto.metadata,
              organisationId,
              advisorId: interview.advisor.toString(),
            },
            queueName: crmQueueName,
            opts: {
              failParentOnFailure: true,
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
            },
          },
        ],
        opts: {
          failParentOnFailure: true,
          attempts: 3,
          delay: 300,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      });

      this.logger.debug(`Queued V2 CRM sync flow for interview ${interview._id}, page ${dto.pageName} (${pageType})`);
    } catch (error) {
      this.logger.error('Failed to queue V2 CRM sync', {
        error: error.message,
        interviewId: interview._id.toString(),
        pageName: dto.pageName,
      });
      // Don't throw - CRM sync failure shouldn't block user navigation
    }
  }

  /**
   * Get CRM queue name for routing
   * More maintainable approach using the CRM_QUEUE constant structure
   */
  private getCrmQueueName(selectedCrm: CRMEnum): string {
    // Convert CRMEnum value to uppercase to match CRM_QUEUE keys
    const crmKey = selectedCrm.toUpperCase() as keyof typeof CRM_QUEUE;
    
    // Check if the CRM exists in our queue configuration
    const crmQueueConfig = CRM_QUEUE[crmKey];
    
    if (!crmQueueConfig) {
      // List available CRMs for better error messaging
      const availableCRMs = Object.keys(CRM_QUEUE).join(', ');
      throw new Error(
        `Unsupported CRM type: ${selectedCrm}. Available CRMs: ${availableCRMs}`
      );
    }

    return crmQueueConfig.NAME;
  }
}