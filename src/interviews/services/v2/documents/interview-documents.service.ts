import {
  Injectable,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import { Model, ClientSession, Connection } from 'mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { InterviewV2 } from '../../../schemas/v2/interview.schema';
import { InterviewPageDefV2 } from 'src/interview-templates/schemas/v2/interview-page-def.schema';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { InterviewEnvelopeService } from '../envelope/interview-envelope.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { UploadDocumentDto } from '../../../dto/v1/upload-document.dto';
import { RequiredDocumentDto } from '../../../dto/v1/required-document.dto';
import { Asset } from 'src/shared/schemas/asset.schema';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { Account } from 'src/clients/schemas/clients.schema';
import { isEmpty } from 'lodash';
import AWS from 'aws-sdk';
import { ConfigService } from '@nestjs/config';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountClientDocumentsEnum, AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { CacheService } from 'src/shared/services/cache.service';
import { getFileCacheKey, DOWNLOAD_PATH } from 'src/utils/cache/cache.util';
import { createWriteStream, existsSync } from 'fs';
import { mkdirAsync, unlinkAsync } from 'src/utils/file/file.util';
import path from 'path';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { computeAccountFileSuffix } from 'src/utils/accounts/accountFileNameSuffix';

export interface DocumentUploadResult {
  success: boolean;
  documentId: string;
  processor: string;
  uploadedAt: Date;
  metadata?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
}

@Injectable()
export class InterviewDocumentsService {
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(Asset.name) private readonly assetModel: Model<Asset>,
    @InjectModel(Interview.name) private readonly interviewModel: Model<Interview>,
    @InjectModel(InterviewV2.name) private readonly interviewV2Model: Model<InterviewV2>,
    @Inject(forwardRef(() => ClientsV2Service)) private readonly clientsService: ClientsV2Service,
    @Inject(forwardRef(() => DocusignService)) private readonly docusignService: DocusignService,
    @Inject(forwardRef(() => OrganisationsService)) private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => InterviewEnvelopeService)) private readonly interviewEnvelopeService: InterviewEnvelopeService,
    @Inject(forwardRef(() => MailService)) private readonly mailService: MailService,
    @InjectConnection() private readonly connection: Connection,
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
    private readonly cacheService: CacheService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async upload(
    interview: Interview,
    files: Express.Multer.File[],
    dto?: UploadDocumentDto,
    session?: ClientSession,
  ): Promise<string> {
    let transactionToUse = session;
    let shouldManageTransaction = false;

    if (!session) {
      transactionToUse = await this.connection.startSession();
      await transactionToUse.startTransaction();
      shouldManageTransaction = true;
    }

    try {
      const envelopeId = interview.envelopeId;
      const { client } = interview as any;
      const clientId = client._id.toString();

      const envelopeExists = !!envelopeId;
      if (!envelopeExists) {
        throw new HttpException('Cannot upload to non-existent envelope', HttpStatus.BAD_REQUEST);
      }

      const { organisationId, primaryAdvisor } = await this.clientsService.findOne({ _id: clientId }, transactionToUse);
      const entityIds = { organisationId: organisationId.toString(), advisorId: primaryAdvisor.id.toString() };

      await this.docusignService.addClientDocuments({ ...entityIds, envelopeId, files });

      const { documentName, accountId, feature } = dto || {};

      const query: any = { _id: interview._id, 'documents.name': documentName };
      const filter: any = { 'elem.name': documentName };

      if (!isEmpty(accountId)) {
        query['documents.accountId'] = accountId;
        filter['elem.accountId'] = accountId;
      }
      if (!isEmpty(feature)) {
        query['documents.feature'] = feature;
        filter['elem.feature'] = feature;
      }

      if (!!documentName && documentName.length > 0) {
        const { fileUploadsNo } = await this.clientsService.findOne({ _id: clientId }, transactionToUse);
        const now = new Date();
        const interviewUpdatePromise = this.interviewModel.updateOne(
          query,
          { $set: { 'documents.$[elem].updatedAt': now } },
          { arrayFilters: [filter], session: transactionToUse },
        );
        const clientUpdatePromise = this.clientsService.update(clientId, { 
          fileUploadsNo: fileUploadsNo + 1,
          status: ClientStatusEnum.Sent
        }, transactionToUse);
        await Promise.all([interviewUpdatePromise, clientUpdatePromise]);
      }

      this.clientQueue.add(ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP, {
        clientId,
        isPrimary: interview.isPrimary,
      });

      if (shouldManageTransaction) await transactionToUse.commitTransaction();
      return 'Document successfully uploaded.';
    } catch (error) {
      if (shouldManageTransaction) await transactionToUse.abortTransaction();
      throw new HttpException(error.message, error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      if (shouldManageTransaction) await transactionToUse.endSession();
    }
  }

  selectClientFilesToFetch(accountFeatures: AccountFeaturesEnum[], accountId: string) {
    const fileTypes = new Set<{ documentType: AccountClientDocumentsEnum; accountId: string; feature: AccountFeaturesEnum }>();
    accountFeatures.forEach(feature => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          fileTypes.add({ documentType: AccountClientDocumentsEnum.CancelledCheck, accountId, feature });
          break;
        case AccountFeaturesEnum.Acat:
          fileTypes.add({ documentType: AccountClientDocumentsEnum.BrokerageStatement, accountId, feature });
          break;
        case AccountFeaturesEnum.IraDistribution:
          break;
        default:
          throw new HttpException(`Account opening files not found for account feature ${feature}`, HttpStatus.NOT_FOUND);
      }
    });
    return [...fileTypes];
  }

  getAccountAdvisoryFileTypes(accountType: string, accountFeatures: AccountFeaturesEnum[]): AccountAdvisoryDocumentsEnum[] {
    const advisoryFileTypes: Set<AccountAdvisoryDocumentsEnum> = new Set();
    const isIraAccount = [ 'ira', 'roth' ].includes(accountType);
    const isBrokerageAccount = [ 'brokerage', 'joint' ].includes(accountType);
    if (isIraAccount) advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication);
    if (isBrokerageAccount) advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabOnePersonalAccountApplication);
    accountFeatures.forEach(feature => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabMoneyLinkElectronicTransferForm);
          break;
        case AccountFeaturesEnum.Acat:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabTransferAccountForm);
          break;
        case AccountFeaturesEnum.IraDistribution:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabIraDistributionForm);
          break;
        default:
          throw new HttpException(`Account opening files not found for account feature ${feature}`, HttpStatus.NOT_FOUND);
      }
    });
    return [...advisoryFileTypes];
  }

  async addRequiredDocument(interviewId: string, dto: RequiredDocumentDto): Promise<any> {
    return this.interviewModel.updateOne(
      { _id: interviewId },
      {
        $push: {
          documents: {
            name: dto.document,
            updatedAt: null,
          },
        },
      },
    );
  }

  async removeRequiredDocument(interviewId: string, dto: RequiredDocumentDto): Promise<any> {
    const interview = await this.interviewModel.findById(interviewId);
    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }
    
    return this.interviewModel.updateOne(
      { _id: interviewId },
      {
        $set: {
          documents: interview.documents.filter(
            (document) => document.name !== dto.document,
          ),
        },
      },
    );
  }

  async getAccountOpeningAdvisoryFiles(
    accounts: Account[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    const allRequiredFiles: Express.Multer.File[] = [];

    for (const account of accounts) {
      const { type, features, label } = account;
      const accountAdvisoryFileTypes = this.getAccountAdvisoryFileTypes(
        type,
        features,
      );

      if (!isEmpty(accountAdvisoryFileTypes)) {
        const accountOpeningFiles: Express.Multer.File[] =
          await this.getFilesFromS3(accountAdvisoryFileTypes, organisationId);

        accountOpeningFiles.forEach((accountOpeningFile) => {
          const filename = accountOpeningFile.originalname.replace('.pdf', '');
          const accountSuffix = computeAccountFileSuffix(label);

          accountOpeningFile.originalname = `${filename}${accountSuffix}.pdf`;
        });

        allRequiredFiles.push(...accountOpeningFiles);
      }
    }

    return allRequiredFiles;
  }

  async getFilesFromS3(
    fileNames: AccountAdvisoryDocumentsEnum[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    try {
      const s3 = new AWS.S3();

      const promises = fileNames.map<Promise<Express.Multer.File>>(
        async (document) => {
          let cacheKey: string;
          let advisoryAgreementAsset: Asset;
          let assetId: string;

          const isAdvisoryAgreement =
            document === AccountAdvisoryDocumentsEnum.AdvisoryAgreement;

          if (isAdvisoryAgreement && organisationId) {
            const organisation = await this.organisationsService.findOne(
              organisationId,
            );
            advisoryAgreementAsset = organisation.assets.find(
              (asset: Asset) =>
                asset.assetType === AssetTypeEnum.AdvisoryAgreement,
            );
            assetId = advisoryAgreementAsset?.assetId;
            cacheKey = getFileCacheKey(document, organisationId);
          } else {
            const asset = await this.assetModel
              .findOne({
                assetId: document,
                assetType: AssetTypeEnum.Document,
              })
              .exec();
            assetId = asset?.assetId;
            cacheKey = getFileCacheKey(document);
          }

          if (this.cacheService.has(cacheKey)) {
            let file: Express.Multer.File;

            try {
              file = await this.cacheService.get(cacheKey);
              this.logger.info(`File fetched from cache: ${cacheKey}`);
            } catch (error) {
              this.logger.error(
                `Error fetching file from cache: ${cacheKey}`,
                error,
              );
            }

            if (file) {
              return file;
            }
          } else {
            this.logger.info(`File not found in cache: ${cacheKey}`);
          }

          const params = {
            Bucket: this.configService.get<string>('ORG_DOCS_BUCKET_NAME'),
            Key: isAdvisoryAgreement
              ? advisoryAgreementAsset.assetKey
              : `global/${assetId}`,
          };

          const directoryPath = path.join(__dirname, DOWNLOAD_PATH);
          const filePath = path.join(directoryPath, `${cacheKey}.pdf`);

          if (!existsSync(directoryPath)) {
            await mkdirAsync(directoryPath, { recursive: true });
          }

          return new Promise((resolve, reject) => {
            const fileWriteStream = createWriteStream(filePath);

            fileWriteStream.on('finish', () => {
              const file: Express.Multer.File = {
                fieldname: 'files',
                originalname: `${document}.pdf`,
                encoding: '7bit',
                mimetype: 'application/pdf',
                path: filePath,
              } as Express.Multer.File;
              this.logger.info(`File downloaded: ${filePath} setting cache`);
              this.cacheService.set(cacheKey, file);
              resolve(file);
            });

            fileWriteStream.on('error', async (writeStreamError) => {
              this.logger.error(
                `Error writing file to disk: ${filePath}`,
                writeStreamError,
              );
              if (existsSync(filePath)) {
                try {
                  await unlinkAsync(filePath);
                } catch (cleanupError) {
                  this.logger.error(
                    `Error cleaning up file: ${filePath}`,
                    cleanupError,
                  );
                }
              }
              reject(writeStreamError);
            });

            s3.getObject(params)
              .createReadStream()
              .on('error', (readStreamError) => reject(readStreamError))
              .pipe(fileWriteStream);
          });
        },
      );
      return Promise.all(promises);
    } catch (error) {
      throw new HttpException(
        `Error fetching files from S3: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ================== V2 DOCUMENT UPLOAD METHODS ==================

  /**
   * Process V2 document upload based on page configuration
   */
  async processV2DocumentUpload(
    interviewId: string,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    // Validate page type
    if (pageDefinition.pageType !== 'document_upload') {
      throw new HttpException('Page is not a document upload page', HttpStatus.BAD_REQUEST);
    }

    // Validate upload configuration exists
    if (!pageDefinition.uploadConfig) {
      throw new HttpException('Page does not have upload configuration', HttpStatus.BAD_REQUEST);
    }

    const interview = await this.getV2Interview(interviewId, session);
    
    // Validate file against page configuration
    this.validateUploadFile(file, pageDefinition.uploadConfig);

    let result: DocumentUploadResult;

    // Route to appropriate processor
    switch (pageDefinition.uploadConfig.processor.type) {
      case 'docusign':
        result = await this.processV2DocuSignUpload(interview, pageDefinition, file, session);
        break;
        
      case 'email':
        result = await this.processV2EmailUpload(interview, pageDefinition, file, session);
        break;
        
      case 'custom':
        result = await this.processV2CustomUpload(interview, pageDefinition, file, session);
        break;
        
      default:
        throw new HttpException(`Unknown processor: ${pageDefinition.uploadConfig.processor.type}`, HttpStatus.BAD_REQUEST);
    }

    // Record upload in interview
    await this.recordV2DocumentUpload(interviewId, pageDefinition, result, session);

    return result;
  }

  /**
   * Process V2 DocuSign upload with envelope creation if needed
   */
  private async processV2DocuSignUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    const client = await this.clientsService.findOne({ _id: interview.client });
    
    // Create envelope if doesn't exist (addressing the gap you identified)
    if (!interview.envelopeId) {
      this.logger.info(`Creating DocuSign envelope for interview ${interview._id}`);
      const envelopeId = await this.interviewEnvelopeService.createDocusignEnvelope(interview._id.toString());
      
      // Update interview with envelope ID
      await this.interviewV2Model.updateOne(
        { _id: interview._id },
        { $set: { envelopeId } },
        { session }
      );
      
      interview.envelopeId = envelopeId;
    }

    // Add document to envelope using existing logic
    await this.docusignService.addClientDocuments({
      organisationId: client.organisationId.toString(),
      advisorId: client.primaryAdvisor.id.toString(),
      envelopeId: interview.envelopeId,
      files: [file]
    });

    this.logger.info(`Added document ${pageDefinition.uploadConfig.documentId} to DocuSign envelope ${interview.envelopeId}`);

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'docusign',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Process V2 email upload - send notification to recipients
   */
  private async processV2EmailUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    const client = await this.clientsService.findOne({ _id: interview.client });
    const config = pageDefinition.uploadConfig.processor.config;
    
    // Determine recipients
    const recipients = config?.recipients || [client.primaryAdvisor.email];
    
    // Create email context
    const emailContext = {
      clientName: `${client.primaryContact.firstName} ${client.primaryContact.lastName}`,
      documentName: pageDefinition.uploadConfig.documentName,
      documentDescription: pageDefinition.uploadConfig.description,
      interviewId: interview._id.toString(),
      uploadedAt: new Date(),
      fileName: file.originalname,
      fileSize: this.formatFileSize(file.size),
      advisorName: `${client.primaryAdvisor.firstName} ${client.primaryAdvisor.lastName}`,
      organisationName: client.organisationId.name || 'Your Organization'
    };

    // Send actual email to each recipient
    try {
      for (const recipient of recipients) {
        await this.mailService.send({
          to: recipient,
          template: 'document-upload-notification',
          context: emailContext,
          subject: `Document Upload Notification: ${pageDefinition.uploadConfig.documentName}`,
          // If template doesn't exist, use a fallback HTML
          fallbackHtml: `
            <h2>Document Upload Notification</h2>
            <p>A new document has been uploaded for client ${emailContext.clientName}.</p>
            <ul>
              <li><strong>Document:</strong> ${emailContext.documentName}</li>
              <li><strong>Description:</strong> ${emailContext.documentDescription || 'N/A'}</li>
              <li><strong>File Name:</strong> ${emailContext.fileName}</li>
              <li><strong>File Size:</strong> ${emailContext.fileSize}</li>
              <li><strong>Upload Time:</strong> ${emailContext.uploadedAt.toLocaleString()}</li>
              <li><strong>Interview ID:</strong> ${emailContext.interviewId}</li>
            </ul>
            <p>Please log in to your advisor portal to review this document.</p>
          `
        });
        
        this.logger.info(`Sent document upload notification for ${pageDefinition.uploadConfig.documentId} to ${recipient}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send email notification for document ${pageDefinition.uploadConfig.documentId}: ${error.message}`);
      // Don't fail the upload if email fails - just log it
    }

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'email',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Process V2 custom upload - placeholder for future custom processors
   */
  private async processV2CustomUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    // For now, just log and return success
    this.logger.info(`Custom processor for ${pageDefinition.uploadConfig.documentId} - not yet implemented`);

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'custom',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Record V2 document upload in interview schema
   */
  private async recordV2DocumentUpload(
    interviewId: string,
    pageDefinition: InterviewPageDefV2,
    result: DocumentUploadResult,
    session?: ClientSession
  ): Promise<void> {
    await this.interviewV2Model.updateOne(
      { _id: interviewId },
      {
        $push: {
          documentUploads: {
            documentId: result.documentId,
            processor: result.processor,
            uploadedAt: result.uploadedAt,
            pageId: pageDefinition.pageId,
            metadata: result.metadata
          }
        }
      },
      { session }
    );

    this.logger.info(`Recorded document upload ${result.documentId} for interview ${interviewId}`);
  }

  /**
   * Get V2 document upload status for an interview
   */
  async getV2DocumentUploadStatus(interviewId: string): Promise<{
    uploads: any[];
    totalUploads: number;
  }> {
    const interview = await this.interviewV2Model.findById(interviewId).select('documentUploads');
    
    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    return {
      uploads: interview.documentUploads || [],
      totalUploads: interview.documentUploads?.length || 0
    };
  }

  /**
   * Get V2 interview with error handling
   */
  private async getV2Interview(interviewId: string, session?: ClientSession): Promise<InterviewV2> {
    const interview = await this.interviewV2Model.findById(interviewId).session(session);
    
    if (!interview) {
      throw new HttpException(`Interview ${interviewId} not found`, HttpStatus.NOT_FOUND);
    }

    return interview;
  }

  /**
   * Validate uploaded file against page configuration
   */
  private validateUploadFile(file: Express.Multer.File, uploadConfig: any): void {
    // Check file size
    if (file.size > uploadConfig.maxSizeBytes) {
      throw new HttpException(
        `File size ${this.formatFileSize(file.size)} exceeds maximum allowed size ${this.formatFileSize(uploadConfig.maxSizeBytes)}`,
        HttpStatus.BAD_REQUEST
      );
    }

    // Check file format
    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    if (fileExtension && !uploadConfig.acceptedFormats.includes(fileExtension)) {
      throw new HttpException(
        `File format .${fileExtension} is not allowed. Accepted formats: ${uploadConfig.acceptedFormats.join(', ')}`,
        HttpStatus.BAD_REQUEST
      );
    }

    // Check MIME type for additional security
    const allowedMimeTypes = this.getAcceptedMimeTypes(uploadConfig.acceptedFormats);
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new HttpException(
        `MIME type ${file.mimetype} is not allowed for this upload`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * Get accepted MIME types for file extensions
   */
  private getAcceptedMimeTypes(formats: string[]): string[] {
    const mimeMap: Record<string, string[]> = {
      'pdf': ['application/pdf'],
      'jpg': ['image/jpeg'],
      'jpeg': ['image/jpeg'],
      'png': ['image/png'],
      'doc': ['application/msword'],
      'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      'txt': ['text/plain']
    };

    return formats.flatMap(format => mimeMap[format] || []);
  }

  /**
   * Format file size in human readable format
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}