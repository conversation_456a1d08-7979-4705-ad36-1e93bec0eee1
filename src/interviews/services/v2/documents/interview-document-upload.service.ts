import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { InterviewV2 } from '../../../schemas/v2/interview.schema';
import { InterviewPageDefV2 } from 'src/interview-templates/schemas/v2/interview-page-def.schema';
import { InterviewEnvelopeService } from '../envelope/interview-envelope.service';
import { InterviewNotificationService } from '../notifications/interview-notification.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { MailService } from 'src/notifications/mail/mail.service';

export interface DocumentUploadResult {
  success: boolean;
  documentId: string;
  processor: string;
  uploadedAt: Date;
  metadata?: {
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
  };
}

@Injectable()
export class InterviewDocumentUploadService {
  private readonly logger = new Logger(InterviewDocumentUploadService.name);

  constructor(
    @InjectModel(InterviewV2.name)
    private interviewModel: Model<InterviewV2>,
    private interviewEnvelopeService: InterviewEnvelopeService,
    private interviewNotificationService: InterviewNotificationService,
    private docusignService: DocusignService,
    private mailService: MailService,
  ) {}

  /**
   * Process document upload based on page configuration
   */
  async processDocumentUpload(
    interviewId: string,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    // Validate page type
    if (pageDefinition.pageType !== 'document_upload') {
      throw new BadRequestException('Page is not a document upload page');
    }

    // Validate upload configuration exists
    if (!pageDefinition.uploadConfig) {
      throw new BadRequestException('Page does not have upload configuration');
    }

    const interview = await this.getInterview(interviewId, session);
    
    // Validate file against page configuration
    this.validateUploadFile(file, pageDefinition.uploadConfig);

    let result: DocumentUploadResult;

    // Route to appropriate processor
    switch (pageDefinition.uploadConfig.processor.type) {
      case 'docusign':
        result = await this.processDocuSignUpload(interview, pageDefinition, file, session);
        break;
        
      case 'email':
        result = await this.processEmailUpload(interview, pageDefinition, file, session);
        break;
        
      case 'custom':
        result = await this.processCustomUpload(interview, pageDefinition, file, session);
        break;
        
      default:
        throw new BadRequestException(`Unknown processor: ${pageDefinition.uploadConfig.processor.type}`);
    }

    // Record upload in interview
    await this.recordDocumentUpload(interviewId, pageDefinition, result, session);

    return result;
  }

  /**
   * Process DocuSign upload (synchronous like V1)
   */
  private async processDocuSignUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    const client = await this.getPopulatedClient(interview);
    
    // Create envelope if doesn't exist (leveraging existing envelope service)
    if (!interview.envelopeId) {
      this.logger.log(`Creating DocuSign envelope for interview ${interview._id}`);
      const envelopeId = await this.interviewEnvelopeService.createEnvelope(interview._id.toString());
      
      // Update interview with envelope ID
      await this.interviewModel.updateOne(
        { _id: interview._id },
        { $set: { envelopeId } },
        { session }
      );
      
      interview.envelopeId = envelopeId;
    }

    // Add document to envelope (existing pattern)
    await this.docusignService.addClientDocuments({
      organisationId: client.organisationId.toString(),
      advisorId: client.primaryAdvisor.id.toString(),
      envelopeId: interview.envelopeId,
      files: [file]
    });

    this.logger.log(`Added document ${pageDefinition.uploadConfig.documentId} to DocuSign envelope ${interview.envelopeId}`);

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'docusign',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Process email upload - send notification to recipients
   */
  private async processEmailUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    const client = await this.getPopulatedClient(interview);
    const config = pageDefinition.uploadConfig.processor.config;
    
    // Determine recipients
    const recipients = config?.recipients || [client.primaryAdvisor.email];
    
    // Send notification email (no attachment - just notification)
    const emailContext = {
      clientName: `${client.primaryContact.firstName} ${client.primaryContact.lastName}`,
      documentName: pageDefinition.uploadConfig.documentName,
      documentDescription: pageDefinition.uploadConfig.description,
      interviewId: interview._id.toString(),
      uploadedAt: new Date(),
      fileName: file.originalname,
      fileSize: this.formatFileSize(file.size)
    };

    // Send to each recipient
    for (const recipient of recipients) {
      await this.mailService.sendEmail(
        {
          to: recipient,
          subject: `New Document Upload: ${pageDefinition.uploadConfig.documentName}`,
          templateName: 'document_upload_notification' as any, // Would need to create this template
          context: emailContext,
          organisation: client.organisationId
        },
        client.primaryContact,
        client.primaryAdvisor.id?.toString()
      );
    }

    this.logger.log(`Sent document upload notification for ${pageDefinition.uploadConfig.documentId} to ${recipients.join(', ')}`);

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'email',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Process custom upload - placeholder for future custom processors
   */
  private async processCustomUpload(
    interview: InterviewV2,
    pageDefinition: InterviewPageDefV2,
    file: Express.Multer.File,
    session?: ClientSession
  ): Promise<DocumentUploadResult> {
    // For now, just log and return success
    // Future custom processors can be implemented here
    this.logger.log(`Custom processor for ${pageDefinition.uploadConfig.documentId} - not yet implemented`);

    return {
      success: true,
      documentId: pageDefinition.uploadConfig.documentId,
      processor: 'custom',
      uploadedAt: new Date(),
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype
      }
    };
  }

  /**
   * Record document upload in interview schema
   */
  private async recordDocumentUpload(
    interviewId: string,
    pageDefinition: InterviewPageDefV2,
    result: DocumentUploadResult,
    session?: ClientSession
  ): Promise<void> {
    await this.interviewModel.updateOne(
      { _id: interviewId },
      {
        $push: {
          documentUploads: {
            documentId: result.documentId,
            processor: result.processor,
            uploadedAt: result.uploadedAt,
            pageId: pageDefinition.pageId,
            metadata: result.metadata
          }
        }
      },
      { session }
    );

    this.logger.log(`Recorded document upload ${result.documentId} for interview ${interviewId}`);
  }

  /**
   * Get document upload status for an interview
   */
  async getDocumentUploadStatus(interviewId: string): Promise<{
    uploads: any[];
    totalUploads: number;
  }> {
    const interview = await this.interviewModel.findById(interviewId).select('documentUploads');
    
    if (!interview) {
      throw new NotFoundException('Interview not found');
    }

    return {
      uploads: interview.documentUploads || [],
      totalUploads: interview.documentUploads?.length || 0
    };
  }

  /**
   * Validate uploaded file against page configuration
   */
  private validateUploadFile(file: Express.Multer.File, uploadConfig: any): void {
    // Check file size
    if (file.size > uploadConfig.maxSizeBytes) {
      throw new BadRequestException(
        `File size ${this.formatFileSize(file.size)} exceeds maximum allowed size ${this.formatFileSize(uploadConfig.maxSizeBytes)}`
      );
    }

    // Check file format
    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    if (fileExtension && !uploadConfig.acceptedFormats.includes(fileExtension)) {
      throw new BadRequestException(
        `File format .${fileExtension} is not allowed. Accepted formats: ${uploadConfig.acceptedFormats.join(', ')}`
      );
    }

    // Check MIME type for additional security
    const allowedMimeTypes = this.getAcceptedMimeTypes(uploadConfig.acceptedFormats);
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `MIME type ${file.mimetype} is not allowed for this upload`
      );
    }
  }

  /**
   * Get accepted MIME types for file extensions
   */
  private getAcceptedMimeTypes(formats: string[]): string[] {
    const mimeMap: Record<string, string[]> = {
      'pdf': ['application/pdf'],
      'jpg': ['image/jpeg'],
      'jpeg': ['image/jpeg'],
      'png': ['image/png'],
      'doc': ['application/msword'],
      'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      'txt': ['text/plain']
    };

    return formats.flatMap(format => mimeMap[format] || []);
  }

  /**
   * Format file size in human readable format
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get interview with error handling
   */
  private async getInterview(interviewId: string, session?: ClientSession): Promise<InterviewV2> {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    
    if (!interview) {
      throw new NotFoundException(`Interview ${interviewId} not found`);
    }

    return interview;
  }

  /**
   * Get populated client data (helper method - would use existing client service)
   */
  private async getPopulatedClient(interview: InterviewV2): Promise<any> {
    // This would use the existing ClientsV2Service to get populated client data
    // For now, returning a placeholder - actual implementation would use dependency injection
    return interview.client;
  }
}