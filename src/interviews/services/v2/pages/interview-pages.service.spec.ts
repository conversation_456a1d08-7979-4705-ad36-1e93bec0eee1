import { Test, TestingModule } from '@nestjs/testing';
import { InterviewPagesService } from './interview-pages.service';
import { getModelToken } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from '../../../dto/v1/update-interview.dto';
import { EnrichedInterview } from '../../../dto/v1/enriched-interview-dto';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { HttpException, HttpStatus } from '@nestjs/common';
import * as handlersMapper from 'src/interviews/utils/page-updates/handlers.mapper';

describe('InterviewPagesService', () => {
  let service: InterviewPagesService;
  let interviewModel: Model<Interview>;
  let clientsService: ClientsV2Service;

  const mockInterviewModel = {
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    updateOne: jest.fn(),
  };

  const mockClientsV2Service = {
    findOne: jest.fn(),
    updateClientCompletionPercentage: jest.fn(),
    updateLastContactActivityTimestamp: jest.fn(),
  };

  const mockPageHandler = {
    handle: jest.fn(),
  };

  const mockSession = {} as ClientSession;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewPagesService,
        {
          provide: getModelToken(Interview.name),
          useValue: mockInterviewModel,
        },
        {
          provide: ClientsV2Service,
          useValue: mockClientsV2Service,
        },
      ],
    }).compile();

    service = module.get<InterviewPagesService>(InterviewPagesService);
    interviewModel = module.get<Model<Interview>>(getModelToken(Interview.name));
    clientsService = module.get<ClientsV2Service>(ClientsV2Service);

    // Reset all mocks
    jest.clearAllMocks();
    jest.spyOn(handlersMapper, 'getPageHandler').mockReturnValue(mockPageHandler);
  });

  describe('updatePages', () => {
    it('should update pages with default SYNCED status', () => {
      const pages = [
        { name: PagesEnum.NAME, elements: [], filled: false },
        { name: PagesEnum.ADDRESS, elements: [], filled: false },
      ];
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.NAME, data: {} } };
      const expectedPage = { name: PagesEnum.NAME, elements: [], filled: true };

      mockPageHandler.handle.mockReturnValue(expectedPage);

      const result = service.updatePages(pages, dto) as any;

      expect(handlersMapper.getPageHandler).toHaveBeenCalledTimes(2);
      expect(handlersMapper.getPageHandler).toHaveBeenCalledWith(
        PagesEnum.NAME,
        { fillContingentBeneficiaries: false },
        dto,
        InterviewPageStatusEnum.SYNCED,
      );
      expect(mockPageHandler.handle).toHaveBeenCalledTimes(2);
      expect(result).toHaveLength(2);
    });

    it('should update pages with custom status', () => {
      const pages = [{ name: PagesEnum.DOB, elements: [], filled: false }];
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.DOB, data: {} } };
      const status = InterviewPageStatusEnum.UNANSWERED;

      service.updatePages(pages, dto, status);

      expect(handlersMapper.getPageHandler).toHaveBeenCalledWith(
        PagesEnum.DOB,
        { fillContingentBeneficiaries: false },
        dto,
        status,
      );
    });
  });

  describe('calculateCompletion', () => {
    const mockTemplate = {
      pages: [
        { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
        { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.SYNCED },
        { name: PagesEnum.PRIMARY_BENEFICIARIES, status: InterviewPageStatusEnum.UNANSWERED },
        { name: PagesEnum.CONTINGENT_BENEFICIARIES, status: InterviewPageStatusEnum.UNANSWERED },
      ],
    };

    it('should return 100 when interview is already complete', () => {
      const interview: EnrichedInterview = {
        isComplete: true,
        isPrimary: true,
        template: mockTemplate,
      } as any;
      const client: EnrichedClient = {} as any;

      const result = service.calculateCompletion(interview, client);

      expect(result).toBe(100);
    });

    it('should calculate percentage for primary contact with accounts', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: true,
        template: mockTemplate,
      } as any;
      const client: EnrichedClient = {
        primaryContact: {
          accounts: [{ _id: 'account1' }],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // 2 synced pages out of 4 total pages = 50%
      expect(result).toBe(50);
    });

    it('should calculate percentage for primary contact without accounts', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: true,
        template: mockTemplate,
      } as any;
      const client: EnrichedClient = {
        primaryContact: {
          accounts: [],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // 2 synced pages out of 2 non-beneficiary pages = 100%
      expect(result).toBe(100);
    });

    it('should calculate percentage for secondary contact with accounts', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: false,
        template: mockTemplate,
      } as any;
      const client: EnrichedClient = {
        secondaryContact: {
          accounts: [{ _id: 'account1' }],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // 2 synced pages out of 4 total pages = 50%
      expect(result).toBe(50);
    });

    it('should calculate percentage for secondary contact without accounts', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: false,
        template: mockTemplate,
      } as any;
      const client: EnrichedClient = {
        secondaryContact: {
          accounts: [],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // 2 synced pages out of 2 non-beneficiary pages = 100%
      expect(result).toBe(100);
    });

    it('should round up percentage correctly', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: true,
        template: {
          pages: [
            { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
            { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.UNANSWERED },
            { name: PagesEnum.DOB, status: InterviewPageStatusEnum.UNANSWERED },
          ],
        },
      } as any;
      const client: EnrichedClient = {
        primaryContact: {
          accounts: [],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // 1 synced page out of 3 total pages = 33.33...% rounded up to 34%
      expect(result).toBe(34);
    });
  });

  describe('validateAllSynced', () => {
    it('should return true when all pages are synced', async () => {
      const mockInterview = {
        template: {
          pages: [
            { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
            { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.SYNCED },
          ],
        },
      };
      mockInterviewModel.findById.mockResolvedValue(mockInterview);

      const result = await service.validateAllSynced('interviewId');

      expect(mockInterviewModel.findById).toHaveBeenCalledWith('interviewId');
      expect(result).toBe(true);
    });

    it('should return false when some pages are not synced', async () => {
      const mockInterview = {
        template: {
          pages: [
            { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
            { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.UNANSWERED },
          ],
        },
      };
      mockInterviewModel.findById.mockResolvedValue(mockInterview);

      const result = await service.validateAllSynced('interviewId');

      expect(result).toBe(false);
    });

    it('should throw HttpException when interview not found', async () => {
      mockInterviewModel.findById.mockResolvedValue(null);

      await expect(service.validateAllSynced('interviewId')).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('updateInterviewPages', () => {
    const mockInterview = {
      _id: 'interviewId',
      client: 'clientId',
      isPrimary: true,
      template: {
        pages: [
          { name: PagesEnum.NAME, status: InterviewPageStatusEnum.UNANSWERED },
          { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.UNANSWERED },
        ],
      },
      toObject: jest.fn(),
    };

    const mockClient = {
      _id: 'clientId',
      primaryContact: {
        accounts: [],
      },
    };

    beforeEach(() => {
      mockInterview.toObject.mockReturnValue(mockInterview);
      mockInterviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      });
      mockClientsV2Service.findOne.mockResolvedValue(mockClient);
      mockPageHandler.handle.mockImplementation((page) => ({
        ...page,
        status: InterviewPageStatusEnum.SYNCED,
        filled: true,
      }));
      mockInterviewModel.findByIdAndUpdate.mockImplementation(() => {
        // Update the template pages with SYNCED status
        const updatedInterview = {
          ...mockInterview,
          template: {
            pages: mockInterview.template.pages.map(page => ({
              ...page,
              status: InterviewPageStatusEnum.SYNCED,
            })),
          },
        };
        return Promise.resolve(updatedInterview);
      });
    });

    it('should update interview pages successfully with transaction', async () => {
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.NAME, data: {} } };
      const pageStatus = InterviewPageStatusEnum.SYNCED;

      const result = await service.updateInterviewPages('interviewId', dto, pageStatus, mockSession);

      expect(mockInterviewModel.findById).toHaveBeenCalledWith('interviewId');
      expect(mockClientsV2Service.findOne).toHaveBeenCalledWith(
        { _id: 'clientId' },
        mockSession,
        false,
        true,
      );
      // The calculateCompletion is called BEFORE pages are updated, so it sees 0 synced pages
      expect(mockClientsV2Service.updateClientCompletionPercentage).toHaveBeenCalledWith(
        'clientId',
        0, // No pages are synced yet (calculation happens before update)
        expect.any(Date),
        mockSession,
      );
      expect(mockClientsV2Service.updateLastContactActivityTimestamp).toHaveBeenCalledWith(
        'clientId',
        true,
        mockSession,
      );
      expect(mockInterviewModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'interviewId',
        { $set: { 'template.pages': expect.any(Array) } },
        { new: true, session: mockSession },
      );
      expect(result).toBeTruthy();
    });

    it('should calculate correct percentage and update client', async () => {
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.NAME, data: {} } };
      const pageStatus = InterviewPageStatusEnum.SYNCED;

      // Mock handler to only sync the first page
      mockPageHandler.handle.mockImplementation((page) =>
        page.name === PagesEnum.NAME
          ? { ...page, status: InterviewPageStatusEnum.SYNCED }
          : { ...page, status: InterviewPageStatusEnum.UNANSWERED },
      );

      await service.updateInterviewPages('interviewId', dto, pageStatus);

      expect(mockClientsV2Service.updateClientCompletionPercentage).toHaveBeenCalledWith(
        'clientId',
        0, // calculateCompletion happens BEFORE pages are updated, so still 0
        expect.any(Date),
        undefined, // No session passed
      );
    });

    it('should throw HttpException when interview not found', async () => {
      mockInterviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      });

      await expect(
        service.updateInterviewPages('interviewId', {} as UpdateInterviewDto, InterviewPageStatusEnum.SYNCED),
      ).rejects.toThrow(new HttpException('Interview not found', HttpStatus.NOT_FOUND));
    });

    it('should work without session', async () => {
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.NAME, data: {} } };
      const pageStatus = InterviewPageStatusEnum.SYNCED;

      await service.updateInterviewPages('interviewId', dto, pageStatus);

      expect(mockInterviewModel.findById).toHaveBeenCalledWith('interviewId');
      const sessionCall = (mockInterviewModel.findById as jest.Mock).mock.results[0].value;
      expect(sessionCall.session).toHaveBeenCalledWith(undefined);
    });
  });

  describe('addPage', () => {
    it('should add new page successfully', async () => {
      mockInterviewModel.updateOne.mockResolvedValue({ modifiedCount: 1 });

      await service.addPage('interviewId', PagesEnum.CUSTOM_QUESTIONS);

      expect(mockInterviewModel.updateOne).toHaveBeenCalledWith(
        { _id: 'interviewId', 'template.pages.name': { $ne: PagesEnum.CUSTOM_QUESTIONS } },
        {
          $push: {
            'template.pages': {
              name: PagesEnum.CUSTOM_QUESTIONS,
              order: 0,
              elements: [],
              filled: false,
              data: {},
            },
          },
        },
        { runValidators: false, new: true },
      );
    });

    it('should not add page if it already exists', async () => {
      mockInterviewModel.updateOne.mockResolvedValue({ modifiedCount: 0 });

      await service.addPage('interviewId', PagesEnum.NAME);

      expect(mockInterviewModel.updateOne).toHaveBeenCalledWith(
        { _id: 'interviewId', 'template.pages.name': { $ne: PagesEnum.NAME } },
        expect.any(Object),
        expect.any(Object),
      );
    });
  });

  describe('removePage', () => {
    it('should remove page successfully', async () => {
      mockInterviewModel.updateOne.mockResolvedValue({ modifiedCount: 1 });

      await service.removePage('interviewId', PagesEnum.CUSTOM_QUESTIONS);

      expect(mockInterviewModel.updateOne).toHaveBeenCalledWith(
        { _id: 'interviewId' },
        { $pull: { 'template.pages': { name: PagesEnum.CUSTOM_QUESTIONS } } },
      );
    });

    it('should throw HttpException when page not found', async () => {
      mockInterviewModel.updateOne.mockResolvedValue({ modifiedCount: 0 });

      await expect(service.removePage('interviewId', 'nonExistentPage')).rejects.toThrow(
        new HttpException('Page not found', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('edge cases', () => {
    it('should handle empty pages array in updatePages', () => {
      const pages: any[] = [];
      const dto: UpdateInterviewDto = { page: { name: PagesEnum.NAME, data: {} } };

      const result = service.updatePages(pages, dto);

      expect(result).toEqual([]);
      expect(handlersMapper.getPageHandler).not.toHaveBeenCalled();
    });

    it('should handle null secondary contact in calculateCompletion', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: false,
        template: {
          pages: [
            { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
            { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.UNANSWERED },
          ],
        },
      } as any;
      const client: EnrichedClient = {
        secondaryContact: null,
      } as any;

      const result = service.calculateCompletion(interview, client);

      // Should treat null secondary contact as having no accounts
      expect(result).toBe(50);
    });

    it('should handle pages with undefined status in calculateCompletion', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: true,
        template: {
          pages: [
            { name: PagesEnum.NAME, status: InterviewPageStatusEnum.SYNCED },
            { name: PagesEnum.ADDRESS, status: InterviewPageStatusEnum.UNANSWERED },
            { name: PagesEnum.DOB, status: InterviewPageStatusEnum.SYNCED },
          ],
        },
      } as any;
      const client: EnrichedClient = {
        primaryContact: {
          accounts: [],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // Only 2 synced pages out of 3 = 67% rounded up
      expect(result).toBe(67);
    });

    it('should handle zero total pages in calculateCompletion', () => {
      const interview: EnrichedInterview = {
        isComplete: false,
        isPrimary: true,
        template: {
          pages: [],
        },
      } as any;
      const client: EnrichedClient = {
        primaryContact: {
          accounts: [],
        },
      } as any;

      const result = service.calculateCompletion(interview, client);

      // Should handle division by zero gracefully
      expect(result).toBe(NaN);
    });
  });
});