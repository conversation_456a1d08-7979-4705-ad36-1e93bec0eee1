import { Test, TestingModule } from '@nestjs/testing';
import { InterviewEnvelopeService } from './interview-envelope.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { InterviewDocumentsService } from '../documents/interview-documents.service';
import { InterviewTemplatesV2Service } from 'src/interview-templates/services/v2/interview-templates.service';
import { ConfigService } from '@nestjs/config';
import { ClsService } from 'nestjs-cls';
import { getModelToken, getConnectionToken } from '@nestjs/mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { InterviewV2 } from '../../../schemas/v2/interview.schema';
import { Model, ClientSession, Connection } from 'mongoose';
import { DocusignAccountOwnershipEnum } from 'src/integrations/docusign/docusign.types';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import { AddClientDocumentsDto } from 'src/integrations/docusign/dto/add-client-documents.dto';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { CRMEnum } from 'src/shared/types/integrations';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { RedtailAccountOwnershipEnum, RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { getQueueToken } from '@nestjs/bullmq';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { UploadDocumentDto } from '../../../dto/v1/upload-document.dto';
import { PdfDataProviderFactory } from '../../../services/pdf-data/pdf-data-provider.factory';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';

describe('InterviewEnvelopeService', () => {
  let service: InterviewEnvelopeService;
  let docusignService: jest.Mocked<DocusignService>;
  let clientsService: jest.Mocked<ClientsV2Service>;
  let advisorsDocusignService: jest.Mocked<AdvisorsDocusignService>;
  let organisationsService: jest.Mocked<OrganisationsService>;
  let advisorsCrudService: jest.Mocked<AdvisorsCrudService>;
  let advisorsCrmService: jest.Mocked<AdvisorsCrmService>;
  let documentsService: jest.Mocked<InterviewDocumentsService>;
  let interviewTemplateService: jest.Mocked<InterviewTemplatesV2Service>;
  let configService: jest.Mocked<ConfigService>;
  let clsService: jest.Mocked<ClsService>;
  let interviewModel: jest.Mocked<Model<InterviewV2>>;
  let connection: jest.Mocked<Connection>;
  let clientQueue: jest.Mocked<any>;
  let pdfDataProviderFactory: jest.Mocked<PdfDataProviderFactory>;
  let logger: jest.Mocked<any>;

  const mockSession = {
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    abortTransaction: jest.fn(),
    endSession: jest.fn(),
  } as unknown as ClientSession;

  const mockInterview = {
    _id: 'interview123',
    client: 'client123',
    envelopeId: null,
    docusignSelected: true,
    isComplete: true,
    toObject: jest.fn().mockReturnThis(),
  };

  const mockClient = {
    _id: 'client123',
    organisationId: { toString: () => 'org123' },
    primaryAdvisor: {
      id: { toString: () => 'advisor123' },
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      mobile: '**********',
    },
    primaryContact: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      mobile: '**********',
      crmClientId: 'crm123',
      accounts: [{
        id: 'acc1',
        type: AccountTypeEnum.Ira,
        label: 'IRA Account',
        ownership: 'Individual',
      }],
    },
    secondaryContact: {
      firstName: 'Bob',
      lastName: 'Smith',
      email: '<EMAIL>',
      mobile: '**********',
      crmClientId: 'crm456',
      accounts: [{
        id: 'acc2',
        type: AccountTypeEnum.SingleNameBrokerage,
        label: 'Individual Account',
        ownership: 'Individual',
      }],
    },
    fileUploadsNo: 2,
  };

  const mockOrganisation = {
    _id: 'org123',
    name: 'Test Organisation',
  };

  const mockAdvisor = {
    _id: 'advisor123',
    organisation: { _id: 'org123' },
    firstName: 'John',
    lastName: 'Doe',
  };

  const mockCrmInstance = {
    getType: jest.fn().mockResolvedValue(CRMEnum.Redtail),
    getContact: jest.fn(),
  };

  const mockCrmContactData = {
    firstName: 'Jane',
    lastName: 'Smith',
    dob: '1990-01-01',
    accounts: [{
      type: 'ira',
      name: 'Traditional IRA Account',
      accountNumber: '12345',
    }],
  };

  const mockFiles = [
    {
      originalname: 'advisory-agreement.pdf',
      buffer: Buffer.from('mock pdf content'),
      mimetype: 'application/pdf',
    } as Express.Multer.File,
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewEnvelopeService,
        {
          provide: DocusignService,
          useValue: {
            getAccountInfo: jest.fn(),
            createEnvelope: jest.fn(),
            getEnvelopDocuments: jest.fn(),
            addAdvisorDocuments: jest.fn(),
            addClientDocuments: jest.fn(),
          },
        },
        {
          provide: ClientsV2Service,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: AdvisorsDocusignService,
          useValue: {
            findDocusignIntegration: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findDocusignIntegration: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrmService,
          useValue: {
            getCrmInstance: jest.fn(),
          },
        },
        {
          provide: InterviewDocumentsService,
          useValue: {
            getAccountOpeningAdvisoryFiles: jest.fn(),
            getFilesFromS3: jest.fn(),
          },
        },
        {
          provide: InterviewTemplatesV2Service,
          useValue: {},
        },
        {
          provide: PdfDataProviderFactory,
          useValue: {
            create: jest.fn().mockReturnValue({
              getInterviewDataForPdf: jest.fn().mockResolvedValue({
                interview: {
                  _id: 'interview123',
                  isPrimary: true,
                  isComplete: true,
                  docusignSelected: true,
                  envelopeId: null,
                },
                organisation: mockOrganisation,
                primaryAdvisor: mockAdvisor,
                primaryContact: mockClient.primaryContact,
                secondaryContact: mockClient.secondaryContact,
              }),
            }),
          },
        },
        {
          provide: getConnectionToken(),
          useValue: {
            startSession: jest.fn(),
          },
        },
        {
          provide: getQueueToken(CLIENT_QUEUE.NAME),
          useValue: {
            add: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: getModelToken(InterviewV2.name),
          useValue: {
            findById: jest.fn(),
            find: jest.fn(),
            updateMany: jest.fn(),
            updateOne: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InterviewEnvelopeService>(InterviewEnvelopeService);
    docusignService = module.get(DocusignService);
    clientsService = module.get(ClientsV2Service);
    advisorsDocusignService = module.get(AdvisorsDocusignService);
    organisationsService = module.get(OrganisationsService);
    advisorsCrudService = module.get(AdvisorsCrudService);
    advisorsCrmService = module.get(AdvisorsCrmService);
    documentsService = module.get(InterviewDocumentsService);
    interviewTemplateService = module.get(InterviewTemplatesV2Service);
    configService = module.get(ConfigService);
    clsService = module.get(ClsService);
    interviewModel = module.get(getModelToken(InterviewV2.name));
    connection = module.get(getConnectionToken());
    clientQueue = module.get(getQueueToken(CLIENT_QUEUE.NAME));
    pdfDataProviderFactory = module.get(PdfDataProviderFactory);
    logger = module.get(WINSTON_MODULE_PROVIDER);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createDocusignEnvelope', () => {
    beforeEach(() => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(null);
      organisationsService.findDocusignIntegration.mockResolvedValue(null);
      configService.get.mockReturnValue('https://webhook.url');
      docusignService.createEnvelope.mockResolvedValue('envelope123');
      interviewModel.updateMany.mockResolvedValue({ acknowledged: true } as any);
    });

    it('should successfully create a new envelope', async () => {
      const result = await service.createDocusignEnvelope('interview123', mockSession);

      expect(result).toBe('envelope123');
      expect(interviewModel.findById).toHaveBeenCalledWith('interview123');
      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' });
      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          advisorId: 'advisor123',
          organisationId: 'org123',
          primaryAdvisorName: 'John Doe',
          primaryAdvisorEmail: '<EMAIL>',
          primaryAdvisorPhone: '**********',
          applicant: {
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '**********',
          },
          coapplicant: {
            name: 'Bob Smith',
            email: '<EMAIL>',
            phone: '**********',
          },
          envelopeId: null,
          isSchwabOwnedDocusignAccount: false,
          shouldSend: false,
          webhookUrl: 'https://webhook.url',
          files: [],
          schwabTemplateId: undefined,
        }),
        mockSession,
      );
      expect(interviewModel.updateMany).toHaveBeenCalledWith(
        { client: 'client123' },
        {
          $set: {
            envelopeId: 'envelope123',
            updatedAt: expect.any(Date),
          },
        },
        { session: mockSession },
      );
    });

    it('should handle existing envelope (should not create new)', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      const result = await service.createDocusignEnvelope('interview123', mockSession);

      expect(result).toBe('envelope123');
      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          envelopeId: 'existing123',
        }),
        mockSession,
      );
    });

    it('should handle Schwab template ID', async () => {
      const integrationConfig = { schwabTemplateId: 'schwab-template-123' };
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(integrationConfig as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          schwabTemplateId: 'schwab-template-123',
        }),
        mockSession,
      );
    });

    it('should handle without secondary contact', async () => {
      const clientWithoutSecondary = { ...mockClient, secondaryContact: null };
      clientsService.findOne.mockResolvedValue(clientWithoutSecondary as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          coapplicant: false,
        }),
        mockSession,
      );
    });

    it('should throw error when interview not found', async () => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.createDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should handle Schwab owned DocuSign account', async () => {
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Schwab,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          isSchwabOwnedDocusignAccount: true,
        }),
        mockSession,
      );
    });

    it('should fallback to organisation integration config when advisor config is null', async () => {
      const orgConfig = { schwabTemplateId: 'org-template-123' };
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(null);
      organisationsService.findDocusignIntegration.mockResolvedValue(orgConfig as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          schwabTemplateId: 'org-template-123',
        }),
        mockSession,
      );
    });
  });

  describe('prepareDocusignEnvelope', () => {
    beforeEach(() => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      interviewModel.find.mockResolvedValue([mockInterview] as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: ['existing-doc.pdf'],
      } as any);
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      docusignService.addAdvisorDocuments.mockResolvedValue(undefined);
      advisorsCrudService.findOne.mockResolvedValue(mockAdvisor as any);
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      mockCrmInstance.getContact.mockResolvedValue(mockCrmContactData);
      clsService.get.mockReturnValue(null);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
    });

    it('should handle DocuSign selected with no existing envelope', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutEnvelope),
      } as any);
      docusignService.createEnvelope.mockResolvedValue('new-envelope123');
      interviewModel.updateMany.mockResolvedValue({ acknowledged: true } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(service.createDocusignEnvelope).toBeDefined();
      expect(docusignService.addAdvisorDocuments).toHaveBeenCalled();
    });

    it('should handle DocuSign selected with existing envelope', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.getEnvelopDocuments).toHaveBeenCalledWith({
        envelopeId: 'existing123',
        organisationId: 'org123',
        advisorId: 'advisor123',
      });
      expect(docusignService.addAdvisorDocuments).toHaveBeenCalled();
    });

    it('should handle both interviews finished scenario', async () => {
      interviewModel.find.mockResolvedValue([
        { ...mockInterview, isComplete: true },
        { ...mockInterview, isComplete: true },
      ] as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          advisorId: 'advisor123',
          organisationId: 'org123',
          envelopeId: undefined,
          files: mockFiles,
          interviewData: expect.any(Object),
          previousDocumentUploadNo: 2,
        }),
      );
    });

    it('should handle only one interview finished', async () => {
      interviewModel.find.mockResolvedValue([
        { ...mockInterview, isComplete: true },
        { ...mockInterview, isComplete: false },
      ] as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).not.toHaveBeenCalled();
    });

    it('should handle DocuSign not selected', async () => {
      const interviewNotDocusignSelected = { ...mockInterview, docusignSelected: false };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewNotDocusignSelected),
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).not.toHaveBeenCalled();
      expect(docusignService.addAdvisorDocuments).not.toHaveBeenCalled();
    });

    it('should filter files that already exist in envelope', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: ['advisory-agreement.pdf'],
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          files: [],
        }),
      );
    });

    it('should handle interview not found error', async () => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        'Interview interview123 not found',
      );
    });

    it('should handle client not found error', async () => {
      const interviewWithoutClient = { ...mockInterview, client: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutClient),
      } as any);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        'Client not found for interview interview123',
      );
    });

    it('should handle createDocusignEnvelope error gracefully', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutEnvelope),
      } as any);
      const createError = new Error('DocuSign service error');
      jest.spyOn(service, 'createDocusignEnvelope').mockRejectedValue(createError);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(createError);
      expect(logger.error).toHaveBeenCalledWith('Failed to create DocuSign envelope: DocuSign service error');
    });
  });

  // Note: getInterviewDataWithCrmInfo is handled by PdfDataProviderFactory in v2
  describe.skip('getInterviewDataWithCrmInfo', () => {
    const mockInterviewData = {
      interview: mockInterview,
      primaryContact: mockClient.primaryContact,
      secondaryContact: mockClient.secondaryContact,
      primaryAdvisor: mockClient.primaryAdvisor,
    };

    beforeEach(() => {
      advisorsCrudService.findOne.mockResolvedValue(mockAdvisor as any);
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      mockCrmInstance.getType.mockResolvedValue(CRMEnum.Redtail);
      mockCrmInstance.getContact.mockResolvedValue(mockCrmContactData);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
    });

    it('should enrich primary contact data', async () => {
      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledWith('crm123', true, true);
      expect(result.primaryContact).toMatchObject({
        ...mockClient.primaryContact,
        ...mockCrmContactData,
        dob: '01/01/1990',
      });
    });

    it('should enrich secondary contact data', async () => {
      const secondaryCrmData = {
        ...mockCrmContactData,
        firstName: 'Bob',
        accounts: [{
          type: 'brokerage',
          name: 'Single-Name Brokerage Account',
          accountNumber: '67890',
        }]
      };
      mockCrmInstance.getContact
        .mockResolvedValueOnce(mockCrmContactData)
        .mockResolvedValueOnce(secondaryCrmData);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledTimes(2);
      expect(mockCrmInstance.getContact).toHaveBeenNthCalledWith(2, 'crm456', true, true);
      expect(result.secondaryContact).toMatchObject({
        ...mockClient.secondaryContact,
        ...secondaryCrmData,
        dob: '01/01/1990',
      });
    });

    it('should use organization from CLS if available', async () => {
      clsService.get.mockReturnValue(mockOrganisation);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(clsService.get).toHaveBeenCalledWith(ClsDataEnum.Organisation);
      expect(organisationsService.findOne).not.toHaveBeenCalled();
      expect(result.organisation).toEqual(mockOrganisation);
    });

    it('should fetch organization if not in CLS', async () => {
      clsService.get.mockReturnValue(null);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(organisationsService.findOne).toHaveBeenCalledWith('org123', mockSession);
      expect(result.organisation).toEqual(mockOrganisation);
    });

    it('should merge accounts with correct CRM type', async () => {
      const crmAccounts = [{
        type: 'ira',
        name: 'Traditional IRA Account',
        accountNumber: '12345',
      }];
      mockCrmInstance.getContact.mockResolvedValue({
        ...mockCrmContactData,
        accounts: crmAccounts,
      });

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(result.primaryContact.accounts).toHaveLength(1);
      expect(result.primaryContact.accounts[0]).toMatchObject({
        ...mockClient.primaryContact.accounts[0],
        ...crmAccounts[0],
      });
    });

    it('should handle missing secondary contact', async () => {
      const dataWithoutSecondary = {
        ...mockInterviewData,
        secondaryContact: null,
      };

      const result = await (service as any).getInterviewDataWithCrmInfo(
        dataWithoutSecondary,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledTimes(1);
      expect(result.secondaryContact).toBeNull();
    });

    it('should format dates correctly', async () => {
      mockCrmInstance.getContact.mockResolvedValue({
        ...mockCrmContactData,
        dob: '1990-01-01T00:00:00Z',
      });

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(result.primaryContact.dob).toBe('01/01/1990');
    });
  });

  // Note: mergeAccounts is handled by PdfDataProviderFactory in v2
  describe.skip('mergeAccounts', () => {
    const dbAccounts = [
      {
        type: AccountTypeEnum.Ira,
        label: 'IRA Account',
        ownership: 'Individual',
        masterAccountNumber: 'master123',
        features: [],
        advisoryRate: 0.5,
      },
      {
        type: AccountTypeEnum.JointNameBrokerage,
        label: 'Joint Account',
        ownership: 'Joint',
        masterAccountNumber: 'master456',
        features: [],
        advisoryRate: 0.5,
      },
    ] as AccountDto[];

    const crmAccounts: GenericCrmAccount[] = [
      {
        type: 'ira',
        name: 'Traditional IRA Account',
        accountNumber: '12345',
      } as any,
      {
        type: 'joint',
        name: 'Joint-Name Brokerage Account',
        accountNumber: '67890',
      } as any,
    ];

    it('should handle Redtail CRM matching logic', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });

    it('should handle Practifi/Salesforce matching logic', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });

    it('should handle joint name brokerage special case', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });

    it('should return unmatched accounts with empty CRM data', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });

    it('should handle empty arrays', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });

    it('should handle Salesforce CRM type with label matching', async () => {
      // This functionality is now handled by PdfDataProviderFactory
      expect(true).toBe(true);
    });
  });

  describe('getEnvelopeFiles', () => {
    const mockAdvisoryFiles = [
      {
        originalname: 'advisory-agreement.pdf',
        buffer: Buffer.from('advisory content'),
        mimetype: 'application/pdf',
      } as Express.Multer.File,
    ];

    const mockAccountFiles = [
      {
        originalname: 'account-doc.pdf',
        buffer: Buffer.from('account content'),
        mimetype: 'application/pdf',
      } as Express.Multer.File,
    ];

    beforeEach(() => {
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue(mockAccountFiles);
      documentsService.getFilesFromS3.mockResolvedValue(mockAdvisoryFiles);
    });

    it('should get files for primary and secondary contacts', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        mockClient.secondaryContact as any,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledTimes(2);
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(
        1,
        mockClient.primaryContact.accounts,
        'org123',
      );
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(
        2,
        mockClient.secondaryContact.accounts,
        'org123',
      );
      expect(result).toEqual([...mockAccountFiles, ...mockAccountFiles, ...mockAdvisoryFiles]);
    });

    it('should get advisory agreement files', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        mockClient.secondaryContact as any,
      );

      expect(documentsService.getFilesFromS3).toHaveBeenCalledWith(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        'org123',
      );
      expect(result).toContain(mockAdvisoryFiles[0]);
    });

    it('should handle empty accounts arrays', async () => {
      const contactWithoutAccounts = { ...mockClient.primaryContact, accounts: [] };

      await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        contactWithoutAccounts as any,
        null as any,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledWith([], 'org123');
    });

    it('should handle null secondary contact', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        null,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledTimes(2);
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(2, [], 'org123');
    });

    it('should handle undefined accounts', async () => {
      const contactWithUndefinedAccounts = { ...mockClient.primaryContact, accounts: undefined };

      await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        contactWithUndefinedAccounts as any,
        null,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledWith([], 'org123');
    });
  });

  describe('circular dependency handling', () => {
    it('should handle forwardRef dependencies correctly', () => {
      expect(service).toBeDefined();
      // Note: Services are private in v2, just check service is defined
    });
  });

  describe('edge cases and null checks', () => {
    it('should handle interview with client as string ID', async () => {
      const interviewWithStringClient = {
        ...mockInterview,
        client: 'client123',
      };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithStringClient),
      } as any);

      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' });
    });

    it('should handle empty secondary contact object', async () => {
      const clientWithEmptySecondary = {
        ...mockClient,
        secondaryContact: {},
      };
      clientsService.findOne.mockResolvedValue(clientWithEmptySecondary as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          coapplicant: false,
        }),
        mockSession,
      );
    });

    it('should handle null envelope documents response', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue(null as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          files: mockFiles,
        }),
      );
    });

    it('should handle envelope documents with mixed types', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: [
          'advisory-agreement.pdf', // This should match the mock file and filter it out
          { name: 'object-doc.pdf' },
          null,
          undefined,
          { name: null },
        ],
      } as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      const addAdvisorCall = docusignService.addAdvisorDocuments.mock.calls[0][0];
      expect(addAdvisorCall.files).toHaveLength(0);
    });

    it('should handle missing fileUploadsNo in client data', async () => {
      const clientWithoutFileUploads = { ...mockClient, fileUploadsNo: undefined };
      clientsService.findOne.mockResolvedValue(clientWithoutFileUploads as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          previousDocumentUploadNo: 0,
        }),
      );
    });
  });

  describe('upload', () => {
    const mockUploadFiles = [
      {
        originalname: 'test-document.pdf',
        buffer: Buffer.from('test content'),
        mimetype: 'application/pdf',
      } as Express.Multer.File,
    ];

    const mockUploadDto: UploadDocumentDto = {
      documentName: 'test-document',
      accountId: 'account123',
      feature: AccountFeaturesEnum.MoneyLink,
    };

    beforeEach(() => {
      // Mock session handling
      connection.startSession.mockResolvedValue(mockSession as any);
      
      // Mock interview lookup
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      
      // Mock client lookup
      clientsService.findOne.mockResolvedValue(mockClient as any);
      
      // Mock DocuSign services
      docusignService.addClientDocuments.mockResolvedValue(undefined);
      
      // Mock database updates
      interviewModel.updateOne.mockResolvedValue({ acknowledged: true } as any);
      clientsService.update.mockResolvedValue(undefined);
      
      // Mock queue
      clientQueue.add.mockResolvedValue(undefined);
    });

    it('should upload documents to existing envelope', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      const result = await service.upload('interview123', mockUploadFiles, mockUploadDto);

      expect(result).toBe('Document successfully uploaded.');
      expect(docusignService.addClientDocuments).toHaveBeenCalledWith({
        organisationId: 'org123',
        advisorId: 'advisor123',
        envelopeId: 'existing123',
        files: mockUploadFiles,
      });
      expect(interviewModel.updateOne).toHaveBeenCalled();
      expect(clientsService.update).toHaveBeenCalledWith(
        'client123',
        {
          fileUploadsNo: 3, // mockClient.fileUploadsNo + 1
          status: ClientStatusEnum.Sent,
        },
        mockSession,
      );
    });

    it('should create envelope if not exists', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutEnvelope),
      } as any);
      
      // Mock the createDocusignEnvelope method
      jest.spyOn(service, 'createDocusignEnvelope').mockResolvedValue('new-envelope123');

      await service.upload('interview123', mockUploadFiles, mockUploadDto);

      expect(service.createDocusignEnvelope).toHaveBeenCalledWith('interview123', mockSession);
      expect(docusignService.addClientDocuments).toHaveBeenCalledWith({
        organisationId: 'org123',
        advisorId: 'advisor123',
        envelopeId: 'new-envelope123',
        files: mockUploadFiles,
      });
    });

    it('should handle upload without DTO', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      const result = await service.upload('interview123', mockUploadFiles);

      expect(result).toBe('Document successfully uploaded.');
      expect(docusignService.addClientDocuments).toHaveBeenCalled();
      expect(interviewModel.updateOne).not.toHaveBeenCalled();
      expect(clientsService.update).not.toHaveBeenCalled();
    });

    it('should handle transaction management', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      await service.upload('interview123', mockUploadFiles, mockUploadDto);

      expect(connection.startSession).toHaveBeenCalled();
      expect(mockSession.startTransaction).toHaveBeenCalled();
      expect(mockSession.commitTransaction).toHaveBeenCalled();
      expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('should throw error when interview not found', async () => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.upload('interview123', mockUploadFiles, mockUploadDto)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should handle database update errors and rollback transaction', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);
      
      const updateError = new Error('Database update failed');
      clientsService.update.mockRejectedValue(updateError);

      await expect(service.upload('interview123', mockUploadFiles, mockUploadDto)).rejects.toThrow();
      expect(mockSession.abortTransaction).toHaveBeenCalled();
      expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('should add client queue job', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123', isPrimary: true };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      await service.upload('interview123', mockUploadFiles, mockUploadDto);

      expect(clientQueue.add).toHaveBeenCalledWith(
        'update_last_contact_activity_timestamp',
        {
          clientId: 'client123',
          isPrimary: true,
        },
      );
    });
  });
});