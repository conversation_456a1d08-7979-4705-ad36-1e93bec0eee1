import { Injectable, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import { InterviewV2 } from '../../../schemas/v2/interview.schema';
import { Model, ClientSession, Connection } from 'mongoose';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { isEmpty } from 'lodash';
import { DocusignAccountOwnershipEnum } from 'src/integrations/docusign/docusign.types';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import { AddClientDocumentsDto } from 'src/integrations/docusign/dto/add-client-documents.dto';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { Account } from 'src/clients/schemas/clients.schema';
import { AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { InterviewDocumentsService } from '../documents/interview-documents.service';
import { ConfigService } from '@nestjs/config';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { CRMEnum, CRMType } from 'src/shared/types/integrations';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { RedtailAccountOwnershipEnum, RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { fdob } from 'src/utils/formate-dob';
import { InterviewData, InterviewDataWithCrmInfo } from 'src/interviews/types/v1/interview-data.type';
import { InterviewTemplatesV2Service } from 'src/interview-templates/services/v2/interview-templates.service';
import { PdfDataProviderFactory } from '../../../services/pdf-data/pdf-data-provider.factory';
import { UploadDocumentDto } from '../../../dto/v1/upload-document.dto';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';

@Injectable()
export class InterviewEnvelopeService {
  constructor(
    @Inject(forwardRef(() => DocusignService)) private readonly docusignService: DocusignService,
    @Inject(forwardRef(() => ClientsV2Service)) private readonly clientsService: ClientsV2Service,
    @Inject(forwardRef(() => OrganisationsService)) private readonly organisationsService: OrganisationsService,
    private readonly interviewTemplateService: InterviewTemplatesV2Service,
    @InjectModel(InterviewV2.name) private readonly interviewModel: Model<InterviewV2>,
    @InjectConnection() private readonly connection: Connection,
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
    private readonly pdfDataProviderFactory: PdfDataProviderFactory,
    @Inject(forwardRef(() => AdvisorsCrudService)) private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsCrmService)) private readonly advisorsCrmService: AdvisorsCrmService,
    @Inject(forwardRef(() => AdvisorsDocusignService)) private readonly advisorsDocusignService: AdvisorsDocusignService,
    @Inject(forwardRef(() => InterviewDocumentsService)) private readonly documentsService: InterviewDocumentsService,
    private readonly configService: ConfigService,
    private readonly clsService: ClsService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async createDocusignEnvelope(interviewId: string, session?: ClientSession): Promise<string> {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const { client, envelopeId } = interview;
    const clientId = client.toString();

    const { organisationId, primaryAdvisor, primaryContact, secondaryContact } =
      await this.clientsService.findOne({ _id: clientId });

    const entityIds = {
      advisorId: primaryAdvisor.id.toString(),
      organisationId: organisationId.toString(),
    };

    const { accountOwnership } = await this.docusignService.getAccountInfo(entityIds);

    const isSchwabOwnedDocusignAccount =
      accountOwnership === DocusignAccountOwnershipEnum.Schwab;

    const schwabAdvisoryFiles = [];

    const advisorIntegrationConfig = await this.advisorsDocusignService.findDocusignIntegration(entityIds.advisorId);
    const organisationIntegrationConfig = await this.organisationsService.findDocusignIntegration(entityIds.organisationId);

    const integrationConfig = advisorIntegrationConfig ?? organisationIntegrationConfig;

    const createEnvelopeDto: CreateEnvelopeDto = {
      ...entityIds,
      primaryAdvisorName: `${primaryAdvisor.firstName} ${primaryAdvisor.lastName}`,
      primaryAdvisorEmail: primaryAdvisor.email,
      primaryAdvisorPhone: primaryAdvisor.mobile,
      applicant: {
        name: `${primaryContact.firstName} ${primaryContact.lastName}`,
        email: primaryContact.email,
        phone: primaryContact.mobile,
      },
      coapplicant: !isEmpty(secondaryContact) && {
        name: `${secondaryContact?.firstName} ${secondaryContact?.lastName}`,
        email: secondaryContact?.email,
        phone: secondaryContact?.mobile,
      },
      envelopeId,
      isSchwabOwnedDocusignAccount,
      shouldSend: false,
      webhookUrl: this.configService.get<string>('DOCUSIGN_STATUS_WEBHOOK_URI'),
      files: schwabAdvisoryFiles,
      schwabTemplateId: integrationConfig?.schwabTemplateId
    };

    const createdEnvelopeId = await this.docusignService.createEnvelope(
      createEnvelopeDto,
      session
    );

    const now = new Date();
    await this.interviewModel.updateMany(
      { client },
      {
        $set: {
          envelopeId: createdEnvelopeId,
          updatedAt: now,
        },
      },
      { session }
    );

    return createdEnvelopeId;
  }

  async prepareDocusignEnvelope(interviewId: string, session: ClientSession) {
    try {
      const interview = await this.interviewModel.findById(interviewId).session(session);

      if (!interview) {
        throw new Error(`Interview ${interviewId} not found`);
      }

      const { client } = interview;
      if (!client) {
        throw new Error(`Client not found for interview ${interviewId}`);
      }

      const clientData = await this.clientsService.findOne({ _id: interview.client }, session);
      const { primaryContact, secondaryContact, primaryAdvisor } = clientData;

      const clientId = interview.client.toString();
      const organisationId = clientData.organisationId.toString();
      const advisorId = primaryAdvisor.id.toString();

      let envelopeId = interview.envelopeId;

      if (interview.docusignSelected) {
        const envelopeExists = !!envelopeId;
        if (!envelopeExists) {
          try {
            envelopeId = await this.createDocusignEnvelope(interviewId, session);
          } catch (error) {
            this.logger.error(`Failed to create DocuSign envelope: ${error.message}`);
            throw error;
          }
        }

        const interviews = await this.interviewModel.find(
          { client: clientId },
          null,
          { session },
        );

        const bothInterviewsFinished = interviews.every(
          (interview) => interview.isComplete,
        );

        if (bothInterviewsFinished) {
          const { accountOwnership } = await this.docusignService.getAccountInfo({
            organisationId,
            advisorId,
          });

          // CRITICAL FIX: Use PDF Data Provider for V2 compatibility
          this.logger.info(`[InterviewEnvelopeService] Creating V2 PDF data provider for interview ${interviewId}`);
          const pdfDataProvider = this.pdfDataProviderFactory.create('v2');
          const interviewData = await pdfDataProvider.getInterviewDataForPdf(interviewId, session);
          
          this.logger.info(`[InterviewEnvelopeService] PDF data provider returned - interview isPrimary: ${interviewData.interview.isPrimary}, organisation: ${interviewData.organisation?.name}`);

          const existingEnvelopFiles = await this.docusignService.getEnvelopDocuments({
            envelopeId,
            organisationId,
            advisorId
          }) || { envelopeDocuments: [] };

          const existingFilenames = existingEnvelopFiles.envelopeDocuments?.map(doc =>
            typeof doc === 'string' ? doc : doc?.name
          ).filter(Boolean) || [];

          const files: Express.Multer.File[] = await this.getEnvelopeFiles(
            accountOwnership,
            organisationId,
            primaryContact,
            secondaryContact,
          ) || [];

          const filesToUpload = files.filter(file =>
            file?.originalname && !existingFilenames.includes(file.originalname)
          );

          const addAdvisorDocumentsDto: AddAdvisorDocumentsDto = {
            advisorId,
            organisationId,
            envelopeId,
            files: filesToUpload,
            interviewData,
            previousDocumentUploadNo: clientData?.fileUploadsNo || 0,
          };
          
          this.logger.info(`[InterviewEnvelopeService] About to call DocuSign addAdvisorDocuments with ${filesToUpload.length} files and interview data containing isPrimary: ${interviewData.interview.isPrimary}`);
          await this.docusignService.addAdvisorDocuments(addAdvisorDocumentsDto);
          this.logger.info(
            `Added advisor documents to envelope for client ${clientId}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error in prepareDocusignEnvelope: ${error.message}`);
      throw error;
    }
  }

  async getEnvelopeFiles(
    accountOwnership: DocusignAccountOwnershipEnum,
    organisationId: string,
    primaryContact: EnrichedContact,
    secondaryContact: EnrichedContact,
  ): Promise<Express.Multer.File[]> {
    const advisoryFiles: Express.Multer.File[] = [];

    // Get the account opening advisory files for the applicant and co-applicant
    const applicantAccountFiles = await this.documentsService.getAccountOpeningAdvisoryFiles(
      primaryContact?.accounts || [],
      organisationId,
    );

    const coApplicantAccountFiles = await this.documentsService.getAccountOpeningAdvisoryFiles(
      secondaryContact?.accounts || [],
      organisationId,
    );

    advisoryFiles.push(
      ...(await this.documentsService.getFilesFromS3(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        organisationId,
      )),
    );

    return [
      ...applicantAccountFiles,
      ...coApplicantAccountFiles,
      ...advisoryFiles,
    ];
  }

  // REMOVED: Legacy V2 data transformation logic
  // Now handled by PdfDataProviderFactory with proper V2-to-V1 data transformation

  // CRITICAL FIX: Add missing completion processing method
  async processInterviewCompletion(interviewId: string, session?: ClientSession): Promise<void> {
    this.logger.info(`Processing completion for V2 interview ${interviewId}`);
    
    try {
      // Trigger the existing envelope preparation logic
      await this.prepareDocusignEnvelope(interviewId, session);
    } catch (error) {
      this.logger.error(`Failed to process completion for interview ${interviewId}: ${error.message}`);
      throw error;
    }
  }

  async upload(
    id: string,
    files: Express.Multer.File[],
    dto?: UploadDocumentDto,
    session?: ClientSession,
  ): Promise<string> {
    let transactionToUse = session;
    let shouldManageTransaction = false;

    if (!session) {
      transactionToUse = await this.connection.startSession();
      await transactionToUse.startTransaction();
      shouldManageTransaction = true;
    }

    try {
      const interview = await this.interviewModel.findById(id).session(transactionToUse);

      if (!interview) {
        throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
      }

      let envelopeId = interview.envelopeId;
      const { client } = interview;
      const clientId = client.toString();

      const envelopeExists = !!envelopeId;
      if (!envelopeExists) {
        envelopeId = await this.createDocusignEnvelope(id, transactionToUse);
      }

      // Upload the files to the envelope
      const { organisationId, primaryAdvisor } =
        await this.clientsService.findOne({ _id: clientId }, transactionToUse);

      const entityIds = {
        organisationId: organisationId.toString(),
        advisorId: primaryAdvisor.id.toString(),
      };

      const addClientDocumentsDto: AddClientDocumentsDto = {
        ...entityIds,
        envelopeId,
        files,
      };

      await this.docusignService.addClientDocuments(addClientDocumentsDto);

      // Update the database after documents are uploaded
      const { documentName, accountId, feature } = dto || {};

      const query = { _id: id, 'documents.name': documentName };
      const filter = { 'elem.name': documentName };

      if (!isEmpty(accountId)) {
        query['documents.accountId'] = accountId;
        filter['elem.accountId'] = accountId;
      }

      if (!isEmpty(feature)) {
        query['documents.feature'] = feature;
        filter['elem.feature'] = feature;
      }

      if (!!documentName && documentName.length > 0) {
        const { fileUploadsNo } = await this.clientsService.findOne(
          { _id: clientId },
          transactionToUse,
        );

        const now = new Date();
        const interviewUpdatePromise = this.interviewModel.updateOne(
          query,
          {
            $set: {
              'documents.$[elem].updatedAt': now,
            },
          },
          {
            arrayFilters: [filter],
            session: transactionToUse,
          },
        );

        const clientUpdatePromise = this.clientsService.update(
          clientId,
          {
            fileUploadsNo: fileUploadsNo + 1,
            status: ClientStatusEnum.Sent,
          },
          transactionToUse,
        );

        await Promise.all([interviewUpdatePromise, clientUpdatePromise]);
      }

      this.clientQueue.add(ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP, {
        clientId,
        isPrimary: true, // For v2, we'll default to true or get from interview template
      });

      if (shouldManageTransaction) {
        await transactionToUse.commitTransaction();
      }

      return 'Document successfully uploaded.';
    } catch (error) {
      if (shouldManageTransaction) {
        await transactionToUse.abortTransaction();
      }
      throw new HttpException(
        error.message,
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } finally {
      if (shouldManageTransaction) {
        await transactionToUse.endSession();
      }
    }
  }
}