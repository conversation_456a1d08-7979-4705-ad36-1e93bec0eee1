import { Injectable, Logger } from '@nestjs/common';
import { ClientContext, AccountContext, InterviewContext } from 'src/interviews/types/v2/client-context.interface';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { OrganisationsService } from 'src/organisations/organisations.service';

@Injectable()
export class ClientContextService {
  private readonly logger = new Logger(ClientContextService.name);
  
  constructor(
    private readonly clientsService: ClientsV2Service,
    private readonly organisationsService: OrganisationsService,
  ) {}

  /**
   * Build complete interview context from client ID
   */
  async buildInterviewContext(
    clientId: string,
    organisationId: string,
    currentAccountId?: string
  ): Promise<InterviewContext> {
    // Get client data using client service
    const client = await this.clientsService.findOne({ 
      _id: clientId, 
      organisationId 
    });
    
    // Get organization data using organization service
    const organisation = await this.organisationsService.findOne(organisationId);
    
    const clientContext = this.buildClientContext(client, organisation);
    const accountContext = this.buildAccountContext(client, currentAccountId);
    
    return {
      client: clientContext,
      accounts: accountContext
    };
  }

  /**
   * Build client-level context data
   */
  private buildClientContext(client: any, organisation: any): ClientContext {
    return {
      status: {
        isExistingClient: !!client.crmClientId || !!client.primaryContact?.crmClientId,
        skipContactInterview: client.primaryContact?.skipContactInterview || false,
        readyToSend: client.readyToSend || false,
        featuresSelected: client.featuresSelected || false,
      },
      
      features: {
        docusignSelected: client.docusignSelected || false,
        sendAdv2b: client.sendAdv2b || false,
        doClientProfiling: client.doClientProfiling || false,
      },
      
      contact: {
        firstName: client.primaryContact?.firstName || '',
        lastName: client.primaryContact?.lastName || '',
        email: client.primaryContact?.email || '',
        hasCrmId: !!client.primaryContact?.crmClientId,
      },
      
      advisor: {
        firstName: client.primaryAdvisor?.firstName || '',
        lastName: client.primaryAdvisor?.lastName || '',
        tier: this.determineAdvisorTier(client, organisation),
      },
      
      organization: {
        id: client.organisationId,
        tier: this.determineOrganizationTier(organisation),
      },
    };
  }

  /**
   * Build account-level context data
   */
  private buildAccountContext(client: any, currentAccountId?: string): AccountContext {
    const accounts = client.accounts || client.primaryContact?.accounts || [];
    const accountTypes = accounts.map(acc => acc.type || acc.accountType);
    
    // Debug logging (production: remove or change to debug level)
    this.logger.debug('Building account context for client');
    this.logger.debug(`Extracted accounts: ${JSON.stringify(accounts)}`);
    this.logger.debug(`Account types: ${JSON.stringify(accountTypes)}`);
    this.logger.debug(`Has retirement check: ${accountTypes.some(type => this.isRetirementAccount(type))}`);
    
    // Current account context (only if processing account-specific page)
    let currentAccountContext;
    if (currentAccountId) {
      const currentAccount = accounts.find(acc => acc.accountId === currentAccountId);
      if (currentAccount) {
        currentAccountContext = {
          accountId: currentAccount.accountId,
          ownership: this.determineOwnership(currentAccount),
          label: currentAccount.accountLabel || `${currentAccount.type || currentAccount.accountType} Account`,
          advisoryRate: currentAccount.advisoryRate,
        };
      }
    }

    const accountContext = {
      count: accounts.length,
      types: accountTypes,
      hasRetirement: accountTypes.some(type => this.isRetirementAccount(type)),
      hasJoint: accounts.some(acc => this.isJointAccount(acc)),
      hasIndividual: accounts.some(acc => this.isIndividualAccount(acc)),
      hasTrust: accounts.some(acc => this.isTrustAccount(acc)),
      hasBusiness: accounts.some(acc => this.isBusinessAccount(acc)),
      current_account: currentAccountContext,
    };
    
    this.logger.debug(`Final account context: ${JSON.stringify(accountContext)}`);
    return accountContext;
  }

  /**
   * Helper methods for account classification
   */
  private isRetirementAccount(accountType: string): boolean {
    const retirementTypes = ['ira', 'roth_ira', '401k', 'sep_ira', 'simple_ira', 'traditional_ira'];
    return retirementTypes.includes(accountType.toLowerCase());
  }

  private requiresBeneficiaries(accountType: string): boolean {
    // Most retirement accounts require beneficiaries
    return this.isRetirementAccount(accountType);
  }

  private isJointAccount(account: any): boolean {
    return account.ownership?.toLowerCase().includes('joint') || 
           (account.type || account.accountType)?.toLowerCase().includes('joint');
  }

  private isIndividualAccount(account: any): boolean {
    return account.ownership?.toLowerCase() === 'individual' ||
           (!this.isJointAccount(account) && !this.isTrustAccount(account) && !this.isBusinessAccount(account));
  }

  private isTrustAccount(account: any): boolean {
    return account.ownership?.toLowerCase().includes('trust') ||
           (account.type || account.accountType)?.toLowerCase().includes('trust');
  }

  private isBusinessAccount(account: any): boolean {
    return account.ownership?.toLowerCase().includes('business') ||
           (account.type || account.accountType)?.toLowerCase().includes('business') ||
           (account.type || account.accountType)?.toLowerCase().includes('corporate');
  }

  private determineOwnership(account: any): string {
    if (account.ownership) return account.ownership;
    
    // Infer from account type if ownership not explicitly set
    if (this.isJointAccount(account)) return 'joint';
    if (this.isTrustAccount(account)) return 'trust';
    if (this.isBusinessAccount(account)) return 'business';
    return 'individual';
  }

  /**
   * Placeholder methods for tier determination (can be enhanced later)
   */
  private determineAdvisorTier(client: any, organisation: any): string | undefined {
    // TODO: Implement advisor tier logic based on:
    // - Client AUM, transaction volume, complexity
    // - Organization configuration
    // - Advisor role/seniority
    return undefined;
  }

  private determineOrganizationTier(organisation: any): string | undefined {
    // TODO: Implement organization tier logic based on:
    // - Subscription level
    // - Feature flags
    // - Organization size/type
    return undefined;
  }
}