import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
import { SubmitPageV2Dto } from 'src/interviews/dto/v2/submit-page-v2.dto';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { CRMService } from 'src/integrations/crm/crm.service';
import { InterviewV2QueueService } from './queue/interview-queue.service';
import { InterviewNotificationService } from './notifications/interview-notification.service';
import { InterviewEnvelopeService } from './envelope/interview-envelope.service';
import { InterviewPageInstanceStatusEnum, InterviewPageSyncStatusEnum } from 'src/interviews/types/v2/interview-v2-queue-job.enum';
import { InterviewV2AuditService } from './audit/interview-audit.service';

/**
 * Core service for V2 interview operations
 * Used by the processor for heavy operations
 */
@Injectable()
export class InterviewV2Service {
  private readonly logger = new Logger(InterviewV2Service.name);

  constructor(
    @InjectModel(InterviewV2.name)
    private readonly interviewModel: Model<InterviewV2>,
    
    @InjectModel(InterviewPageInstanceV2.name)
    private readonly pageInstanceModel: Model<InterviewPageInstanceV2>,
    
    private readonly crmService: CRMService,
    private readonly queueService: InterviewV2QueueService,
    private readonly notificationService: InterviewNotificationService,
    private readonly envelopeService: InterviewEnvelopeService,
    
    private readonly auditService: InterviewV2AuditService,
  ) {}

  /**
   * Update interview page sync status after CRM sync
   */
  async updateInterviewPageStatus(
    interviewId: string,
    pageName: string,
    syncStatus: string,
    session?: ClientSession,
  ): Promise<void> {
    // When called from the processor after CRM sync, this updates the sync status
    await this.pageInstanceModel.findOneAndUpdate(
      { interviewId, pageName },
      { 
        $set: { 
          syncStatus: InterviewPageSyncStatusEnum.SYNCED,
          updatedAt: new Date(),
        }
      },
      { session }
    );
  }

  /**
   * Find interview by ID
   */
  async findById(
    interviewId: string,
    session?: ClientSession,
  ): Promise<InterviewV2 | null> {
    return this.interviewModel.findById(interviewId).session(session);
  }


  /**
   * Send notification for interview
   */
  async sendNotification(
    interviewId: string,
    notificationType: string,
    session?: ClientSession,
  ): Promise<void> {
    // V2 notification service doesn't use notification type parameter
    return this.notificationService.sendNotification(
      interviewId,
      session!,
    );
  }

  /**
   * Prepare DocuSign envelope
   */
  async prepareDocusignEnvelope(
    interviewId: string,
    session?: ClientSession,
  ): Promise<void> {
    return this.envelopeService.prepareDocusignEnvelope(interviewId, session);
  }

  /**
   * Mark interview as complete (alias for processor)
   */
  async markComplete(
    interviewId: string,
    session?: ClientSession,
  ): Promise<void> {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) {
      throw new Error(`Interview ${interviewId} not found`);
    }
    
    await this.interviewModel.findByIdAndUpdate(
      interviewId,
      {
        $set: {
          status: InterviewPageInstanceStatusEnum.COMPLETED,
          isComplete: true,
          completedAt: new Date(),
        },
      },
      { session }
    );

    // Audit interview completion
    await this.auditService.logInterviewCompleted({
      interviewId,
      clientId: interview.client.toString(),
      organisationId: interview.organisationId.toString(),
    });

    this.logger.log(`V2 Interview ${interviewId} marked as complete`);
  }

  /**
   * Mark interview as abandoned (for timeout or explicit abandonment)
   */
  async markAbandoned(
    interviewId: string,
    reason?: string,
    session?: ClientSession,
  ): Promise<void> {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) {
      throw new Error(`Interview ${interviewId} not found`);
    }
    
    // Get completion percentage
    const totalPages = await this.pageInstanceModel.countDocuments({ interviewId });
    const completedPages = await this.pageInstanceModel.countDocuments({ 
      interviewId, 
      status: InterviewPageInstanceStatusEnum.COMPLETED 
    });
    const completionPercentage = totalPages > 0 ? Math.round((completedPages / totalPages) * 100) : 0;
    
    // Get last visited page
    const lastPage = await this.pageInstanceModel
      .findOne({ interviewId, visitOrder: { $gt: 0 } })
      .sort({ visitOrder: -1 })
      .session(session);
    
    await this.interviewModel.findByIdAndUpdate(
      interviewId,
      {
        $set: {
          status: 'abandoned',
          abandonedAt: new Date(),
          abandonmentReason: reason,
        },
      },
      { session }
    );

    // Audit interview abandonment
    await this.auditService.logInterviewAbandoned({
      interviewId,
      clientId: interview.client.toString(),
      organisationId: interview.organisationId.toString(),
      lastPageId: lastPage?.pageId,
      lastPageName: lastPage?.pageName,
      completionPercentage,
      metadata: { reason },
    });

    this.logger.log(`V2 Interview ${interviewId} marked as abandoned: ${reason || 'No reason provided'}`);
  }
  
  /**
   * Complete interview and trigger DocuSign envelope creation
   */
  async completeInterview(interviewId: string, session?: ClientSession): Promise<void> {
    this.logger.log(`Completing V2 interview ${interviewId}`);
    
    // Trigger DocuSign envelope creation and final processing
    await this.envelopeService.processInterviewCompletion(interviewId, session);
    
    // Mark interview as completed in database
    await this.markComplete(interviewId, session);
  }

  /**
   * Send completion notification to advisor and client
   */
  async sendCompletionNotification(interviewId: string, session?: ClientSession): Promise<void> {
    this.logger.log(`Sending completion notification for V2 interview ${interviewId}`);
    
    // Send notification via notification service
    await this.notificationService.sendInterviewCompletionNotification(interviewId, session);
  }

}