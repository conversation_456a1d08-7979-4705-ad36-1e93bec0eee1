import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  NotFoundException,
  BadRequestException,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { ApiTags, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { SubmitPageV2Dto } from '../../dto/v2/submit-page-v2.dto';
import { NavigationStateV2Dto } from '../../dto/v2/navigation-state-v2.dto';
import { PageNavigationResultV2Dto } from '../../dto/v2/page-navigation-result-v2.dto';
import { InterviewResponseV2Dto } from '../../dto/v2/interview-response-v2.dto';
import { InterviewCompletionValidationDto } from '../../dto/v2/interview-completion-validation.dto';
import { InterviewCoreService } from '../../services/v2/core/interview-core.service';
import { InterviewV2NavigationService } from '../../services/v2/navigation/interview-navigation.service';
import { InterviewNotificationService } from '../../services/v2/notifications/interview-notification.service';
import { InterviewCompletionValidationService } from '../../services/v2/validation/interview-completion-validation.service';
import { InterviewDocumentsService } from '../../services/v2/documents/interview-documents.service';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller({ path: 'interviews', version: '2' })
@ApiTags('Interviews V2')
export class InterviewsV2Controller {
  constructor(
    private readonly coreService: InterviewCoreService,
    private readonly navigationService: InterviewV2NavigationService,
    private readonly notificationService: InterviewNotificationService,
    private readonly validationService: InterviewCompletionValidationService,
    private readonly documentsService: InterviewDocumentsService,
    @InjectConnection() private readonly connection: Connection,
  ) {}


  @Get(':interviewId')
  @ApiResponse({ type: InterviewResponseV2Dto })
  async findOne(@Param('interviewId') interviewId: string) {
    const interview = await this.coreService.findById(interviewId);
    return interview;
  }

  @Get('/client/:clientId')
  async findByClientId(@Param('clientId') clientId: string) {
    const interviews = await this.coreService.find({ client: clientId });
    return interviews;
  }

  @Post(':interviewId/pages/submit')
  @ApiResponse({ type: PageNavigationResultV2Dto })
  async submitPage(
    @Param('interviewId') interviewId: string,
    @Body() dto: SubmitPageV2Dto,
  ) {
    return this.navigationService.submitPageAndNavigate(interviewId, dto);
  }

  @Get(':interviewId/navigation/state')
  @ApiResponse({ type: NavigationStateV2Dto })
  async getNavigationState(@Param('interviewId') interviewId: string) {
    return this.navigationService.getCurrentNavigationState(interviewId);
  }

  @Post(':interviewId/navigation/back')
  @ApiResponse({ type: PageNavigationResultV2Dto })
  async navigateBack(@Param('interviewId') interviewId: string) {
    return this.navigationService.navigateBack(interviewId);
  }

  @Get(':interviewId/pages/:pageId')
  async getPage(
    @Param('interviewId') interviewId: string,
    @Param('pageId') pageId: string,
  ) {
    return this.navigationService.getPageDefinition(interviewId, pageId);
  }

  @Get(':interviewId/validate-completion')
  @ApiResponse({ type: InterviewCompletionValidationDto })
  async validateCompletion(
    @Param('interviewId') interviewId: string,
    @Query('includeDetails') includeDetails?: string,
  ) {
    const includeDetailsFlag = includeDetails === 'true';
    return this.validationService.validateInterviewCompletion(
      interviewId,
      includeDetailsFlag,
    );
  }

  @Post(':interviewId/finish')
  async finish(@Param('interviewId') id: string) {
    return this.coreService.finish(id);
  }

  @Get(':interviewId/sendDesktopInterviewEmail')
  async sendDesktopInterviewEmail(@Param('interviewId') interviewId: string) {
    return this.notificationService.sendDesktopInterviewEmail(interviewId);
  }

  @Get(':interviewId/sendNonCitizenEmail')
  async sendNonCitizenEmail(@Param('interviewId') interviewId: string) {
    return this.notificationService.sendNonCitizenEmail(interviewId);
  }

  @Post(':interviewId/pages/:pageName/upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Document upload for interview page',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Document file to upload',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Document uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        documentId: { type: 'string' },
        processor: { type: 'string' },
        uploadedAt: { type: 'string', format: 'date-time' },
        metadata: {
          type: 'object',
          properties: {
            fileName: { type: 'string' },
            fileSize: { type: 'number' },
            mimeType: { type: 'string' },
          },
        },
        navigation: {
          type: 'object',
          properties: {
            nextPageName: { type: 'string', nullable: true },
            isComplete: { type: 'boolean' },
            branchTaken: { type: 'string' },
          },
        },
      },
    },
  })
  async uploadDocument(
    @Param('interviewId') interviewId: string,
    @Param('pageName') pageName: string,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Start a transaction to ensure atomicity
    const session = await this.connection.startSession();
    session.startTransaction();
    
    try {
      // Get the interview to access template and find page by name
      const interview = await this.coreService.findById(interviewId);
      const template = interview.template as any;
      const pageDefinition = template.pages.find((p: any) => p.pageName === pageName);
      
      if (!pageDefinition || pageDefinition.pageType !== 'document_upload') {
        throw new BadRequestException('Page is not a document upload page');
      }

      // Process the document upload
      const uploadResult = await this.documentsService.processV2DocumentUpload(
        interviewId,
        pageDefinition,
        file,
        session
      );

      // For document upload pages, automatically complete the page and navigate
      // This is because uploading the document IS the answer to the page
      const navigationResult = await this.navigationService.submitPageAndNavigate(
        interviewId,
        {
          pageName: pageName,
          answers: {
            documentUploaded: true,
            documentId: uploadResult.documentId,
            uploadedAt: uploadResult.uploadedAt
          }
        },
        session
      );

      // Commit the transaction
      await session.commitTransaction();

      // Return combined result
      return {
        ...uploadResult,
        navigation: navigationResult
      };
    } catch (error) {
      // Rollback on any error
      await session.abortTransaction();
      throw error;
    } finally {
      // Always end the session
      await session.endSession();
    }
  }

  @Get(':interviewId/documents/status')
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Document upload status for interview',
    schema: {
      type: 'object',
      properties: {
        uploads: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              documentId: { type: 'string' },
              processor: { type: 'string' },
              uploadedAt: { type: 'string', format: 'date-time' },
              pageId: { type: 'string' },
              metadata: {
                type: 'object',
                properties: {
                  fileName: { type: 'string' },
                  fileSize: { type: 'number' },
                  mimeType: { type: 'string' },
                },
              },
            },
          },
        },
        totalUploads: { type: 'number' },
      },
    },
  })
  async getDocumentStatus(@Param('interviewId') interviewId: string) {
    return this.documentsService.getV2DocumentUploadStatus(interviewId);
  }
}