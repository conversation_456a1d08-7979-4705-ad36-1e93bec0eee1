import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { InterviewsV2Controller } from './interviews-v2.controller';
import { InterviewCoreService } from '../../services/v2/core/interview-core.service';
import { InterviewV2NavigationService } from '../../services/v2/navigation/interview-navigation.service';
import { InterviewNotificationService } from '../../services/v2/notifications/interview-notification.service';
import { InterviewV2QueueService } from '../../services/v2/queue/interview-queue.service';
import { InterviewCompletionValidationService } from '../../services/v2/validation/interview-completion-validation.service';

describe('InterviewsV2Controller - Validation Features', () => {
  let controller: InterviewsV2Controller;
  let coreService: jest.Mocked<InterviewCoreService>;
  let navigationService: jest.Mocked<InterviewV2NavigationService>;
  let notificationService: jest.Mocked<InterviewNotificationService>;
  let queueService: jest.Mocked<InterviewV2QueueService>;
  let validationService: jest.Mocked<InterviewCompletionValidationService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InterviewsV2Controller],
      providers: [
        {
          provide: InterviewCoreService,
          useValue: {
            findById: jest.fn(),
            markComplete: jest.fn(),
          },
        },
        {
          provide: InterviewV2NavigationService,
          useValue: {
            submitPageAndNavigate: jest.fn(),
            getCurrentNavigationState: jest.fn(),
            navigateBack: jest.fn(),
            getPageDefinition: jest.fn(),
          },
        },
        {
          provide: InterviewNotificationService,
          useValue: {
            sendDesktopInterviewEmail: jest.fn(),
            sendNonCitizenEmail: jest.fn(),
          },
        },
        {
          provide: InterviewV2QueueService,
          useValue: {
            queueInterviewCompletion: jest.fn(),
          },
        },
        {
          provide: InterviewCompletionValidationService,
          useValue: {
            validateInterviewCompletion: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<InterviewsV2Controller>(InterviewsV2Controller);
    coreService = module.get(InterviewCoreService);
    navigationService = module.get(InterviewV2NavigationService);
    notificationService = module.get(InterviewNotificationService);
    queueService = module.get(InterviewV2QueueService);
    validationService = module.get(InterviewCompletionValidationService);
  });

  describe('validateCompletion', () => {
    it('should validate interview completion with details', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [],
        requiredPages: ['name', 'address'],
        completedPages: ['name', 'address'],
        unsyncedPages: [],
        pageDetails: [
          {
            pageId: 'page1',
            pageName: 'name',
            status: 'completed',
            syncStatus: 'synced',
            isRequired: true,
            visitOrder: 1,
          },
        ],
        summary: 'Interview is ready for completion',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      const result = await controller.validateCompletion('interview123', 'true');

      expect(validationService.validateInterviewCompletion).toHaveBeenCalledWith(
        'interview123',
        true
      );
      expect(result).toEqual(mockValidation);
    });

    it('should validate interview completion without details', async () => {
      const mockValidation = {
        isValid: false,
        completionPercentage: 50,
        errors: [
          {
            type: 'UNSYNCED_PAGE' as const,
            pageId: 'page1',
            pageName: 'name',
            message: 'Page not synced',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address'],
        completedPages: ['name'],
        unsyncedPages: ['name'],
        pageDetails: [],
        summary: 'Interview 50% complete',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      const result = await controller.validateCompletion('interview123', 'false');

      expect(validationService.validateInterviewCompletion).toHaveBeenCalledWith(
        'interview123',
        false
      );
      expect(result).toEqual(mockValidation);
    });

    it('should default to not including details when query param is undefined', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [],
        requiredPages: ['name'],
        completedPages: ['name'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      const result = await controller.validateCompletion('interview123');

      expect(validationService.validateInterviewCompletion).toHaveBeenCalledWith(
        'interview123',
        false
      );
      expect(result).toEqual(mockValidation);
    });
  });

  describe('finish', () => {
    const mockInterview = {
      _id: 'interview123',
      client: 'client123',
      isComplete: false,
      status: 'pending',
    };

    beforeEach(() => {
      coreService.findById.mockResolvedValue(mockInterview as any);
    });

    it('should successfully finish valid interview', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [],
        requiredPages: ['name', 'address'],
        completedPages: ['name', 'address'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);
      queueService.queueInterviewCompletion.mockResolvedValue(undefined);
      coreService.markComplete.mockResolvedValue(undefined);

      const result = await controller.finish('interview123');

      expect(coreService.findById).toHaveBeenCalledWith('interview123');
      expect(validationService.validateInterviewCompletion).toHaveBeenCalledWith('interview123', false);
      expect(queueService.queueInterviewCompletion).toHaveBeenCalledWith('interview123');
      expect(coreService.markComplete).toHaveBeenCalledWith('interview123');

      expect(result).toEqual({
        message: 'Interview completion initiated',
        interviewId: 'interview123',
        validation: {
          completionPercentage: 100,
          warnings: [],
        },
      });
    });

    it('should finish interview with warnings and log them', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [
          {
            type: 'PAGE_FILLED_BUT_SKIPPABLE' as const,
            pageId: 'page1',
            pageName: 'employment',
            message: 'Page was completed but is now skippable',
          },
        ],
        requiredPages: ['name', 'address'],
        completedPages: ['name', 'address', 'employment'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion (1 warnings)',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);
      queueService.queueInterviewCompletion.mockResolvedValue(undefined);
      coreService.markComplete.mockResolvedValue(undefined);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const result = await controller.finish('interview123');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Interview interview123 completing with warnings:',
        mockValidation.warnings
      );

      expect((result as any).validation.warnings).toEqual(mockValidation.warnings);

      consoleSpy.mockRestore();
    });

    it('should throw BadRequestException for invalid interview with unsynced pages', async () => {
      const mockValidation = {
        isValid: false,
        completionPercentage: 75,
        errors: [
          {
            type: 'UNSYNCED_PAGE' as const,
            pageId: 'page1',
            pageName: 'address',
            message: 'Page "address" is not yet synced with CRM',
            pageStatus: 'completed',
            syncStatus: 'pending',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address', 'ssn'],
        completedPages: ['name', 'address', 'ssn'],
        unsyncedPages: ['address'],
        pageDetails: [],
        summary: 'Interview 75% complete. Issues: 1 page(s) not synced',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      await expect(controller.finish('interview123')).rejects.toThrow(BadRequestException);

      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();

      try {
        await controller.finish('interview123');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.getResponse()).toEqual({
          message: 'Interview cannot be completed due to validation errors',
          validation: {
            isValid: false,
            errors: mockValidation.errors,
            warnings: mockValidation.warnings,
            summary: mockValidation.summary,
            completionPercentage: 75,
          },
        });
      }
    });

    it('should throw BadRequestException for interview with mandatory pages incomplete', async () => {
      const mockValidation = {
        isValid: false,
        completionPercentage: 33,
        errors: [
          {
            type: 'MANDATORY_PAGE_INCOMPLETE' as const,
            pageId: 'page2',
            pageName: 'address',
            message: 'Required page "address" is not completed',
            pageStatus: 'pending',
            syncStatus: 'pending',
          },
          {
            type: 'MANDATORY_PAGE_INCOMPLETE' as const,
            pageId: 'page3',
            pageName: 'ssn',
            message: 'Required page "ssn" is not completed',
            pageStatus: 'pending',
            syncStatus: 'pending',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address', 'ssn'],
        completedPages: ['name'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview 33% complete. Issues: 2 required page(s) not completed',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      await expect(controller.finish('interview123')).rejects.toThrow(BadRequestException);

      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for pages in progress', async () => {
      const mockValidation = {
        isValid: false,
        completionPercentage: 100,
        errors: [
          {
            type: 'PAGE_IN_PROGRESS' as const,
            pageId: 'page1',
            pageName: 'address',
            message: 'Page "address" is currently syncing, please wait',
            pageStatus: 'completed',
            syncStatus: 'syncing',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address'],
        completedPages: ['name', 'address'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview 100% complete. Issues: 1 page(s) still syncing',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      await expect(controller.finish('interview123')).rejects.toThrow(BadRequestException);

      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for multiple validation error types', async () => {
      const mockValidation = {
        isValid: false,
        completionPercentage: 50,
        errors: [
          {
            type: 'UNSYNCED_PAGE' as const,
            pageId: 'page1',
            pageName: 'name',
            message: 'Page "name" is not yet synced with CRM',
            pageStatus: 'completed',
            syncStatus: 'pending',
          },
          {
            type: 'MANDATORY_PAGE_INCOMPLETE' as const,
            pageId: 'page2',
            pageName: 'address',
            message: 'Required page "address" is not completed',
            pageStatus: 'pending',
            syncStatus: 'pending',
          },
          {
            type: 'PAGE_IN_PROGRESS' as const,
            pageId: 'page3',
            pageName: 'ssn',
            message: 'Page "ssn" is currently syncing',
            pageStatus: 'completed',
            syncStatus: 'syncing',
          },
          {
            type: 'SYNC_FAILED' as const,
            pageId: 'page4',
            pageName: 'employment',
            message: 'Page "employment" failed to sync and must be retried',
            pageStatus: 'completed',
            syncStatus: 'failed',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address', 'ssn', 'employment'],
        completedPages: ['name', 'ssn', 'employment'],
        unsyncedPages: ['name'],
        pageDetails: [],
        summary: 'Interview 50% complete. Issues: 1 required page(s) not completed, 1 page(s) not synced, 1 page(s) still syncing, 1 page(s) failed to sync',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      await expect(controller.finish('interview123')).rejects.toThrow(BadRequestException);

      try {
        await controller.finish('interview123');
      } catch (error) {
        expect(error.getResponse().validation.errors).toHaveLength(4);
        expect(error.getResponse().validation.summary).toContain('1 required page(s) not completed');
      }
    });

    it('should throw NotFoundException if interview does not exist', async () => {
      coreService.findById.mockResolvedValue(null);

      await expect(controller.finish('interview123')).rejects.toThrow(NotFoundException);

      expect(validationService.validateInterviewCompletion).not.toHaveBeenCalled();
      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should return early if interview is already complete', async () => {
      const completedInterview = {
        ...mockInterview,
        isComplete: true,
        status: 'completed',
      };

      coreService.findById.mockResolvedValue(completedInterview as any);

      const result = await controller.finish('interview123');

      expect(result).toEqual(completedInterview);
      expect(validationService.validateInterviewCompletion).not.toHaveBeenCalled();
      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should handle validation service errors gracefully', async () => {
      validationService.validateInterviewCompletion.mockRejectedValue(
        new Error('Validation service error')
      );

      await expect(controller.finish('interview123')).rejects.toThrow('Validation service error');

      expect(queueService.queueInterviewCompletion).not.toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should handle queue service errors after validation', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [],
        requiredPages: ['name'],
        completedPages: ['name'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);
      queueService.queueInterviewCompletion.mockRejectedValue(
        new Error('Queue service error')
      );

      await expect(controller.finish('interview123')).rejects.toThrow('Queue service error');

      expect(validationService.validateInterviewCompletion).toHaveBeenCalled();
      expect(coreService.markComplete).not.toHaveBeenCalled();
    });

    it('should handle core service errors after validation and queuing', async () => {
      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [],
        requiredPages: ['name'],
        completedPages: ['name'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion',
      };

      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);
      queueService.queueInterviewCompletion.mockResolvedValue(undefined);
      coreService.markComplete.mockRejectedValue(
        new Error('Core service error')
      );

      await expect(controller.finish('interview123')).rejects.toThrow('Core service error');

      expect(validationService.validateInterviewCompletion).toHaveBeenCalled();
      expect(queueService.queueInterviewCompletion).toHaveBeenCalled();
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle complex validation scenario with conditional navigation', async () => {
      const mockInterview = {
        _id: 'interview123',
        client: 'client123',
        isComplete: false,
        status: 'pending',
      };

      const mockValidation = {
        isValid: true,
        completionPercentage: 100,
        errors: [],
        warnings: [
          {
            type: 'PAGE_FILLED_BUT_SKIPPABLE' as const,
            pageId: 'page3',
            pageName: 'employment',
            message: 'Page "employment" was completed but is now skippable due to conditional logic',
          },
        ],
        requiredPages: ['name', 'address', 'ssn'], // employment skipped
        completedPages: ['name', 'address', 'employment', 'ssn'],
        unsyncedPages: [],
        pageDetails: [],
        summary: 'Interview is ready for completion (1 warnings)',
      };

      coreService.findById.mockResolvedValue(mockInterview as any);
      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);
      queueService.queueInterviewCompletion.mockResolvedValue(undefined);
      coreService.markComplete.mockResolvedValue(undefined);

      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const result = await controller.finish('interview123');

      expect((result as any).message).toBe('Interview completion initiated');
      expect((result as any).validation.warnings).toHaveLength(1);
      expect((result as any).validation.warnings[0].type).toBe('PAGE_FILLED_BUT_SKIPPABLE');
      expect((result as any).validation.completionPercentage).toBe(100);

      expect(consoleSpy).toHaveBeenCalledWith(
        'Interview interview123 completing with warnings:',
        mockValidation.warnings
      );

      consoleSpy.mockRestore();
    });

    it('should handle account-specific validation errors', async () => {
      const mockInterview = {
        _id: 'interview123',
        client: 'client123',
        isComplete: false,
        status: 'pending',
      };

      const mockValidation = {
        isValid: false,
        completionPercentage: 80,
        errors: [
          {
            type: 'UNSYNCED_PAGE' as const,
            pageId: 'page-account1-beneficiaries',
            pageName: 'beneficiaries_account1',
            message: 'Page "beneficiaries_account1" is not yet synced with CRM',
            pageStatus: 'completed',
            syncStatus: 'pending',
          },
        ],
        warnings: [],
        requiredPages: ['name', 'address', 'ssn', 'beneficiaries_account1'],
        completedPages: ['name', 'address', 'ssn', 'beneficiaries_account1'],
        unsyncedPages: ['beneficiaries_account1'],
        pageDetails: [],
        summary: 'Interview 80% complete. Issues: 1 page(s) not synced',
      };

      coreService.findById.mockResolvedValue(mockInterview as any);
      validationService.validateInterviewCompletion.mockResolvedValue(mockValidation);

      await expect(controller.finish('interview123')).rejects.toThrow(BadRequestException);

      try {
        await controller.finish('interview123');
      } catch (error) {
        expect(error.getResponse().validation.errors[0].pageName).toBe('beneficiaries_account1');
        expect(error.getResponse().validation.errors[0].type).toBe('UNSYNCED_PAGE');
      }
    });
  });
});