import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";
import { InterviewPageInstanceStatusEnum, InterviewPageSyncStatusEnum } from '../../types/v2/interview-v2-queue-job.enum';

@Schema({ 
    timestamps: true, 
    collection: 'interviewpageinstances_v2' 
  })
  export class InterviewPageInstanceV2 extends Document {
    @Prop({ 
      type: SchemaTypes.ObjectId, 
      ref: 'InterviewV2', 
      required: true,
      index: true 
    })
    interviewId!: string;
  
    @Prop({ 
      type: String, 
      required: true,
      validate: {
        validator: (v: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v),
        message: 'Invalid UUID format'
      }
    })
    pageId!: string;  // UUID from frontend
  
    @Prop({ 
      type: String, 
      required: true 
    })
    pageName!: string;  // Unique name set by frontend

    @Prop({ 
      type: String, 
      required: true 
    })
    pageType!: string;  // Page type for rendering (e.g., "us_citizen", "primary_beneficiaries")
  
    @Prop({ 
      type: Number, 
      required: true 
    })
    visitOrder!: number;
  
    @Prop({ 
      type: String,
      default: null
    })
    navigatedFrom?: string;  // Previous page name
  
    @Prop({ 
      type: String,
      default: null
    })
    navigatedTo?: string;  // Next page name

    @Prop({ 
      type: String, 
      enum: Object.values(InterviewPageInstanceStatusEnum),
      default: InterviewPageInstanceStatusEnum.PENDING
    })
    status!: string;

    @Prop({ 
      type: String, 
      enum: Object.values(InterviewPageSyncStatusEnum),
      default: InterviewPageSyncStatusEnum.PENDING
    })
    syncStatus!: string;

    @Prop({ 
      type: SchemaTypes.Mixed,
      required: false
    })
    accountContext?: {
      accountId?: string;
      accountType?: string;  // 'beneficiary', 'spouse', 'dependent', etc.
      accountSubtype?: string;  // 'primary', 'secondary', etc.
      accountName?: string;  // For display purposes
    };

    @Prop({ 
      type: SchemaTypes.Mixed,
      default: {}
    })
    navigationContext?: {
      branchTaken?: string;
      timestamp?: Date;
      [key: string]: any;
    };
  }

  export const InterviewPageInstanceV2Schema = SchemaFactory.createForClass(InterviewPageInstanceV2);