import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { InterviewAccountInstanceStatusEnum, InterviewBeneficiaryPageStatusEnum } from '../../types/v2/interview-v2-queue-job.enum';

@Schema({ 
  timestamps: true, 
  collection: 'interviewaccountinstances_v2' 
})
export class InterviewAccountInstanceV2 extends Document {
  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'InterviewV2', 
    required: true,
    index: true 
  })
  interviewId!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'Client', 
    required: true,
    index: true 
  })
  accountId!: string;

  @Prop({ 
    type: String, 
    required: true 
  })
  accountType!: string;  // 'Ira', 'RothIra', 'Brokerage', etc.

  @Prop({ 
    type: String, 
    required: true 
  })
  accountLabel!: string;  // Display name

  @Prop({ 
    type: Boolean, 
    default: false 
  })
  requiresBeneficiaries!: boolean;

  @Prop({ 
    type: Object,
    default: {}
  })
  beneficiaryPages?: {
    primary?: {
      pageInstanceId?: string;
      status?: InterviewBeneficiaryPageStatusEnum;
      completedAt?: Date;
    };
    contingent?: {
      pageInstanceId?: string;
      status?: InterviewBeneficiaryPageStatusEnum;
      completedAt?: Date;
    };
  };

  @Prop({ 
    type: Object,
    default: {}
  })
  metadata?: {
    accountNumber?: string;
    openedDate?: Date;
    [key: string]: any;
  };

  @Prop({ 
    type: String,
    enum: Object.values(InterviewAccountInstanceStatusEnum),
    default: InterviewAccountInstanceStatusEnum.PENDING
  })
  status!: string;
}

export const InterviewAccountInstanceV2Schema = SchemaFactory.createForClass(InterviewAccountInstanceV2);

// Indexes
InterviewAccountInstanceV2Schema.index({ interviewId: 1, accountId: 1 }, { unique: true });
InterviewAccountInstanceV2Schema.index({ interviewId: 1, status: 1 });