import { Test, TestingModule } from '@nestjs/testing';
import { InterviewsService } from 'src/interviews/interviews.service';
import { InterviewsV1Service } from 'src/interviews/services/v1/interviews-v1.service';
import { CreateInterviewDto } from 'src/interviews/dto/v1/create-interview.dto';

describe('InterviewsService', () => {
  let service: InterviewsService;
  let mockV1Service: any;

  const mockClient = {
    _id: 1,
    primaryAdvisor: { id: 1 },
    primaryContact: {
      crmClientId: '1',
    },
    organisationId: 5,
  };
  beforeEach(async () => {
    mockV1Service = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      enqueueFlow: jest.fn(),
      addRequiredDocument: jest.fn(),
      removeRequiredDocument: jest.fn(),
      fetchPdf: jest.fn(),
      uploadDocument: jest.fn(),
    };
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewsService,
        {
          provide: InterviewsV1Service,
          useValue: mockV1Service,
        },
      ],
    }).compile();

    service = module.get<InterviewsService>(InterviewsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create an interview', async () => {
    const dto: CreateInterviewDto = {
      docusignSelected: true,
      customTemplates: ['1'],
      clientId: '1',
      email: '',
      name: '',
      accounts: [],
    };
    
    mockV1Service.create.mockResolvedValue('createdInterview');
    
    const result = await service.create(dto);

    expect(result).toEqual('createdInterview');
    expect(mockV1Service.create).toHaveBeenCalledWith(dto, undefined);
  });

  it('should find all interviews', async () => {
    mockV1Service.findAll.mockResolvedValue(['interview1', 'interview2']);

    const result = await service.findAll();

    expect(result).toEqual(['interview1', 'interview2']);
    expect(mockV1Service.findAll).toHaveBeenCalled();
  });

  it('should find one interview', async () => {
    const mockInterview = {
      _id: '1',
      client: mockClient,
    };
    const filter = { _id: '1' };
    
    mockV1Service.findOne.mockResolvedValue(mockInterview);
    
    const result = await service.findOne(filter, { x: 'x' } as any);
    
    expect(mockV1Service.findOne).toHaveBeenCalledWith(filter, { x: 'x' }, false);
    expect(result).toEqual(mockInterview);
  });

  it('should return null when interview not found', async () => {
    mockV1Service.findOne.mockResolvedValue(null);
    
    const result = await service.findOne({ _id: '1' }, { x: '' } as any);
    
    expect(result).toBeFalsy();
    expect(mockV1Service.findOne).toHaveBeenCalledWith({ _id: '1' }, { x: '' }, false);
  });


  it('should remove an interview', async () => {
    mockV1Service.remove.mockResolvedValue('removedInterview');

    const result = await service.remove('1');

    expect(result).toEqual('removedInterview');
    expect(mockV1Service.remove).toHaveBeenCalledWith('1', undefined);
  });
});
