export enum InterviewV2QueueJobType {
  UPDATE_PAGE = 'update_page',
  UPDATE_PAGE_STATUS_V2 = 'update_page_status_v2',
  COMPLETE_INTERVIEW_V2 = 'complete_interview_v2',
  SEND_COMPLETION_NOTIFICATION_V2 = 'send_completion_notification_v2',
  MARK_COMPLETE = 'mark_complete',
  SEND_NOTIFICATION = 'send_notification',
  PREPARE_DOCUSIGN_ENVELOPE = 'prepare_docusign_envelope',
}

export enum InterviewV2StatusType {
  NOTIFICATION = 'notification',
  SYNCED = 'synced',
}

export enum InterviewPageInstanceStatusEnum {
  PENDING = 'pending',
  VISITED = 'visited',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}

export enum InterviewPageSyncStatusEnum {
  PENDING = 'pending',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  FAILED = 'failed',
}

export enum InterviewAccountInstanceStatusEnum {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}

export enum InterviewBeneficiaryPageStatusEnum {
  PENDING = 'pending',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}

export enum InterviewStatusEnum {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ABANDONED = 'abandoned',
}