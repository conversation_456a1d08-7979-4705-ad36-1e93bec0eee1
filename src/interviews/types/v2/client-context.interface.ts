export interface ClientContext {
  // Client Status & Preferences
  status: {
    isExistingClient: boolean;           // Has crmClientId
    skipContactInterview: boolean;       // primaryContact.skipContactInterview
    readyToSend: boolean;               // readyToSend
    featuresSelected: boolean;          // featuresSelected
  };
  
  // Features & Selections
  features: {
    docusignSelected: boolean;          // docusignSelected
    sendAdv2b: boolean;                 // sendAdv2b
    doClientProfiling: boolean;         // doClientProfiling
  };
  
  // Contact Information
  contact: {
    firstName: string;                  // primaryContact.firstName
    lastName: string;                   // primaryContact.lastName
    email: string;                      // primaryContact.email
    hasCrmId: boolean;                  // !!primaryContact.crmClientId
  };
  
  // Advisor Context
  advisor: {
    firstName: string;                  // primaryAdvisor.firstName
    lastName: string;                   // primaryAdvisor.lastName
    tier?: string;                      // Could be derived or configured
  };
  
  // Organization Context
  organization: {
    id: string;                         // organisationId
    tier?: string;                      // Could come from org settings
  };
}

export interface AccountContext {
  // Account Collection Data (flattened for easier access)
  count: number;                      // Total account count
  types: string[];                    // Array of all account types
  hasRetirement: boolean;             // Has any IRA, 401k, Roth IRA
  hasJoint: boolean;                  // Has any joint accounts
  hasIndividual: boolean;             // Has any individual accounts
  hasTrust: boolean;                  // Has any trust accounts
  hasBusiness: boolean;               // Has any business accounts
  
  // Current Account Context (only when processing account-specific pages)
  current_account?: {
    accountId: string;                  // Account identifier
    ownership: string;                  // Ownership type (individual, joint, trust)
    label: string;                      // Display label
    advisoryRate?: number;              // Advisory fee rate
  };
}

export interface InterviewContext {
  client: ClientContext;
  accounts: AccountContext;
}