import { BaseClientContact, PrimaryAdvisor } from 'src/clients/schemas/clients.base.schema';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

// Type that represents actual database contact with crmClientId
export type DatabaseClientContact = BaseClientContact & {
  crmClientId: string; // Required for CRM operations
};

export type MergedContact = (Omit<CrmContact, 'accounts'>) & {
  accounts: (GenericCrmAccount & AccountDto)[];
  crmClientId?: string; // Optional for CRM data compatibility
};

export type InterviewData = {
  primaryContact: BaseClientContact;
  secondaryContact?: BaseClientContact;
  primaryAdvisor: PrimaryAdvisor;
  interview: EnrichedInterview;
};

// Type specifically for CRM operations that require crmClientId
export type InterviewDataWithCrmIds = {
  primaryContact: DatabaseClientContact;
  secondaryContact?: DatabaseClientContact;
  primaryAdvisor: PrimaryAdvisor;
  interview: EnrichedInterview;
};

export type InterviewDataWithCrmInfo = {
  organisation: Organisation;
  primaryContact: MergedContact;
  secondaryContact?: MergedContact;
  primaryAdvisor: PrimaryAdvisor;
  interview: EnrichedInterview;
};

export type Applicant = {
  name: string;
  email: string;
  phone: string;
};

export type InterviewCompletionParams = {
  interview: EnrichedInterview;
  clientId: string;
  organisationId: string;
  advisorId: string;
  primaryContact: BaseClientContact;
  secondaryContact?: BaseClientContact;
  primaryAdvisor: PrimaryAdvisor;
  envelopeId?: string;
  docusignSelected: boolean;
  isPrimary: boolean;
};

