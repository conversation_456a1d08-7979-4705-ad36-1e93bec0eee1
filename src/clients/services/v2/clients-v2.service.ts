import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { chunk, isEmpty } from 'lodash';
import { ClientSession, FilterQuery, Model } from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { ClsService } from 'nestjs-cls';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import {
  EnrichedClient,
  GetClientsQueryDto,
} from 'src/clients/dto/v2/get-clients.dto';
import { CSVService } from 'src/csv/csv.service';
import {
  CreateContactResponseDto,
  UpdateContactResponseDto,
} from 'src/integrations/crm/types/create-contact.dto';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { InterviewsService } from 'src/interviews/interviews.service';
import { InterviewCoreService } from 'src/interviews/services/v2/core/interview-core.service';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { XOR } from 'src/shared/types/monads';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { parseFileAsJson } from 'src/utils/file/file.util';
import 'src/utils/promise';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { Logger } from 'winston';
import { ContactDto, CreateClientDto } from 'src/clients/dto/v2/create-client.dto';
import { UpdateClientDto } from 'src/clients/dto/v2/update-client.dto';
import { Client } from 'src/clients/schemas/clients.schema';
import { ClientCsvRow } from 'src/clients/types/clients-csv-row.entity';
import { FileTypesEnum } from 'src/clients/types/file-types.enum';
import { TeamFileType } from 'src/clients/types/team-file.type';
import { DraftClient } from 'src/clients/schemas/draft-client.schema';
import {
  ClientBatchArchiveRequestDto,
  ClientBatchArchiveResponseDto,
} from 'src/clients/dto/v1/batch-archive-client.dto';
import {
  ClientBatchSendTextRequestDto,
  ClientBatchSendTextResponseDto,
  CommunicatedClientResult,
  } from 'src/clients/dto/v1/batch-send-text.dto';
import { TransitionStatusEnum } from 'src/transitions/types/transition-status.enum';
import { TransitionsService } from 'src/transitions/transitions.service';
import { NotificationTypeEnum } from 'src/clients/types/notification-types.enum';
import { InjectQueue } from '@nestjs/bullmq';
import { CLIENT_V2_QUEUE } from 'src/shared/constants/client.constant';
import { Queue } from 'bullmq';
import { ClientV2FlowService } from './client-v2-flow.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { generateClientHash } from 'src/clients/utils/queue.utils';
import * as mongoose from 'mongoose';
import { CreateClientFlowInput } from 'src/clients/types/client-flow-input';
import { ClientUrl } from 'src/clients/types/get-client-urls';
import { InterviewStatusEnum } from 'src/clients/types/client-interview-status';
import { UpdateClientInterviewCompletion } from 'src/clients/types/update-interview-status';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { ClientsCrmService } from 'src/clients/services/clients.crm.service';
import { ClientCommunicationsService } from 'src/clients/services/client-communications.service';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ContactAdapters } from 'src/clients/types/contact-adapters';

@Injectable()
export class ClientsV2Service {
  constructor(
    @InjectModel(Client.name) private readonly clientModel: Model<Client>,
    @InjectModel(DraftClient.name)
    private readonly draftClientModel: Model<DraftClient>,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    private readonly clsService: ClsService,
    @Inject(forwardRef(() => InterviewsService))
    public readonly interviewsService: InterviewsService,
    @Inject(forwardRef(() => InterviewCoreService))
    private readonly interviewCoreService: InterviewCoreService,
    @Inject(forwardRef(() => DocusignService))
    private readonly docusignService: DocusignService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly filesService: CSVService,
    @Inject(forwardRef(() => TransitionsService))
    private readonly transitionService: TransitionsService,
    @InjectQueue(CLIENT_V2_QUEUE.NAME) private readonly clientQueue: Queue,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => ClientsCrmService))
    private readonly clientsCrmService: ClientsCrmService,
    @Inject(forwardRef(() => ClientV2FlowService))
    private readonly clientFlowService: ClientV2FlowService,
    @Inject(forwardRef(() => ClientCommunicationsService))
    private readonly clientCommunicationsService: ClientCommunicationsService,
  ) { }

  async create(
    organisationId: string,
    createClientDto: CreateClientDto,
    session?: ClientSession,
  ) {
    const createdBy = this.clsService.get(ClsDataEnum.Advisor)?._id; // safe cls since it's only used in http request lifecycle
    const { primaryAdvisor, primaryCSA } = await this.getRepresentatives(
      createClientDto,
    );
    let status: ClientStatusEnum;
    if (createClientDto?.sendNow) {
      status = ClientStatusEnum.Creating;
    } else if (createClientDto?.readyToSend) {
      status = ClientStatusEnum.Ready;
    } else {
      status = ClientStatusEnum.Draft;
    }

    const { id, ...createClientDtoWithoutId } = createClientDto as any;

    const clientData = {
      organisationId,
      ...createClientDtoWithoutId,
      createdBy,
      apiVersion: 'v2',
      primaryContact: createClientDto.primaryContact,
      secondaryContact: createClientDto.secondaryContact,
      primaryAdvisor: primaryAdvisor
        ? {
          id: primaryAdvisor._id,
          firstName: primaryAdvisor.personalInfo.firstName,
          lastName: primaryAdvisor.personalInfo.lastName,
          email: primaryAdvisor.personalInfo.email,
          mobile: primaryAdvisor.personalInfo.phone,
          crmId: primaryAdvisor.crmId,
        }
        : null,
      primaryCSA: primaryCSA
        ? {
          id: primaryCSA._id,
          firstName: primaryCSA.personalInfo.firstName,
          lastName: primaryCSA.personalInfo.lastName,
          email: primaryCSA.personalInfo.email,
        }
        : null,
      status,
      fileUploadsNo: 0,
      sendAdv2b: createClientDto.sendAdv2b,
    };

    // If status is Draft, let's just save and return the draft
    if (status === ClientStatusEnum.Draft) {
      const createdDraftClients = await this.draftClientModel.create(
        [clientData],
        { session },
      );
      return createdDraftClients[0];
    }

    if (!primaryAdvisor || !primaryCSA) {
      throw new HttpException(
        'Primary advisor and primary CSA are required',
        HttpStatus.BAD_REQUEST,
      );
    }

    const [client] = await this.clientModel.create([clientData], { session });

    // TODO: This should be handled in the flow
    if (createClientDto?.transitionId) {
      await this.transitionService.updateStatus(
        createClientDto?.transitionId,
        TransitionStatusEnum.Transitioned,
        session
      );
    }

    const crmIntegration = this.advisorsCrmService.getCRMIntegrationFromAdvisor(primaryAdvisor);
    const crm = crmIntegration?.integrationConfig?.name?.toUpperCase();

    // Create flow only if client should be sent now
    if (createClientDto.sendNow) {
      // Convert V2 contacts to V1 format for compatibility with existing flow service
      const v1PrimaryContact = ContactAdapters.convertV2ToV1Contact(createClientDto.primaryContact);
      const v1SecondaryContact = createClientDto.secondaryContact 
        ? ContactAdapters.convertV2ToV1Contact(createClientDto.secondaryContact)
        : undefined;

      await this.clientFlowService.createClientFlow({
        clientId: client._id,
        primaryContact: v1PrimaryContact,
        secondaryContact: v1SecondaryContact,
        primaryAdvisor,
        primaryCSA,
        notificationMethods: createClientDto.notificationMethods,
        customTemplates: createClientDto.customTemplates,
        doClientProfiling: createClientDto.doClientProfiling,
        organisationId,
        sendAdv2b: createClientDto.sendAdv2b
      });
    }

    return client;
  }

  /**
   * Retrieves a list of clients based on the provided query parameters and the logged-in advisor's role.
   * @param query - The query parameters for filtering the clients.
   * @param organisationId - The ID of the organization.
   * @returns A promise that resolves to an array of enriched clients.
   * @throws HttpException with status code UNAUTHORIZED if the logged-in advisor's role is not recognized.
   */
  async findAllByLoggedInAdvisor(
    query: GetClientsQueryDto,
    organisationId: string,
  ): Promise<EnrichedClient[]> {
    // Ensure advisor is not undefined
    const advisor: AdvisorWithRole = this.clsService.get(ClsDataEnum.Advisor); // safe cls
    if (!advisor || !advisor.role || !advisor._id) {
      throw new HttpException(
        'Advisor information is not available',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const filter: FilterQuery<Client> = { organisationId };

    // Handle archived and active filters
    if (query?.include === 'archived') {
      filter.$or = [
        { archivedAt: { $ne: null } },
        { status: ClientStatusEnum.Archived },
      ];
    } else if (query?.include === 'active') {
      filter.$and = [
        { archivedAt: null },
        {
          $or: [
            { status: { $ne: ClientStatusEnum.Archived } },
            { status: ClientStatusEnum.Draft },
          ],
        },
      ];
    }

    // Handle filtering based on advisor's role
    switch (advisor.role.name) {
      case RolesEnum.SuperAdmin:
      case RolesEnum.CompanyAdmin:
        return this.findAll(filter, false);

      case RolesEnum.Representative:
        filter.$or = [
          { 'primaryAdvisor.id': advisor._id },
          { 'primaryCSA.id': advisor._id },
          { secondaryAdvisor: { id: advisor._id.toString() } },
          { secondaryCSA: { id: advisor._id.toString() } },
          { createdBy: advisor._id },
          ...(filter.$or || []), // Safely include archival conditions if they exist
        ];
        return this.findAll(filter, false);

      default:
        throw new HttpException(
          'Unauthorized: Invalid Advisor Role',
          HttpStatus.UNAUTHORIZED,
        );
    }
  }

  /**
   * Retrieves all clients based on the provided filters.
   * @param filters - Optional filters to apply to the query.
   * @param enrich - Flag indicating whether to enrich the clients with additional data.
   * @returns A promise that resolves to an array of enriched clients.
   */
  async findAll(
    filters?: FilterQuery<Client>,
    enrich = true,
  ): Promise<EnrichedClient[]> {
    if (!enrich) return this.clientModel.find(filters);
    return Promise.all(
      (await this.clientModel.find(filters)).map((client) =>
        this.enrichClient(client),
      ),
    );
  }

  /**
   * Finds a single client based on the provided filter.
   * @param filter - The filter to apply when searching for the client.
   * @param session - The optional MongoDB session to use for the operation.
   * @returns A Promise that resolves to the enriched client object.
   * @throws NotFoundException if the client is not found.
   */
  async findOne(
    filter: FilterQuery<Client>,
    session?: ClientSession,
    throwOnNotFound: boolean = false,
    skipEnrich: boolean = false,
  ): Promise<EnrichedClient> {
    const client = await this.clientModel.findOne(filter).session(session);

    if (!client) {
      throw new NotFoundException(`Client not found`);
    }

    if (skipEnrich) {
      return this.clientsCrmService.createFallbackEnrichedClient(client);
    }

    return this.enrichClient(client, throwOnNotFound);
  }

  /**
   * Finds a draft client based on the provided filter.
   * @param filter - The filter to apply when searching for the draft client.
   * @param session - The optional session to use for the database operation.
   * @returns The found draft client.
   * @throws NotFoundException if the draft client is not found.
   */
  async findOneDraft(filter: FilterQuery<Client>, session?: ClientSession) {
    const draftClient = await this.draftClientModel
      .findOne(filter)
      .session(session);

    if (!draftClient) {
      throw new NotFoundException(`Client not found`);
    }

    return draftClient;
  }


  /**
   * Processes the send now flow for a client, handling welcome communications and status updates.
   * @param clientId - The ID of the client to process.
   * @returns A Promise that resolves to true when processing is successful.
   * @throws Will throw an error if any part of the process fails, updating client status to Failed.
   */
  async resendText(clientId: string): Promise<boolean> {
    const client = await this.findOne({ _id: clientId });

    const [primaryInterview, secondaryInterview] = await Promise.all([
      this.interviewsService.findOne({
        client: clientId,
        isPrimary: true
      }),
      this.interviewsService.findOne({
        client: clientId,
        isPrimary: false
      })
    ]);

    if (!primaryInterview || !primaryInterview._id) {
      throw new Error('Primary interview not found or invalid');
    }

    await this.clientQueue.add(
      ClientQueueJobType.RESEND_TEXT,
      {
        clientId: client._id.toString(),
        primaryInterviewId: primaryInterview._id.toString(),
        secondaryInterviewId: secondaryInterview?._id?.toString(),
        organisationId: client.organisationId.toString(),
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: true,
      },
    );

    return true;
  }

  async processResendText(id: string, primaryInterviewId?: string, secondaryInterviewId?: string): Promise<boolean> {
    try {
      const client = await this.clientModel.findById(id);
      if (!client) {
        throw new NotFoundException(`Client not found with id: ${id}`);
      }

      await this.updateClientStatus(id, ClientStatusEnum.Processing);

      const [primaryInterview, secondaryInterview] = await Promise.all([
        primaryInterviewId ?
          this.interviewsService.findOne({ _id: primaryInterviewId }) :
          null,
        secondaryInterviewId ?
          this.interviewsService.findOne({ _id: secondaryInterviewId }) :
          null
      ]);

      await this.clientCommunicationsService.sendWelcomeText(client, primaryInterview, secondaryInterview);
      await this.updateClientStatus(id, ClientStatusEnum.Sent);
      return true;
    } catch (error) {
      console.error('Error in processResendText:', error);
      await this.updateClientStatus(id, ClientStatusEnum.Failed);
      throw error;
    }
  }

  /**
   * Updates a client with the specified ID.
   * @param id - The ID of the client to update.
   * @param updateClientDto - The data to update the client with.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A Promise that resolves to the updated client.
   */
  async update(
    id: string,
    updateClientDto: UpdateClientDto,
    session?: ClientSession,
  ): Promise<EnrichedClient> {
    const existingClient = await this.findOne({ _id: id }, session, false);
    const { primaryAdvisor, primaryCSA } = await this.getRepresentatives(updateClientDto);

    // Determine client status and completion date
    let status: ClientStatusEnum;
    let completedAt: Date | undefined;
    if (updateClientDto?.status) {
      status = updateClientDto.status;
      if ([ClientStatusEnum.Complete, ClientStatusEnum.Signed, ClientStatusEnum.Archived].includes(updateClientDto.status)) {
        completedAt = new Date();
      }
    } else if (updateClientDto?.sendNow) {
      status = ClientStatusEnum.Creating;
    } else if (updateClientDto?.readyToSend) {
      status = ClientStatusEnum.Ready;
    } else {
      status = ClientStatusEnum.Draft;
    }

    // Build update object
    const updateObj = {
      ...updateClientDto,
      primaryContact: updateClientDto?.primaryContact
        ? { ...updateClientDto.primaryContact }
        : existingClient?.primaryContact || null,
      secondaryContact: updateClientDto.secondaryContact
        ? { ...updateClientDto.secondaryContact }
        : existingClient?.secondaryContact || null,
      primaryAdvisor: primaryAdvisor
        ? {
          id: primaryAdvisor._id,
          firstName: primaryAdvisor.personalInfo.firstName,
          lastName: primaryAdvisor.personalInfo.lastName,
          email: primaryAdvisor.personalInfo.email,
          mobile: primaryAdvisor.personalInfo.phone,
          crmId: primaryAdvisor.crmId,
        }
        : existingClient?.primaryAdvisor || null,
      primaryCSA: primaryCSA
        ? {
          id: primaryCSA._id,
          firstName: primaryCSA.personalInfo.firstName,
          lastName: primaryCSA.personalInfo.lastName,
          email: primaryCSA.personalInfo.email,
        }
        : existingClient?.primaryCSA || null,
      status,
      completedAt,
      fileUploadsNo: updateClientDto?.fileUploadsNo || existingClient.fileUploadsNo,
      sendAdv2b: updateClientDto?.sendAdv2b !== undefined ? updateClientDto.sendAdv2b : existingClient.sendAdv2b,
    };

    // Update client
    Object.assign(existingClient, updateObj);
    const updatedClient = await this.enrichClient(
      await this.clientModel.findOneAndUpdate(
        { _id: existingClient._id },
        existingClient,
        { new: true },
      ),
      false,
    );

    // Handle send now flow if needed
    if (updateClientDto.sendNow) {
      // Convert V2 contacts to V1 format for compatibility
      const primaryContactForFlow = updateClientDto.primaryContact 
        ? ContactAdapters.convertV2ToV1Contact(updateClientDto.primaryContact)
        : updatedClient.primaryContact;
      const secondaryContactForFlow = updateClientDto.secondaryContact 
        ? ContactAdapters.convertV2ToV1Contact(updateClientDto.secondaryContact)
        : updatedClient.secondaryContact;

      await this.clientFlowService.createClientFlow({
        clientId: updatedClient._id,
        primaryContact: primaryContactForFlow,
        secondaryContact: secondaryContactForFlow,
        primaryAdvisor: primaryAdvisor || await this.advisorCrudService.findOne({ _id: updatedClient.primaryAdvisor.id }),
        primaryCSA: primaryCSA || await this.advisorCrudService.findOne({ _id: updatedClient.primaryCSA.id }),
        notificationMethods: updateClientDto.notificationMethods || [NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS],
        customTemplates: updateClientDto.customTemplates,
        doClientProfiling: updateClientDto.doClientProfiling,
        organisationId: updatedClient.organisationId.toString(),
        sendAdv2b: updateClientDto.sendAdv2b
      });
    }

    return updatedClient;
  }

  async updateClientStatus(clientId: string, status: ClientStatusEnum) {
    return this.clientModel.updateOne({ _id: clientId }, { $set: { status } });
  }

  async updateCrmClientIds(clientId: string, contactCrmIds: UpdateContactResponseDto | CreateContactResponseDto) {

    return this.clientModel.updateOne({ _id: clientId }, {
      $set: {
        'primaryContact.crmClientId': contactCrmIds?.primaryContactCrmId,
        ...(contactCrmIds?.secondaryContactCrmId && { 'secondaryContact.crmClientId': contactCrmIds.secondaryContactCrmId })
      }
    });
  }

  /**
   * Updates a draft client with the specified ID.
   * If the updateClientDto has the property "sendNow" set to true, the client status will be set to "Sent".
   * If the updateClientDto has the property "readyToSend" set to true, the client status will be set to "Ready".
   * Otherwise, the client status will be set to "Draft".
   *
   * @param id - The ID of the draft client to update.
   * @param updateClientDto - The data to update the draft client with.
   * @param session - Optional. The client session to use for the update operation.
   * @returns A Promise that resolves to the updated draft client.
   */
  async updateDraftClient(
    id: string,
    updateClientDto: UpdateClientDto,
    session?: ClientSession,
  ): Promise<DraftClient> {
    const existingDraftClient = await this.findOneDraft({ _id: id }, session);

    let status: ClientStatusEnum;

    if (updateClientDto?.sendNow) {
      status = ClientStatusEnum.Sent;
    } else if (updateClientDto?.readyToSend) {
      status = ClientStatusEnum.Ready;
    } else {
      status = ClientStatusEnum.Draft;
    }

    const { primaryAdvisor, primaryCSA } = await this.getRepresentatives(
      updateClientDto,
    );

    let primaryContactCrmId: string, secondaryContactCrmId: string;

    const updateObj = {
      ...updateClientDto,
      organisationId: existingDraftClient.organisationId,
      primaryContact: updateClientDto?.primaryContact
        ? {
          ...updateClientDto.primaryContact,
          crmClientId: primaryContactCrmId,
        }
        : existingDraftClient?.primaryContact || null,
      secondaryContact: updateClientDto.secondaryContact
        ? {
          ...updateClientDto.secondaryContact,
          crmClientId: secondaryContactCrmId,
        }
        : existingDraftClient?.secondaryContact || null,
      primaryAdvisor: primaryAdvisor
        ? {
          id: primaryAdvisor._id,
          firstName: primaryAdvisor.personalInfo.firstName,
          lastName: primaryAdvisor.personalInfo.lastName,
          email: primaryAdvisor.personalInfo.email,
          mobile: primaryAdvisor.personalInfo.phone,
          crmId: primaryAdvisor.crmId,
        }
        : existingDraftClient?.primaryAdvisor || null,
      primaryCSA: primaryCSA
        ? {
          id: primaryCSA._id,
          firstName: primaryCSA.personalInfo.firstName,
          lastName: primaryCSA.personalInfo.lastName,
          email: primaryCSA.personalInfo.email,
        }
        : existingDraftClient?.primaryCSA || null,
      status,
      fileUploadsNo:
        updateClientDto?.fileUploadsNo || existingDraftClient.fileUploadsNo,
      sendAdv2b: updateClientDto?.sendAdv2b !== undefined ? updateClientDto.sendAdv2b : existingDraftClient.sendAdv2b,
    };

    /**
     * Destructing because otherwise in the client create, the same _id will be assigned to
     * the new client created.
     */
    const { _id, ...updateDto } = {
      ...existingDraftClient.toJSON(),
      ...updateObj,
    };
    let updatedClient: DraftClient | EnrichedClient;
    if ([ClientStatusEnum.Ready, ClientStatusEnum.Sent].includes(status)) {
      // Turn draft client into a client
      const [client] = await this.clientModel.create([updateDto], { session });

      if (updateDto.transitionId) {
        await this.transitionService.updateStatus(updateDto.transitionId.toString(), TransitionStatusEnum.Transitioned, session);
      }

      if (updateDto.sendNow) {
        // Convert V2 contacts to V1 format for flow compatibility
        const v1PrimaryContact = ContactAdapters.convertV2ToV1Contact(updateDto.primaryContact);
        const v1SecondaryContact = updateDto.secondaryContact 
          ? ContactAdapters.convertV2ToV1Contact(updateDto.secondaryContact)
          : undefined;

        await this.clientFlowService.createClientFlow({
          clientId: client._id,
          primaryContact: v1PrimaryContact,
          secondaryContact: v1SecondaryContact,
          primaryAdvisor,
          primaryCSA,
          notificationMethods: updateDto.notificationMethods,
          customTemplates: updateDto.customTemplates,
          doClientProfiling: updateDto.doClientProfiling,
          organisationId: updateDto.organisationId.toString(),
          sendAdv2b: updateDto.sendAdv2b
        });
      }

      await this.removeDraftClient(existingDraftClient._id.toString());
      return client;
    } else {
      updatedClient = await this.draftClientModel.findOneAndUpdate(
        { _id },
        { $set: { ...updateDto, _id } },
        { new: true },
      );
    }

    return updatedClient;
  }

  /**
   * Removes a draft client from the database.
   * @param draftClientId - The ID of the draft client to be removed.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A promise that resolves to the deleted draft client.
   */
  removeDraftClient(draftClientId: string, session?: ClientSession) {
    return this.draftClientModel.findOneAndDelete(
      { _id: draftClientId },
      { session },
    );
  }

  /**
   * Removes a client by ID.
   * @param id - The ID of the client to remove.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A string indicating the action of removing the client.
   * @throws NotFoundException if the client with the specified ID is not found.
   */
  async remove(id: string, session?: ClientSession): Promise<string> {
    const draftClient = await this.draftClientModel
      .findById(id)
      .session(session);

    if (draftClient) {
      await this.draftClientModel.findByIdAndDelete(id).session(session);
      return `Draft client #${id} removed`;
    }

    // Get the client details before deletion to generate the idempotency key
    const client = await this.clientModel.findById(id).session(session);
    if (!client) {
      throw new NotFoundException(`Client #${id} not found`);
    }

    // Delete the client from the database
    const result = await this.clientModel
      .deleteOne({ _id: id })
      .session(session);

    if (result.deletedCount === 0) {
      throw new NotFoundException(`Client #${id} not found`);
    }

    // Clean up the flow job from the queue if necessary
    if (client.primaryContact && client.primaryAdvisor) {
      await this.clientFlowService.cleanUpClientFlowJobs(client);
    }

    return `Client #${id} removed`;
  }

  /**
   * Enriches a client document with additional information.
   * @param clientDoc The client document to be enriched.
   * @param shouldThrowOnEnrichFail Indicates whether to throw an error if enrichment fails.
   * @returns A promise that resolves to the enriched client document.
   */
  async enrichClient(
    clientDoc: Client,
    shouldThrowOnEnrichFail: boolean = false,
  ): Promise<EnrichedClient> {
    return this.clientsCrmService.enrichClientWithCrmData(clientDoc, shouldThrowOnEnrichFail);
  }

  /**
   * Handles the file upload and adds clients from a CSV file.
   *
   * @param files - The uploaded files.
   * @param organisationId - The ID of the organization.
   * @param session - Optional MongoDB client session.
   * @returns A promise that resolves to the result of adding clients from the CSV file.
   * @throws {HttpException} If an error occurs during the process.
   */
  async handleFileUpload(
    files: Express.Multer.File[],
    organisationId: string,
    session?: ClientSession,
  ): Promise<any> {
    try {
      const teamFile = files.find(
        (file) => file.fieldname === FileTypesEnum.Team,
      );
      const team = teamFile
        ? parseFileAsJson<TeamFileType>(teamFile.buffer)
        : null;

      const csvFile = files.find(
        (file) => file.fieldname === FileTypesEnum.Csv,
      );
      const csvRows = csvFile
        ? await this.filesService.parseCsv<ClientCsvRow>(
          csvFile.buffer,
          ClientCsvRow,
        )
        : [];

      if (!team || !csvRows || csvRows.length === 0) {
        throw new HttpException('Invalid file data', HttpStatus.BAD_REQUEST);
      }

      const result = await this.addClientsFromCsv(
        organisationId,
        team,
        csvRows,
        session,
      );
      return {
        success: true,
        ...result,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Adds clients from a CSV file to the database.
   *
   * @param organisationId - The ID of the organization.
   * @param team - The type of team.
   * @param csvRows - An array of CSV rows.
   * @param session - Optional MongoDB client session.
   * @returns An object containing the count of successfully added clients and any failures.
   */
  private async addClientsFromCsv(
    organisationId: string,
    team: TeamFileType,
    csvRows: ClientCsvRow[],
    session?: ClientSession,
  ) {
    const clientDtos = csvRows.map((csvRow) => csvRow.toCreateDto(team));

    const { successCount, failures } = await this.processClientDtos(
      organisationId,
      clientDtos,
      session,
    );

    return {
      successCount,
      failures,
    };
  }

  /**
   * Processes an array of client DTOs and creates clients for an organization.
   *
   * @param organisationId - The ID of the organization.
   * @param clientDtos - An array of client DTOs.
   * @param session - Optional MongoDB client session.
   * @returns An object containing the count of successful creations and an array of failures.
   */
  private async processClientDtos(
    organisationId: string,
    clientDtos: CreateClientDto[],
    session?: ClientSession,
  ) {
    // Map each DTO to a promise that creates the client and handles errors locally
    const clientPromises = clientDtos.map((dto) =>
      this.create(organisationId, dto, session)
        .then(() => ({ success: true }))
        .catch((error) => ({
          success: false,
          error: error.message,
          data: dto,
        })),
    );

    const results = await Promise.allLimitted(clientPromises);

    // Partition the results into successes and failures
    const successes = results.filter((result) => result.success);
    const failures = results.filter((result) => !result.success);

    return {
      successCount: successes.length,
      failures,
    };
  }

  /**
   * Updates the completion percentage and timestamp of a client's interview status.
   * @param clientId - The ID of the client.
   * @param percentage - The new completion percentage.
   * @param timestamp - The timestamp of the update (default: current date and time).
   */
  async updateClientCompletionPercentage(
    clientId: string,
    percentage: number,
    timestamp: Date = new Date(),
    session?: ClientSession,
  ) {
    await this.clientModel.updateOne(
      { _id: clientId },
      {
        $set: {
          'interviewStatus.percentageCompleted': percentage,
          'interviewStatus.timestamp': timestamp,
        },
      },
      { session },
    );
  }

  /**
   * Sends an SMS message to a batch of clients.
   *
   * @param request - The batch request containing client IDs.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A Promise that resolves when the messages are sent.
   */
  async batchSendSms(
    request: ClientBatchSendTextRequestDto,
    session?: ClientSession,
  ): Promise<ClientBatchSendTextResponseDto> {
    const { clientIds } = request;
    const clients = await this.findAll(
      {
        _id: { $in: clientIds },
        status: { $in: [ClientStatusEnum.Ready, ClientStatusEnum.Sent] },
      },
      true,
    );

    // Get all interviews for these clients
    const clientInterviews = await this.interviewsService.find({
      client: { $in: clientIds },
    });

    // Organize interviews by client ID and type (primary/secondary)
    const primaryInterviews: Record<string, any> = {};
    const secondaryInterviews: Record<string, any> = {};

    clientInterviews.forEach(interview => {
      const clientId = interview.client.toString();
      if (interview.isPrimary) {
        primaryInterviews[clientId] = interview;
      } else {
        secondaryInterviews[clientId] = interview;
      }
    });

    const batches = chunk<Client>(clients, 10);
    const communicatedClients: CommunicatedClientResult[] = [];

    for (const batch of batches) {
      const results = await this.clientCommunicationsService.batchSendSms(
        batch,
        primaryInterviews,
        secondaryInterviews,
        session
      );

      communicatedClients.push(...results.filter(r => r.clientId));
    }

    return {
      clients: communicatedClients,
      batchSize: clientIds.length,
      totalClientsCommunicated: communicatedClients.filter(
        (client) => client.communicated,
      ).length,
    };
  }

  async archiveClient(
    clientId: string,
    session?: ClientSession,
  ): Promise<EnrichedClient> {
    // Get client before archiving to clean up flow jobs
    const client = await this.clientModel.findById(clientId).session(session);
    if (!client) {
      throw new NotFoundException(`Client #${clientId} not found`);
    }

    // Archive the client
    await this.clientModel.findOneAndUpdate(
      { _id: clientId },
      { archivedAt: new Date() },
      { session },
    );

    // Clean up the flow jobs to allow recreation of the client if necessary
    if (client.primaryContact && client.primaryAdvisor) {
      await this.clientFlowService.cleanUpClientFlowJobs(client);
    }

    return this.findOne({ _id: clientId }, session, false);
  }

  async batchArchiveClients(
    request: ClientBatchArchiveRequestDto,
    session?: ClientSession,
  ): Promise<ClientBatchArchiveResponseDto> {
    const clientsUpdated = await this.clientModel.updateMany(
      { _id: { $in: request.clientIds } },
      { $set: { archivedAt: new Date() } },
    );

    // also do for draft
    const draftClientsUpdated = await this.draftClientModel.updateMany(
      { _id: { $in: request.clientIds } },
      { $set: { archivedAt: new Date() } },
    );

    if (isEmpty(clientsUpdated) && isEmpty(draftClientsUpdated)) {
      throw new NotFoundException(`Clients not found`);
    }

    return {
      clientsModified:
        clientsUpdated.modifiedCount + draftClientsUpdated.modifiedCount,
      batchSize: request.clientIds.length,
    };
  }

  async unarchiveClient(
    clientId: string,
    session?: ClientSession,
  ): Promise<EnrichedClient> {
    await this.clientModel.findOneAndUpdate(
      { _id: clientId },
      { archivedAt: null },
      { session },
    );
    return this.findOne({ _id: clientId }, session, false);
  }

  async updateLastContactActivityTimestamp(
    clientId: string,
    isPrimary: boolean,
    session?: ClientSession,
  ) {
    return this.clientModel.findOneAndUpdate(
      { _id: clientId },
      {
        $set: {
          ...(isPrimary
            ? { 'primaryContact.lastActivityAt': new Date() }
            : { 'secondaryContact.lastActivityAt': new Date() }),
        },
      },
      { session },
    );
  }

  async sendWelcomeNotification(
    clientId: string,
    notificationMethods: NotificationTypeEnum[],
  ) {
    const client = await this.findOne({ _id: clientId });
    
    let primaryInterview, secondaryInterview;
    
    if (client.apiVersion === 'v2') {
      // For V2 clients, use V2 interview system
      const v2Interviews = await this.interviewCoreService.find({ client: clientId });
      primaryInterview = v2Interviews.find(interview => interview.contactType === 'primary');
      secondaryInterview = v2Interviews.find(interview => interview.contactType === 'secondary');
      
      // Transform V2 interviews to include isPrimary property for compatibility with communications service
      if (primaryInterview) {
        (primaryInterview as any).isPrimary = true;
      }
      if (secondaryInterview) {
        (secondaryInterview as any).isPrimary = false;
      }
    } else {
      // For V1 clients, use V1 interview system
      const v1Interviews = await this.interviewsService.find({ client: clientId });
      primaryInterview = v1Interviews.find(interview => interview.isPrimary);
      secondaryInterview = v1Interviews.find(interview => !interview.isPrimary);
    }

    await this.clientCommunicationsService.sendWelcomeNotification(
      clientId,
      client,
      primaryInterview,
      secondaryInterview,
      notificationMethods
    );
  }

  async createInterview(clientId: string, createClientDto: CreateClientDto) {
    const client = await this.findOne({ _id: clientId }, undefined, false, true);
    if (!client) {
      throw new Error('Client not found');
    }
    // For V2 clients, use V2 interview system with templates
    return this.createV2Interviews(client, createClientDto);
  }

  public async createV2Interviews(
    client: Client,
    createClientDto: CreateClientDto | UpdateClientDto,
    session?: ClientSession,
  ) {
    const interviews = [];

    // Get advisor ID from client (primary advisor is required)
    const advisorId = client.primaryAdvisor?.id;
    if (!advisorId) {
      throw new Error('Primary advisor is required for V2 interview creation');
    }

    // Create primary interview using V2 system
    if (client.primaryContact) {
      const primaryInterview = await this.interviewCoreService.create(
        {
          clientId: client._id.toString(),
          contactType: 'primary',
          templateId: createClientDto.customTemplates?.[0] || undefined, // Use custom template if provided
        },
        session,
      );
      interviews.push(primaryInterview);
    }
    
    // Create secondary interview using V2 system
    if (
      client.secondaryContact &&
      createClientDto?.secondaryContact &&
      !isEmpty(createClientDto.secondaryContact)
    ) {
      const secondaryInterview = await this.interviewCoreService.create(
        {
          clientId: client._id.toString(),
          contactType: 'secondary',
          templateId: createClientDto.customTemplates?.[0] || undefined, // Use same template as primary contact
        },
        session,
      );
      interviews.push(secondaryInterview);
    }

    return interviews;
  }

  /**
   * Retrieves the primary advisor and primary CSA for a client.
   * @param dto - The DTO containing the IDs of the primary advisor and primary CSA.
   * @returns An object containing the primary advisor and primary CSA.
   */
  private async getRepresentatives(dto: XOR<CreateClientDto, UpdateClientDto>) {
    let primaryAdvisor: AdvisorWithRole, primaryCSA: AdvisorWithRole;
    if (dto?.primaryAdvisor?.id && dto?.primaryCSA?.id) {
      const res = await Promise.all([
        this.advisorCrudService.findOne({
          _id: dto?.primaryAdvisor?.id,
        }),
        this.advisorCrudService.findOne({
          _id: dto?.primaryCSA?.id,
        }),
      ]);
      primaryAdvisor = res[0];
      primaryCSA = res[1];
    }

    return { primaryAdvisor, primaryCSA };
  }

  async getClientStatuses(
    organisationId: string,
    clientIds: string[],
  ): Promise<{ clientId: string; status: ClientStatusEnum }[]> {
    const validClientIds = clientIds.filter(id => mongoose.Types.ObjectId.isValid(id));

    if (validClientIds.length !== clientIds.length) {
      this.logger.warn(`Some client IDs were invalid and filtered out`);
    }

    const clients = await this.clientModel
      .find({
        _id: { $in: validClientIds.map((id) => new mongoose.Types.ObjectId(id)) },
        organisationId: new mongoose.Types.ObjectId(organisationId),
      })
      .select('_id status')
      .lean();

    return clients.map((client) => ({
      clientId: client._id.toString(),
      status: client.status,
    }));
  }

  public async generateClientUrl(
    organisationId: string,
    clientId: string,
  ): Promise<ClientUrl> {
    const client = await this.clientModel.findOne({
      _id: clientId,
      organisationId: new mongoose.Types.ObjectId(organisationId)
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${clientId} not found`);
    }

    if (client.status !== ClientStatusEnum.Sent) {
      throw new BadRequestException(`Client ${clientId} is not in Sent status`);
    }

    // Find associated interviews
    const interviews = await this.interviewsService.find({ client: clientId });

    const primaryInterview = interviews.find(interview => interview.isPrimary);
    const secondaryInterview = interviews.find(interview => !interview.isPrimary);

    const frontendDomain = getFrontendDomain();

    if (!frontendDomain) {
      throw new InternalServerErrorException('Frontend domain configuration is missing');
    }

    return {
      primaryUrl: primaryInterview
        ? this.buildInterviewUrl(frontendDomain, primaryInterview._id)
        : null,
      secondaryUrl: secondaryInterview
        ? this.buildInterviewUrl(frontendDomain, secondaryInterview._id)
        : null,
    };
  }

  private buildInterviewUrl(domain: string, interviewId: mongoose.Types.ObjectId): string {
    return `https://${domain}/interview/${interviewId.toString()}`;
  }

  /**
   * Sends a DocuSign envelope to a client.
   * @param clientId - The ID of the client to send the envelope to.
   * @returns A string indicating that the DocuSign envelope was sent.
   */
  async sendEnvelope(clientId: string) {
    const { envelopeId } = await this.interviewsService.findOne({
      client: clientId,
    });

    const {
      organisationId,
      primaryAdvisor: { id: advisorId },
    } = await this.findOne({ _id: clientId });

    const args = {
      organisationId: organisationId.toString(),
      advisorId: advisorId.toString(),
    };

    await this.docusignService.sendEnvelope({ ...args, envelopeId });

    return 'DocuSign envelope sent.';
  }

  /**
   * Retrieves the envelope ID from the v2 interview and uses it to generate a DocuSign sender view URL for envelope review.
   * @param clientId - The ID of the client.
   * @returns A Promise that resolves to the DocuSign sender view URL for envelope review.
   */
  async reviewEnvelope(clientId: string) {
    // Find v2 interview using the InterviewCoreService which handles v2 interviews
    const interview = await this.interviewCoreService.findOne({ client: clientId });
    
    if (!interview || !interview.envelopeId) {
      throw new HttpException(
        'No envelope found for this client', 
        HttpStatus.NOT_FOUND
      );
    }

    const {
      organisationId,
      primaryAdvisor: { id: advisorId },
    } = await this.findOne({ _id: clientId });

    const args = {
      organisationId: organisationId.toString(),
      advisorId: advisorId.toString(),
    };

    return this.docusignService.reviewEnvelope({
      ...args,
      envelopeId: interview.envelopeId,
    });
  }

  async updateClientStatusAfterEnvelope(clientId: string, docusignSelected: boolean): Promise<void> {
    const interviews = await this.interviewsService.find({ client: clientId });

    if (!interviews?.length) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    const bothInterviewsFinished = interviews.every(interview => interview.isComplete);

    const newStatus = docusignSelected
      ? bothInterviewsFinished
        ? ClientStatusEnum.PendingReview
        : ClientStatusEnum.Sent
      : ClientStatusEnum.Complete;

    await this.updateClientStatus(clientId, newStatus);
  }

  async updateInterviewStatus(clientId: string, interviewStatus: InterviewStatusEnum): Promise<boolean> {
    const client = await this.clientModel.findOne({ _id: clientId }, { interviewStatus: 1 });
    if (!client) {
      throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
    }

    if (client.interviewStatus?.status === interviewStatus) {
      return
    }

    await this.clientModel.updateOne(
      { _id: clientId },
      { $set: { 'interviewStatus.status': interviewStatus } }
    );
  }

  async updateClientInterviewCompletion({
    clientId,
    isPrimary
  }: UpdateClientInterviewCompletion) {
    const contactField = isPrimary ? 'primaryContact' : 'secondaryContact';
    const currentDate = new Date();

    const update = {
      $set: {
        'interviewStatus.status': InterviewStatusEnum.Completed,
        'interviewStatus.percentageCompleted': 100,
        'interviewStatus.timestamp': currentDate,
        [`${contactField}.lastActivityAt`]: currentDate,
      },
    };

    return this.clientModel.updateOne({
      _id: clientId,
      'interviewStatus.percentageCompleted': { $ne: 100 },
    },
      update
    );
  }

  /**
  * Attempts to retry a failed client by retrying the specific failed job
  * or creating a new processing flow as a fallback.
  *
  * @param clientId - The ID of the client to retry.
  * @param session - Optional MongoDB session.
  * @returns A message indicating the result of the retry attempt.
  */
  async retryFailedClient(
    clientId: string,
    session?: ClientSession,
  ): Promise<{ message: string }> {
    // Validate client exists and is in failed state
    const client = await this.clientModel.findById(clientId).session(session);
    if (!client) {
      throw new NotFoundException(`Client #${clientId} not found`);
    }
    if (client.status !== ClientStatusEnum.Failed) {
      throw new HttpException(
        'Client is not in a failed state',
        HttpStatus.CONFLICT,
      );
    }

    // Set status to Processing
    await this.updateClientStatus(clientId, ClientStatusEnum.Creating);

    try {
      // First attempt: Try to find and retry the specific failed job
      if (client.organisationId && client.primaryContact && client.primaryAdvisor?.id) {
        const idempotencyKey = generateClientHash(
          client.organisationId.toString(),
          {
            organisationId: client.organisationId.toString(),
            primaryContact: client.primaryContact as any,
            primaryAdvisor: { id: client.primaryAdvisor.id.toString() },
          },
        );

        const flow: any = await this.clientFlowService
          .getFlowProducer()
          .getFlow({ id: idempotencyKey, queueName: CLIENT_V2_QUEUE.NAME });

        if (flow) {
          const failedJobInfo = await this.clientFlowService.findFailedJobInFlow(flow);
          if (failedJobInfo) {
            await failedJobInfo.job.retry();
            return { message: `Retry initiated for failed job` };
          }
        }
      }

      // Fallback: Create a new flow if specific job retry wasn't possible
      const [primaryAdvisor, primaryCSA] = await Promise.all([
        this.advisorCrudService.findOne({ _id: client.primaryAdvisor?.id }),
        this.advisorCrudService.findOne({ _id: client.primaryCSA?.id }),
      ]);

      if (!primaryAdvisor || !primaryCSA) {
        throw new Error('Missing advisor/CSA information required for client processing');
      }

      // Clean up any existing jobs and create a new flow
      await this.clientFlowService.cleanUpClientFlowJobs(client);
      
      // Ensure database contacts are compatible with V1 format
      if (!client.primaryContact.email) {
        throw new Error('Primary contact must have email for flow processing');
      }
      
      const flowInput: CreateClientFlowInput = {
        clientId,
        primaryContact: {
          firstName: client.primaryContact.firstName,
          lastName: client.primaryContact.lastName,
          email: client.primaryContact.email,
          mobile: client.primaryContact.mobile || '',
          skipContactInterview: client.primaryContact.skipContactInterview || false,
          accounts: client.primaryContact.accounts || [],
        },
        secondaryContact: client.secondaryContact && client.secondaryContact.email ? {
          firstName: client.secondaryContact.firstName,
          lastName: client.secondaryContact.lastName,
          email: client.secondaryContact.email,
          mobile: client.secondaryContact.mobile || '',
          skipContactInterview: client.secondaryContact.skipContactInterview || false,
          accounts: client.secondaryContact.accounts || [],
        } : undefined,
        primaryAdvisor: primaryAdvisor as AdvisorWithRole,
        primaryCSA: primaryCSA as AdvisorWithRole,
        notificationMethods: client.notificationMethods || [NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS],
        customTemplates: client.customTemplates || [],
        doClientProfiling: typeof client.doClientProfiling === 'boolean' ? client.doClientProfiling : false,
        organisationId: client.organisationId.toString(),
        sendAdv2b: typeof client.sendAdv2b === 'boolean' ? client.sendAdv2b : true,
      };
      
      await this.clientFlowService.createClientFlow(flowInput);
      return { message: 'Client retry initiated successfully' };
      
    } catch (error) {
      // Revert status to Failed on error
      await this.clientModel.updateOne(
        { _id: clientId, status: ClientStatusEnum.Creating },
        { $set: { status: ClientStatusEnum.Failed } },
      );

      this.logger.error(`Retry failed for client ${clientId}: ${error.message}`, error.stack);
      
      if (error instanceof HttpException && 
          error.getStatus() === HttpStatus.BAD_REQUEST && 
          error.message.includes('Client already in progress')) {
        return { message: 'Client processing is already in progress' };
      }
      
      throw new InternalServerErrorException('Failed to process client retry');
    }
  }
}
