import { ContactDto as V1ContactDto } from '../dto/v1/create-client.dto';
import { ContactDto as V2ContactDto } from '../dto/v2/create-client.dto';
import { BaseClientContact, BaseAccount } from '../schemas/clients.base.schema';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

/**
 * Type adapters to convert between V1 and V2 contact formats
 * Ensures V1 compatibility while allowing V2 flexibility
 */
export class ContactAdapters {
  /**
   * Converts BaseAccount to AccountDto format
   * Handles the difference between database schema and API DTO
   */
  private static convertBaseAccountToAccountDto(baseAccount: BaseAccount): AccountDto {
    return {
      type: baseAccount.type,
      label: baseAccount.label,
      ownership: baseAccount.ownership,
      masterAccountNumber: baseAccount.masterAccountNumber || '', // Default empty string for required field
      features: baseAccount.features || [],
      advisoryRate: baseAccount.advisoryRate || 0,
    };
  }

  /**
   * Converts V2 ContactDto to V1 ContactDto format
   * Ensures email is required for V1 compatibility
   * Handles mixed account types from database/DTO sources
   */
  static convertV2ToV1Contact(v2Contact: V2ContactDto | BaseClientContact): V1ContactDto {
    if (!v2Contact.email) {
      throw new Error('Email is required when converting contact to V1 format');
    }

    // Handle accounts conversion - could be BaseAccount[] or AccountDto[]
    let convertedAccounts: AccountDto[] = [];
    if (v2Contact.accounts) {
      convertedAccounts = v2Contact.accounts.map(account => {
        // Check if it's BaseAccount (has _id, toObject methods) or AccountDto
        if ('toObject' in account || '_id' in account) {
          return this.convertBaseAccountToAccountDto(account as BaseAccount);
        }
        return account as AccountDto;
      });
    }

    return {
      firstName: v2Contact.firstName,
      lastName: v2Contact.lastName,
      email: v2Contact.email, // Required in V1
      mobile: v2Contact.mobile || '', // V1 expects string, not optional
      skipContactInterview: v2Contact.skipContactInterview || false,
      accounts: convertedAccounts,
    };
  }

  /**
   * Converts V1 ContactDto to V2 ContactDto format
   * Straightforward conversion since V2 is more flexible
   */
  static convertV1ToV2Contact(v1Contact: V1ContactDto): V2ContactDto {
    return {
      firstName: v1Contact.firstName,
      lastName: v1Contact.lastName,
      email: v1Contact.email,
      mobile: v1Contact.mobile,
      skipContactInterview: v1Contact.skipContactInterview,
      accounts: v1Contact.accounts,
    };
  }
}

/**
 * Type for database operations where crmClientId is needed
 * Extends V1ContactDto with database-specific fields
 */
export interface DatabaseContact extends V1ContactDto {
  crmClientId?: string;
}

/**
 * Type guard to check if contact has crmClientId
 */
export function hasValidCrmClientId(contact: any): contact is DatabaseContact & { crmClientId: string } {
  return contact && typeof contact.crmClientId === 'string' && contact.crmClientId.length > 0;
}