import {
  Is<PERSON>rray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { NotificationTypeEnum } from '../../types/notification-types.enum';

export class ContactDto {
  @ApiProperty()
  @IsString()
  firstName: string;

  @ApiProperty()
  @IsString()
  lastName: string;

  @ApiProperty()
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  mobile?: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  skipContactInterview?: boolean;

  @ApiProperty({ type: [AccountDto] })
  @ValidateNested({ each: true })
  @Type(() => AccountDto)
  @IsOptional()
  accounts?: AccountDto[];
}

export class AdvisorRefDto {
  @ApiProperty({ description: 'Advisor ID (ObjectId as string)' })
  @IsString()
  id: string;
}

export class CreateClientDto {
  // Core contact data
  @ApiProperty({ type: ContactDto, description: 'Primary contact information' })
  @ValidateNested()
  @Type(() => ContactDto)
  primaryContact: ContactDto;

  @ApiProperty({ type: ContactDto, description: 'Secondary contact information', required: false })
  @ValidateNested()
  @IsOptional()
  @Type(() => ContactDto)
  secondaryContact?: ContactDto;

  // Workflow control (both needed for proper state management)
  @ApiProperty({ description: 'Client data is complete and validated, ready to be sent when needed' })
  @IsBoolean()
  readyToSend: boolean;

  @ApiProperty({ description: 'Actually triggers the sending flow (creates interview, sends emails, updates CRM)' })
  @IsBoolean()
  sendNow: boolean;

  // Feature selection (UI workflow states)
  @ApiProperty({ description: 'Whether account features have been selected', required: false })
  @IsBoolean()
  @IsOptional()
  featuresSelected?: boolean;

  @ApiProperty({ description: 'Whether DocuSign integration is selected for this client', required: false, default: false })
  @IsOptional()
  @IsBoolean()
  docusignSelected?: boolean = false;

  @ApiProperty({ description: 'Whether accounts have been added/selected (draft workflow)', required: false })
  @IsOptional()
  @IsBoolean()
  addAccountsSelected?: boolean;

  // Team assignment (fixed type errors)
  @ApiProperty({ type: AdvisorRefDto, description: 'Primary advisor assignment', required: false })
  @ValidateNested()
  @IsOptional()
  @Type(() => AdvisorRefDto)
  primaryAdvisor?: AdvisorRefDto;

  @ApiProperty({ type: AdvisorRefDto, description: 'Primary CSA assignment', required: false })
  @ValidateNested()
  @IsOptional()
  @Type(() => AdvisorRefDto)
  primaryCSA?: AdvisorRefDto;

  @ApiProperty({ type: [AdvisorRefDto], description: 'Additional advisors who can provide advice', required: false })
  @ValidateNested({ each: true })
  @Type(() => AdvisorRefDto)
  @IsArray()
  @IsOptional()
  secondaryAdvisor?: AdvisorRefDto[];

  @ApiProperty({ type: [AdvisorRefDto], description: 'Additional CSAs (Client Service Associates)', required: false })
  @ValidateNested({ each: true })
  @Type(() => AdvisorRefDto)
  @IsArray()
  @IsOptional()
  secondaryCSA?: AdvisorRefDto[];

  // Optional configuration
  @ApiProperty({ description: 'Whether to update CRM system', required: false, default: true })
  @IsOptional()
  @IsBoolean()
  shouldUpdateCrm?: boolean = true;

  @ApiProperty({ description: 'Whether client is already onboarded', required: false, default: false })
  @IsOptional()
  @IsBoolean()
  isAlreadyOnbord?: boolean = false;

  @ApiProperty({ description: 'Whether to perform client profiling', required: false, default: true })
  @IsOptional()
  @IsBoolean()
  doClientProfiling?: boolean = true;

  @ApiProperty({ type: [String], description: 'Custom templates for this client', required: false })
  @IsArray()
  @IsOptional()
  customTemplates?: string[];

  @ApiProperty({ description: 'Transition ID for client transitions', required: false })
  @IsString()
  @IsOptional()
  transitionId?: string;

  @ApiProperty({
    description: 'Whether to send ADV2B attachment in welcome emails',
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  sendAdv2b?: boolean = true;

  @ApiProperty({
    enum: NotificationTypeEnum,
    isArray: true,
    enumName: 'NotificationTypeEnum',
    description: 'Notification methods such as email or sms.',
    required: false
  })
  @IsArray()
  @IsEnum(NotificationTypeEnum, { each: true })
  @IsOptional()
  notificationMethods?: NotificationTypeEnum[];
}

