import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ClientsV2Service } from '../../services/v2/clients-v2.service';
import { CreateClientDto } from '../../dto/v2/create-client.dto';
import { UpdateClientDto } from '../../dto/v2/update-client.dto';
import { GetClientsQueryDto } from '../../dto/v2/get-clients.dto';
import { Roles } from 'src/shared/guards/roles.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller({ path: '/organisations/:organisationId/clients', version: '2' })
@ApiTags('Clients V2')
export class ClientsV2Controller {
  constructor(private readonly clientsService: ClientsV2Service) {}

  @Post()
  @Roles(RolesEnum.Representative, RolesEnum.CompanyAdmin)
  @ApiOperation({ summary: 'Create a new client with V2 template-based interviews' })
  async create(
    @Param('organisationId') organisationId: string,
    @Body() createClientDto: CreateClientDto,
  ) {
    return this.clientsService.create(organisationId, createClientDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all clients for the logged-in advisor' })
  async findAllByAdvisor(
    @Param('organisationId') organisationId: string,
    @Query() query: GetClientsQueryDto,
  ) {
    return this.clientsService.findAllByLoggedInAdvisor(query, organisationId);
  }

  @Get(':clientId')
  @ApiOperation({ summary: 'Get a specific client by ID' })
  async findOne(@Param('clientId') clientId: string) {
    return this.clientsService.findOne({ _id: clientId });
  }

  @Patch(':clientId')
  @Roles(RolesEnum.Representative, RolesEnum.CompanyAdmin)
  @ApiOperation({ summary: 'Update a client' })
  async update(
    @Param('clientId') clientId: string,
    @Body() updateClientDto: UpdateClientDto,
  ) {
    return this.clientsService.update(clientId, updateClientDto);
  }

  @Delete(':clientId')
  @Roles(RolesEnum.Representative, RolesEnum.CompanyAdmin)
  @ApiOperation({ summary: 'Remove a client' })
  async remove(
    @Param('clientId') clientId: string,
  ) {
    return this.clientsService.remove(clientId);
  }

  @Post(':clientId/interviews')
  @Roles(RolesEnum.Representative, RolesEnum.CompanyAdmin)
  @ApiOperation({ summary: 'Create V2 interviews for a client' })
  async createInterview(
    @Param('clientId') clientId: string,
    @Body() createClientDto: CreateClientDto,
  ) {
    const client = await this.clientsService.findOne({ _id: clientId }, undefined, false, true);
    if (!client) {
      throw new Error('Client not found');
    }
    return this.clientsService.createV2Interviews(client, createClientDto);
  }

  @Get(':clientId/interview/reviewEnvelope')
  @Roles(RolesEnum.Representative, RolesEnum.CompanyAdmin)
  @ApiOperation({ summary: 'Get DocuSign envelope review URL for advisor' })
  @ApiResponse({ status: 200, description: 'Returns DocuSign sender view URL for envelope review' })
  reviewEnvelope(@Param('clientId') clientId: string) {
    return this.clientsService.reviewEnvelope(clientId);
  }
}