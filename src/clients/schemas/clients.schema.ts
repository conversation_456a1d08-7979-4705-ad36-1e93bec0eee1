import { Factory, DataFactory } from 'nestjs-seeder';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import {
  BaseAccount,
  BaseClient,
  BaseClientContact,
} from './clients.base.schema';
import { NotificationTypeEnum } from '../types/notification-types.enum';

@Schema()
export class Account extends BaseAccount {
  @Factory((faker) => faker.finance.accountNumber())
  @Prop({ required: false })
  masterAccountNumber: string;

  @Factory([])
  @Prop({ type: Array, enum: AccountFeaturesEnum, default: [] })
  features: AccountFeaturesEnum[];

  @Factory((faker) =>  faker.finance.amount({ min: 5, max: 10, dec: 0 }))
  @Prop({ required: false })
  advisoryRate?: number;

  @Prop({ 
    type: String,
    ref: 'InterviewTemplateV2'
  })
  templateId?: string;
}

export const AccountSchema = SchemaFactory.createForClass(Account);

@Schema({ _id: false })
class InterviewStatus extends Document {
  @Factory('Not Started') // #TODO: Create enum
  @Prop({ required: true, default: 'Not Started' }) // Set default value
  status: string;

  @Factory((faker) => faker.number.int({ min: 0, max: 0 })) // Default percentage
  @Prop({ required: true, default: 0 }) // Set default value
  percentageCompleted: number;

  @Factory((faker) => faker.date.recent()) // Default timestamp
  @Prop({ required: true, default: () => new Date() }) // Set default value
  timestamp: Date;
}

@Schema({ _id: false })
export class ClientContact extends BaseClientContact {
  @Factory(false)
  @Prop({ default: false })
  skipContactInterview?: boolean;

  @Factory(
    (_, context) => DataFactory.createForClass(Account).generate(1, context)[0],
  )
  @Prop({ type: [AccountSchema], required: true })
  accounts: Account[];

  @Factory('181')
  @Prop()
  crmClientId: string;

  @Factory((faker) => faker.internet.email())
  @Prop({ required: false })
  email?: string;

  @Prop({ required: false })
  mobile?: string;
}

@Schema({ timestamps: true })
export class Client extends BaseClient {
  @Factory(
    (_, context) =>
      DataFactory.createForClass(ClientContact).generate(1, context)[0],
  )
  @Prop({ type: ClientContact, required: true })
  primaryContact: ClientContact;

  @Factory(
    (_, context) =>
      DataFactory.createForClass(ClientContact).generate(1, context)[0],
  )
  @Prop({ type: ClientContact })
  secondaryContact?: ClientContact;

  @Factory(
    (_, context) =>
      DataFactory.createForClass(InterviewStatus).generate(1, context)[0],
  )
  @Prop({ type: InterviewStatus, default: () => ({}) })
  interviewStatus?: InterviewStatus;

  // Added properties with defaults
  @Factory([NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS])
  @Prop({
    type: [String],
    enum: Object.values(NotificationTypeEnum),
    default: [NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS]
  })
  notificationMethods?: NotificationTypeEnum[];

  @Factory([])
  @Prop({ type: [String], default: [] })
  customTemplates?: string[];

  @Factory(false)
  @Prop({ type: Boolean, default: false })
  doClientProfiling?: boolean;

  @Factory('v2')
  @Prop({ required: true, default: 'v1' })
  apiVersion: string;

  @Prop({ 
    type: Object,
    default: {}
  })
  templateConfiguration?: {
    baseTemplateId: string;
    accountTemplates: Array<{
      accountId: string;
      templateId: string;
      appliedAt: Date;
    }>;
  };
}

export const ClientSchema = SchemaFactory.createForClass(Client);

// Add indexes for improving query performance
ClientSchema.index({ 'primaryContact.crmClientId': 1, status: 1 });
ClientSchema.index({ 'secondaryContact.crmClientId': 1, status: 1 });

// CRITICAL FIX: Add unique constraints to prevent duplicate clients
// Prevent duplicate clients with same email in same organization
ClientSchema.index(
  { 'primaryContact.email': 1, organisationId: 1 }, 
  { 
    unique: true, 
    sparse: true, // Allow null emails but enforce uniqueness for non-null
    name: 'unique_primary_email_per_org'
  }
);

// Prevent duplicate clients with same secondary email in same organization  
ClientSchema.index(
  { 'secondaryContact.email': 1, organisationId: 1 }, 
  { 
    unique: true, 
    sparse: true,
    name: 'unique_secondary_email_per_org'
  }
);
