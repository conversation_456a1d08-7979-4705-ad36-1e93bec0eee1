import { isEmpty } from 'lodash';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/v1/interview-data.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';

export const primaryContactBooleanMappers = {
  'primaryContact.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.EMPLOYED;
    },
  },
  'primaryContact.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.BUSINESS_OWNER.toLowerCase());
    },
  },
  'primaryContact.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.BUSINESS_OWNER.toLowerCase()) || 
             jobDescription?.includes(JobDescription.SELF_EMPLOYED.toLowerCase());
    },
  },
  'primaryContact.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.CLERICAL.toLowerCase()) || 
             jobDescription?.includes(JobDescription.ADMINISTRATIVE_SERVICES.toLowerCase());
    },
  },
  'primaryContact.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.CONSULTANT.toLowerCase());
    },
  },
  'primaryContact.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.EDUCATOR.toLowerCase());
    },
  },
  'primaryContact.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase().includes(JobDescription.EXECUTIVE.toLowerCase());
    },
  },
  'primaryContact.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.FINANCIAL_SERVICES.toLowerCase());
    },
  },
  'primaryContact.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE.toLowerCase());
    },
  },
  'primaryContact.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.HOMEMAKER.toLowerCase());
    },
  },
  'primaryContact.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL.toLowerCase()) || 
             jobDescription?.includes(JobDescription.IT_PROFESSIONAL.toLowerCase());
    },
  },
  'primaryContact.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.LEGAL_PROFESSIONAL.toLowerCase());
    },
  },
  'primaryContact.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.MEDICAL_PROFESSIONAL.toLowerCase());
    },
  },
  'primaryContact.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.MILITARY.toLowerCase());
    },
  },
  'primaryContact.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.NOTEMPLOYED;
    },
  },
  'primaryContact.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.OTHER_PROFESSIONAL.toLowerCase());
    },
  },
  'primaryContact.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.RETIRED;
    },
  },
  'primaryContact.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.SALES_MARKETING.toLowerCase()) || 
             jobDescription?.includes(JobDescription.SALES_MARKETING_2.toLowerCase());
    },
  },
  'primaryContact.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const employmentStatus = interviewData.primaryContact?.additionalInfo?.employmentStatus;
      return employmentStatus === GenericEmploymentStatusEnum.SELF_EMPLOYED || 
             employmentStatus === GenericEmploymentStatusEnum.SELF_EMPLOYED_SO;
    },
  },
  'primaryContact.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.STUDENT.toLowerCase());
    },
  },
  'primaryContact.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.TRADE_SERVICE_CAREER.toLowerCase());
    },
  },
  'primaryContact.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.US_GOVERNMENT_EMPLOYEE.toLowerCase());
    },
  },
  'primaryContact.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact?.citizenship === RedtailCitizenshipEnum.US;
    },
  },
  'primaryContact.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => true,
  },
  'primaryContact.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(interviewData.primaryContact?.additionalInfo?.companyAssociation);
    },
  },
  'primaryContact.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(interviewData.primaryContact?.additionalInfo?.industryAffiliation);
    },
  },
  'primaryContact.account#.beneficiaries#.isPrimary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary?.type === GenericCrmBeneficiaryEnum.Primary;
    },
  },
  'primaryContact.account#.beneficiaries#.isContingent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary?.type === GenericCrmBeneficiaryEnum.Contingent;
    },
  },
};
