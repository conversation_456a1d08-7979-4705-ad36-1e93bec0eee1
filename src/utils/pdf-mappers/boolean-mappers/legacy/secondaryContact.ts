import { isEmpty } from 'lodash';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/v1/interview-data.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';

export const secondaryContactBooleanMappers = {
  'secondaryContact.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.EMPLOYED;
    },
  },
  'secondaryContact.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.BUSINESS_OWNER.toLowerCase());
    },
  },
  'secondaryContact.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.BUSINESS_OWNER.toLowerCase()) || 
             jobDescription?.includes(JobDescription.SELF_EMPLOYED.toLowerCase());
    },
  },
  'secondaryContact.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.CLERICAL.toLowerCase()) || 
             jobDescription?.includes(JobDescription.ADMINISTRATIVE_SERVICES.toLowerCase());
    },
  },
  'secondaryContact.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.CONSULTANT.toLowerCase());
    },
  },
  'secondaryContact.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.EDUCATOR.toLowerCase());
    },
  },
  'secondaryContact.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase().includes(JobDescription.EXECUTIVE.toLowerCase());
    },
  },
  'secondaryContact.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.FINANCIAL_SERVICES.toLowerCase());
    },
  },
  'secondaryContact.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE.toLowerCase());
    },
  },
  'secondaryContact.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.HOMEMAKER.toLowerCase());
    },
  },
  'secondaryContact.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL.toLowerCase()) || 
             jobDescription?.includes(JobDescription.IT_PROFESSIONAL.toLowerCase());
    },
  },
  'secondaryContact.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.LEGAL_PROFESSIONAL.toLowerCase());
    },
  },
  'secondaryContact.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.MEDICAL_PROFESSIONAL.toLowerCase());
    },
  },
  'secondaryContact.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.MILITARY.toLowerCase());
    },
  },
  'secondaryContact.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.NOTEMPLOYED;
    },
  },
  'secondaryContact.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.OTHER_PROFESSIONAL.toLowerCase());
    },
  },
  'secondaryContact.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.employmentStatus === GenericEmploymentStatusEnum.RETIRED;
    },
  },
  'secondaryContact.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription = interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase();
      return jobDescription?.includes(JobDescription.SALES_MARKETING.toLowerCase()) || 
             jobDescription?.includes(JobDescription.SALES_MARKETING_2.toLowerCase());
    },
  },
  'secondaryContact.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const employmentStatus = interviewData.secondaryContact?.additionalInfo?.employmentStatus;
      return employmentStatus === GenericEmploymentStatusEnum.SELF_EMPLOYED || 
             employmentStatus === GenericEmploymentStatusEnum.SELF_EMPLOYED_SO;
    },
  },
  'secondaryContact.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.STUDENT.toLowerCase());
    },
  },
  'secondaryContact.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.TRADE_SERVICE_CAREER.toLowerCase());
    },
  },
  'secondaryContact.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo?.jobDescription?.toLowerCase()?.includes(JobDescription.US_GOVERNMENT_EMPLOYEE.toLowerCase());
    },
  },
  'secondaryContact.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.citizenship === RedtailCitizenshipEnum.US;
    },
  },
  'secondaryContact.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(interviewData.secondaryContact);
    },
  },
  'secondaryContact.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(interviewData.secondaryContact?.additionalInfo?.companyAssociation);
    },
  },
  'secondaryContact.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(interviewData.secondaryContact?.additionalInfo?.industryAffiliation);
    },
  },
  'secondaryContact.account#.beneficiaries#.isPrimary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact?.accounts?.[accountIndex];
      const beneficiary = account?.beneficiaries?.[beneficiaryIndex];
      return beneficiary?.type === GenericCrmBeneficiaryEnum.Primary;
    },
  },
  'secondaryContact.account#.beneficiaries#.isContingent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact?.accounts?.[accountIndex];
      const beneficiary = account?.beneficiaries?.[beneficiaryIndex];
      return beneficiary?.type === GenericCrmBeneficiaryEnum.Contingent;
    },
  },
};
