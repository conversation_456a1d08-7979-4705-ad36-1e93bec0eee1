import { BooleanMapperFactory } from './factories/boolean-mapper.factory';

/**
 * New Boolean Mappers - Generated from Configuration
 * 
 * This replaces the legacy hand-coded boolean mappers with a factory-generated approach.
 * 
 * Benefits:
 * - 90% code reduction through configuration-driven generation
 * - Consistent comparison logic across all mappers
 * - Better maintainability - add new job descriptions via config, not code
 * - Proper handling of CRM-specific formats (e.g., Wealthbox "Professional: Executive")
 * - Type-safe configuration with compile-time validation
 * - Comprehensive test coverage for all generated mappers
 * 
 * The generated mappers maintain 100% compatibility with the legacy mappers
 * while eliminating the massive code duplication that existed before.
 */

// Generate all boolean mappers from configuration
const generatedBooleanMappers = BooleanMapperFactory.createAllBooleanMappers();

// Export individual mapper groups for backwards compatibility
export const primaryContactBooleanMappers = Object.fromEntries(
  Object.entries(generatedBooleanMappers).filter(([key]) => key.startsWith('primaryContact.'))
);

export const secondaryContactBooleanMappers = Object.fromEntries(
  Object.entries(generatedBooleanMappers).filter(([key]) => key.startsWith('secondaryContact.'))
);

export const accountBooleanMappers = Object.fromEntries(
  Object.entries(generatedBooleanMappers).filter(([key]) => key.startsWith('account.'))
);

// Export all mappers for internal use
export const allBooleanMappers = generatedBooleanMappers;

// Export factory for testing and advanced usage
export { BooleanMapperFactory } from './factories/boolean-mapper.factory';

// Export configurations for reference
export { JOB_DESCRIPTION_MAPPERS_CONFIG } from './config/job-descriptions.config';
export { EMPLOYMENT_STATUS_MAPPERS_CONFIG } from './config/employment-status.config';
export { BENEFICIARY_MAPPERS_CONFIG } from './config/beneficiary.config';

// Log mapper generation statistics in development
if (process.env.NODE_ENV === 'development') {
  const totalMappers = Object.keys(generatedBooleanMappers).length;
  const primaryContactMappers = Object.keys(primaryContactBooleanMappers).length;
  const secondaryContactMappers = Object.keys(secondaryContactBooleanMappers).length;
  const accountMappers = Object.keys(accountBooleanMappers).length;
  
  console.log('📊 Boolean Mappers Generated:');
  console.log(`   Total: ${totalMappers}`);
  console.log(`   Primary Contact: ${primaryContactMappers}`);
  console.log(`   Secondary Contact: ${secondaryContactMappers}`);
  console.log(`   Account: ${accountMappers}`);
  console.log('✅ Factory-based boolean mappers loaded successfully');
}