import { InterviewDataWithCrmInfo } from 'src/interviews/types/v1/interview-data.type';
import { JOB_DESCRIPTION_MAPPERS_CONFIG } from '../config/job-descriptions.config';
import { EMPLOYMENT_STATUS_MAPPERS_CONFIG } from '../config/employment-status.config';
import { BENEFICIARY_MAPPERS_CONFIG } from '../config/beneficiary.config';
import {
  extractPrimaryContactData,
  extractSecondaryContactData,
  extractAccountApplicantData,
  extractAccountCoapplicantData,
  extractAccountData,
  extractBeneficiaryData,
  extractPrimaryContactBeneficiaryData,
  extractSecondaryContactBeneficiaryData,
  ContactData,
} from './data-extractors';
import {
  matchesJobDescription,
  matchesEmploymentStatus,
  matchesBeneficiaryType,
  matchesString,
} from './comparison-utils';
import { isEmpty } from 'lodash';
import { AccountOwnershipEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { DBAccountType } from 'src/integrations/crm/types/accounts/db-account-type.enum';

/**
 * Factory class to generate boolean mappers from configuration
 * This eliminates the need to hand-code repetitive boolean mappers
 */
export class BooleanMapperFactory {
  /**
   * Generate all job description mappers for a specific contact type
   */
  static createJobDescriptionMappers(
    contactType: 'primaryContact' | 'secondaryContact' | 'account.applicant' | 'account.coapplicant',
    extractorFunction: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => ContactData,
  ) {
    const mappers: Record<string, any> = {};

    JOB_DESCRIPTION_MAPPERS_CONFIG.forEach(config => {
      const mapperKey = `${contactType}.${config.name}`;
      
      mappers[mapperKey] = {
        callback: (
          fileName: string,
          interviewData: InterviewDataWithCrmInfo,
          accountIndex: number,
        ) => {
          const contactData = extractorFunction(fileName, interviewData, accountIndex);
          return matchesJobDescription(contactData.jobDescription, config.jobDescriptions);
        },
      };
    });

    return mappers;
  }

  /**
   * Generate all employment status mappers for a specific contact type
   */
  static createEmploymentStatusMappers(
    contactType: 'primaryContact' | 'secondaryContact' | 'account.applicant' | 'account.coapplicant',
    extractorFunction: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => ContactData,
  ) {
    const mappers: Record<string, any> = {};

    EMPLOYMENT_STATUS_MAPPERS_CONFIG.forEach(config => {
      const mapperKey = `${contactType}.${config.name}`;
      
      mappers[mapperKey] = {
        callback: (
          fileName: string,
          interviewData: InterviewDataWithCrmInfo,
          accountIndex: number,
        ) => {
          const contactData = extractorFunction(fileName, interviewData, accountIndex);
          return matchesEmploymentStatus(contactData.employmentStatus, config.employmentStatuses);
        },
      };
    });

    return mappers;
  }

  /**
   * Generate beneficiary type mappers for account beneficiaries
   */
  static createAccountBeneficiaryMappers() {
    const mappers: Record<string, any> = {};

    BENEFICIARY_MAPPERS_CONFIG.forEach(config => {
      const mapperKey = `account.beneficiaries#.${config.name}`;
      
      mappers[mapperKey] = {
        callback: (
          fileName: string,
          interviewData: InterviewDataWithCrmInfo,
          accountIndex: number,
          beneficiaryIndex: number,
        ) => {
          const beneficiary = extractBeneficiaryData(fileName, interviewData, accountIndex, beneficiaryIndex);
          return matchesBeneficiaryType(beneficiary?.type, config.beneficiaryTypes);
        },
      };
    });

    return mappers;
  }

  /**
   * Generate beneficiary type mappers for primary contact account beneficiaries
   */
  static createPrimaryContactBeneficiaryMappers() {
    const mappers: Record<string, any> = {};

    BENEFICIARY_MAPPERS_CONFIG.forEach(config => {
      const mapperKey = `primaryContact.account#.beneficiaries#.${config.name}`;
      
      mappers[mapperKey] = {
        callback: (
          fileName: string,
          interviewData: InterviewDataWithCrmInfo,
          accountIndex: number,
          beneficiaryIndex: number,
        ) => {
          const beneficiary = extractPrimaryContactBeneficiaryData(fileName, interviewData, accountIndex, beneficiaryIndex);
          return matchesBeneficiaryType(beneficiary?.type, config.beneficiaryTypes);
        },
      };
    });

    return mappers;
  }

  /**
   * Generate beneficiary type mappers for secondary contact account beneficiaries
   */
  static createSecondaryContactBeneficiaryMappers() {
    const mappers: Record<string, any> = {};

    BENEFICIARY_MAPPERS_CONFIG.forEach(config => {
      const mapperKey = `secondaryContact.account#.beneficiaries#.${config.name}`;
      
      mappers[mapperKey] = {
        callback: (
          fileName: string,
          interviewData: InterviewDataWithCrmInfo,
          accountIndex: number,
          beneficiaryIndex: number,
        ) => {
          const beneficiary = extractSecondaryContactBeneficiaryData(fileName, interviewData, accountIndex, beneficiaryIndex);
          return matchesBeneficiaryType(beneficiary?.type, config.beneficiaryTypes);
        },
      };
    });

    return mappers;
  }

  /**
   * Generate account type mappers
   */
  static createAccountTypeMappers() {
    return {
      'account.isIndividual': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.ownership, AccountOwnershipEnum.Individual);
        },
      },
      'account.isJtwros': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.ownership, AccountOwnershipEnum.JTWROS);
        },
      },
      'account.isJointTenantsInCommon': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.ownership, AccountOwnershipEnum.JTIC);
        },
      },
      'account.isTenantsByEntirety': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.ownership, AccountOwnershipEnum.entirety);
        },
      },
      'account.isRothIra': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.type, DBAccountType.roth);
        },
      },
      'account.isIra': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const accountData = extractAccountData(fileName, interviewData, accountIndex);
          return matchesString(accountData.type, DBAccountType.ira);
        },
      },
    };
  }

  /**
   * Generate citizenship mappers for primary and secondary contacts
   */
  static createCitizenshipMappers() {
    return {
      'primaryContact.isUSCitizen': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          // For now, return true as per existing logic
          return true;
        },
      },
      'primaryContact.isUSResident': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          // For now, return true as per existing logic
          return true;
        },
      },
      'secondaryContact.isUSCitizen': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractSecondaryContactData(fileName, interviewData, accountIndex);
          return matchesString(contactData.citizenship, 'US');
        },
      },
      'secondaryContact.isUSResident': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractSecondaryContactData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.firstName); // If secondary contact exists
        },
      },
    };
  }

  /**
   * Generate company association mappers
   */
  static createCompanyAssociationMappers() {
    return {
      'primaryContact.hasPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractPrimaryContactData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'primaryContact.hasIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractPrimaryContactData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
      'secondaryContact.hasPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractSecondaryContactData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'secondaryContact.hasIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractSecondaryContactData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
      // Account applicant company association mappers
      'account.applicant.hasPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountApplicantData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'account.applicant.hasNoPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountApplicantData(fileName, interviewData, accountIndex);
          return isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'account.applicant.hasIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountApplicantData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
      'account.applicant.hasNoIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountApplicantData(fileName, interviewData, accountIndex);
          return isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
      // Account coapplicant company association mappers
      'account.coapplicant.hasPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountCoapplicantData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'account.coapplicant.hasNoPublicCompanyAssociation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountCoapplicantData(fileName, interviewData, accountIndex);
          return isEmpty(contactData.additionalInfo?.companyAssociation);
        },
      },
      'account.coapplicant.hasIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountCoapplicantData(fileName, interviewData, accountIndex);
          return !isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
      'account.coapplicant.hasNoIndustryAffiliation': {
        callback: (fileName: string, interviewData: InterviewDataWithCrmInfo, accountIndex: number) => {
          const contactData = extractAccountCoapplicantData(fileName, interviewData, accountIndex);
          return isEmpty(contactData.additionalInfo?.industryAffiliation);
        },
      },
    };
  }

  /**
   * Generate all boolean mappers
   */
  static createAllBooleanMappers() {
    return {
      // Job description mappers
      ...this.createJobDescriptionMappers('primaryContact', extractPrimaryContactData),
      ...this.createJobDescriptionMappers('secondaryContact', extractSecondaryContactData),
      ...this.createJobDescriptionMappers('account.applicant', extractAccountApplicantData),
      ...this.createJobDescriptionMappers('account.coapplicant', extractAccountCoapplicantData),

      // Employment status mappers
      ...this.createEmploymentStatusMappers('primaryContact', extractPrimaryContactData),
      ...this.createEmploymentStatusMappers('secondaryContact', extractSecondaryContactData),
      ...this.createEmploymentStatusMappers('account.applicant', extractAccountApplicantData),
      ...this.createEmploymentStatusMappers('account.coapplicant', extractAccountCoapplicantData),

      // Beneficiary mappers
      ...this.createAccountBeneficiaryMappers(),
      ...this.createPrimaryContactBeneficiaryMappers(),
      ...this.createSecondaryContactBeneficiaryMappers(),

      // Account type mappers
      ...this.createAccountTypeMappers(),

      // Citizenship mappers
      ...this.createCitizenshipMappers(),

      // Company association mappers
      ...this.createCompanyAssociationMappers(),
    };
  }
}