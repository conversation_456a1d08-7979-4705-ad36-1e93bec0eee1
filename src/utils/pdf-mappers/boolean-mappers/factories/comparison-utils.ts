import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';

/**
 * Comparison utilities for boolean mappers
 * These handle the actual comparison logic with proper null safety and case sensitivity
 */

/**
 * Safe string conversion with null checks
 */
function safeString(value: unknown): string {
  if (value == null) return '';
  return String(value).trim();
}

/**
 * Safe lowercase conversion with null checks
 */
function safeLowercase(value: unknown): string {
  return safeString(value).toLowerCase();
}

/**
 * Check if job description matches any of the expected values
 * Uses includes() to handle CRM-specific formats like "Professional: Executive"
 */
export function matchesJobDescription(
  storedValue: unknown,
  expectedValues: readonly JobDescription[],
): boolean {
  const stored = safeLowercase(storedValue);
  if (!stored) return false;

  return expectedValues.some(expected => 
    stored.includes(safeLowercase(expected))
  );
}

/**
 * Check if employment status matches any of the expected values
 * Uses exact match for employment status
 */
export function matchesEmploymentStatus(
  storedValue: unknown,
  expectedValues: readonly GenericEmploymentStatusEnum[],
): boolean {
  const stored = safeLowercase(storedValue);
  if (!stored) return false;

  return expectedValues.some(expected => 
    stored === safeLowercase(expected)
  );
}

/**
 * Check if beneficiary type matches any of the expected values
 * Uses exact match for beneficiary types
 */
export function matchesBeneficiaryType(
  storedValue: unknown,
  expectedValues: readonly GenericCrmBeneficiaryEnum[],
): boolean {
  const stored = safeLowercase(storedValue);
  if (!stored) return false;

  return expectedValues.some(expected => 
    stored === safeLowercase(expected)
  );
}

/**
 * Check if citizenship matches expected value
 * Uses exact match for citizenship
 */
export function matchesCitizenship(
  storedValue: unknown,
  expectedValue: RedtailCitizenshipEnum,
): boolean {
  const stored = safeLowercase(storedValue);
  if (!stored) return false;

  return stored === safeLowercase(expectedValue);
}

/**
 * Generic string comparison with case-insensitive matching
 */
export function matchesString(
  storedValue: unknown,
  expectedValue: string,
): boolean {
  const stored = safeLowercase(storedValue);
  const expected = safeLowercase(expectedValue);
  
  if (!stored || !expected) return false;
  
  return stored === expected;
}

/**
 * Check if string contains expected value (case-insensitive)
 */
export function containsString(
  storedValue: unknown,
  expectedValue: string,
): boolean {
  const stored = safeLowercase(storedValue);
  const expected = safeLowercase(expectedValue);
  
  if (!stored || !expected) return false;
  
  return stored.includes(expected);
}