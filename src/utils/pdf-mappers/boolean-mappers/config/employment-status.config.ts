import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';

/**
 * Configuration for employment status boolean mappers
 * This eliminates the need to hand-code each employment status mapper
 */
export const EMPLOYMENT_STATUS_MAPPERS_CONFIG = [
  {
    name: 'isEmployed',
    employmentStatuses: [GenericEmploymentStatusEnum.EMPLOYED],
  },
  {
    name: 'isRetired',
    employmentStatuses: [GenericEmploymentStatusEnum.RETIRED],
  },
  {
    name: 'isSelfEmployed',
    employmentStatuses: [GenericEmploymentStatusEnum.SELF_EMPLOYED, GenericEmploymentStatusEnum.SELF_EMPLOYED_SO],
  },
  {
    name: 'isNotEmployed',
    employmentStatuses: [GenericEmploymentStatusEnum.NOTEMPLOYED],
  },
  {
    name: 'isStudent',
    employmentStatuses: [GenericEmploymentStatusEnum.STUDENT],
  },
  {
    name: 'isHomemaker',
    employmentStatuses: [GenericEmploymentStatusEnum.HOMEMAKER],
  },
] as const;

export type EmploymentStatusMapperName = typeof EMPLOYMENT_STATUS_MAPPERS_CONFIG[number]['name'];