import { JobDescription } from 'src/integrations/crm/types/job-description.enum';

/**
 * Configuration for job description boolean mappers
 * This eliminates the need to hand-code each job description mapper
 */
export const JOB_DESCRIPTION_MAPPERS_CONFIG = [
  {
    name: 'isBusinessOwner',
    jobDescriptions: [JobDescription.BUSINESS_OWNER],
  },
  {
    name: 'isBusinessOwnerSelfEmployed',
    jobDescriptions: [JobDescription.BUSINESS_OWNER, JobDescription.SELF_EMPLOYED],
  },
  {
    name: 'isClerical',
    jobDescriptions: [JobDescription.CLERICAL, JobDescription.ADMINISTRATIVE_SERVICES],
  },
  {
    name: 'isConsultant',
    jobDescriptions: [JobDescription.CONSULTANT],
  },
  {
    name: 'isEducator',
    jobDescriptions: [JobDescription.EDUCATOR],
  },
  {
    name: 'isExecutive',
    jobDescriptions: [JobDescription.EXECUTIVE],
  },
  {
    name: 'isFinancialServices',
    jobDescriptions: [JobDescription.FINANCIAL_SERVICES],
  },
  {
    name: 'isForeignGovernmentEmployee',
    jobDescriptions: [JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE],
  },
  {
    name: 'isHomemaker',
    jobDescriptions: [JobDescription.HOMEMAKER],
  },
  {
    name: 'isInformationTechnologyProfessional',
    jobDescriptions: [JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL, JobDescription.IT_PROFESSIONAL],
  },
  {
    name: 'isLegalProfessional',
    jobDescriptions: [JobDescription.LEGAL_PROFESSIONAL],
  },
  {
    name: 'isMedicalProfessional',
    jobDescriptions: [JobDescription.MEDICAL_PROFESSIONAL],
  },
  {
    name: 'isMilitary',
    jobDescriptions: [JobDescription.MILITARY],
  },
  {
    name: 'isOtherProfessional',
    jobDescriptions: [JobDescription.OTHER_PROFESSIONAL],
  },
  {
    name: 'isSalesMarketing',
    jobDescriptions: [JobDescription.SALES_MARKETING, JobDescription.SALES_MARKETING_2],
  },
  {
    name: 'isSelfEmployed',
    jobDescriptions: [JobDescription.BUSINESS_OWNER, JobDescription.SELF_EMPLOYED],
  },
  {
    name: 'isStudent',
    jobDescriptions: [JobDescription.STUDENT],
  },
  {
    name: 'isTradeServiceCareer',
    jobDescriptions: [JobDescription.TRADE_SERVICE_CAREER],
  },
  {
    name: 'isUSGovernmentEmployee',
    jobDescriptions: [JobDescription.US_GOVERNMENT_EMPLOYEE],
  },
  {
    name: 'isAccountingProfessional',
    jobDescriptions: [JobDescription.ACCOUNTING_PROFESSIONAL],
  },
] as const;

export type JobDescriptionMapperName = typeof JOB_DESCRIPTION_MAPPERS_CONFIG[number]['name'];