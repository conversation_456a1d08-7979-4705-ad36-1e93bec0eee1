import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';

/**
 * Configuration for beneficiary type boolean mappers
 */
export const BENEFICIARY_MAPPERS_CONFIG = [
  {
    name: 'isPrimary',
    beneficiaryTypes: [GenericCrmBeneficiaryEnum.Primary],
  },
  {
    name: 'isContingent',
    beneficiaryTypes: [GenericCrmBeneficiaryEnum.Contingent],
  },
] as const;

export type BeneficiaryMapperName = typeof BENEFICIARY_MAPPERS_CONFIG[number]['name'];