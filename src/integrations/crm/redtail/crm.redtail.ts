import { HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { GetClientsQueryDto } from 'src/advisors/dto/get-clients.dto';
import { GetCrmUsersQueryDto } from 'src/advisors/dto/get-crm-users.dto';
import {
  AuthScheme,
  Basic,
  Credentials,
  CrmCredentials,
  RedtailCredentials,
  UserKey,
} from 'src/auth/auth.types';
import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import {
  CrmContactDto,
  CreateContactDto,
  CreateContactResponseDto,
  UpdateContactDto,
  UpdateContactResponseDto,
} from '../types/create-contact.dto';
import {
  EnrichedClient,
  EnrichedContact,
} from 'src/clients/dto/v1/get-clients.dto';
import { CRM, PageUpdateMapper } from 'src/integrations/crm/crm.interface';
import { RedtailAdapter } from 'src/integrations/crm/redtail/crm.redtail.adapter';
import { UpdateContactRequestDto } from 'src/integrations/crm/redtail/dto/requests/update-contact.dto';
import { UpdatePersonalProfileRequestDto } from 'src/integrations/crm/redtail/dto/requests/update-personal-info.dto';
import { RedtailGetContactResponseDto } from 'src/integrations/crm/redtail/dto/responses/contact/get-contact-response.dto';
import {
  GetPersonalProfileResponse,
  GetPersonalProfileResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/contact/personal-profile/get-personal-profile-response.dto';
import { RedtailAccountService } from 'src/integrations/crm/redtail/services/account.service';
import { RedtailTaskActivityService } from 'src/integrations/crm/redtail/services/activity.service';
import { RedtailAddressService } from 'src/integrations/crm/redtail/services/address.service';
import { RedtailEmailAddressService } from 'src/integrations/crm/redtail/services/email.service';
import { RedtailImportantInformationService } from 'src/integrations/crm/redtail/services/important-info.service';
import { RedtailNotesService } from 'src/integrations/crm/redtail/services/notes.service';
import { RedtailPhoneNumberService } from 'src/integrations/crm/redtail/services/phone.service';
import { RedtailTagService } from 'src/integrations/crm/redtail/services/tag.service';
import { RedtailUdfService } from 'src/integrations/crm/redtail/services/udf.service';
import {
  ActivityAttendeeTypeEnum,
  ActivityRepeatEnum,
} from 'src/integrations/crm/redtail/types/activity.type';
import { RedtailNoteTypeEnum } from 'src/integrations/crm/redtail/types/note.type';
import { mapToSuffixId } from 'src/integrations/crm/redtail/utils/suffix-mapper';
import {
  GenericCrmAccount,
  GenericCrmBeneficiaryEnum,
} from 'src/integrations/crm/types/accounts/crm-account.type';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

import { SuffixEnum } from 'src/shared/types/general/suffix.enum';
import { CRMEnum, CRMType } from 'src/shared/types/integrations';
import { camelize, isObjectOfType } from 'src/utils';
import { CrmPageInfoUpdateDto } from '../types/page-info-update.dto';
import { AuthenticationResponseDto } from './dto/responses/authentication/authentication-response.dto';
import { RedtailCreateContactResponseDto } from './dto/responses/contact/create-contact-response.dto';
import { RedtailGetContactsSearchResponseDto } from './dto/responses/contact/get-contacts-response.dto';
import { RedtailUpdateContactResponseDto } from './dto/responses/contact/update-contact-response.dto';
import { redtailMappers } from './form-mappers';
import { redtailMappersV2 } from './v2/form-mappers';
import { PageUpdateMapperV2, V2MapperContext } from '../types/form-update-mapper-v2';
import { InterviewPageDefV2 } from 'src/interview-templates/schemas/v2/interview-page-def.schema';
import { Client } from 'src/clients/schemas/clients.schema';
import { RedtailFamilyService } from './services/family.service';
import { RedtailContact } from './types/contact.type';
import {
  DefaultTagsEnum,
  RedtailAccountTypeEnum,
  RedtailAccountUdfNameEnum,
  RedtailCRMEndpointsEnum,
  RedtailContactUdfNameEnum,
  RedtailCrmContactTypeEnum,
  RedtailDBAccountType,
  RedtailStatusEnum,
} from './types/enums';
import { RedtailUser } from './types/user.type';
import { Logger } from 'winston';
import { buildAccountCreationTaskDetails } from 'src/integrations/crm/utils/build-account-task';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import {
  getBeneficaryFirstName,
  getBeneficaryFullName,
  getBeneficaryLastName,
  getDobFromBeneficiaryName,
} from 'src/integrations/crm/redtail/utils/beneficiary-name-parser';
import { MailService } from 'src/notifications/mail/mail.service';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';
/**
 * Redtail CRM Integration
 *
 * @see {@link https://documenter.getpostman.com/view/7873823/SVzxXyzn#5e546892-9351-4a91-992d-9459441a0740}
 **/
export class RedtailCRM implements CRM {
  pageUpdateMapper: PageUpdateMapper;
  pageUpdateMapperV2: PageUpdateMapperV2;

  protected apiKey: string;
  protected baseUrl: string;
  protected readonly type: CRMEnum = CRMEnum.Redtail;
  protected readonly adapter = new RedtailAdapter();
  protected tenantId: string;
  
  // Services
  public redtailFamilyService: RedtailFamilyService;
  public redtailAddressService: RedtailAddressService;
  public redtailUdfService: RedtailUdfService;
  public redtailAccountsService: RedtailAccountService;
  public redtailTagService: RedtailTagService;
  public redtailActivityService: RedtailTaskActivityService;
  public redtailEmailAddressService: RedtailEmailAddressService;
  public redtailPhoneNumberService: RedtailPhoneNumberService;
  public redtailNotesService: RedtailNotesService;
  public redtailImportantInformationService: RedtailImportantInformationService;

  constructor(
    protected advisor: AdvisorWithRole,
    protected organisation: Organisation,
    protected credentials: RedtailCredentials,
    protected httpService: SuperHttpService,
    public interviewsService: InterviewsService,
    protected emailService: MailService,
    protected logger: Logger,
  ) {
    this.apiKey = process.env.REDTAIL_API_KEY;
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.tenantId = this.organisation._id.toString();
    this.redtailFamilyService = new RedtailFamilyService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailAddressService = new RedtailAddressService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailUdfService = new RedtailUdfService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailTagService = new RedtailTagService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailAccountsService = new RedtailAccountService(
      httpService,
      this.redtailUdfService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailActivityService = new RedtailTaskActivityService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailEmailAddressService = new RedtailEmailAddressService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailPhoneNumberService = new RedtailPhoneNumberService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailNotesService = new RedtailNotesService(
      httpService,
      this.getAuthHeader(this.apiKey),
      this.tenantId,
    );
    this.redtailImportantInformationService =
      new RedtailImportantInformationService(
        httpService,
        this.getAuthHeader(this.apiKey),
        this.tenantId,
      );
    this.pageUpdateMapper = this.initMappers();
    this.pageUpdateMapperV2 = this.initMappersV2();
  }

  /**
   * Initializes the CRM by setting up configuration values for contact and account UDFs,
   * and initializing tags.
   * @returns A Map object containing the configuration values for the CRM.
   */
  public async initCrm() {
    const config: Map<string, string | number> = new Map();
    const contactUdfs = await this.redtailUdfService.initContactUdfs();
    contactUdfs.forEach((udf) => {
      config.set(udf.name, udf.id);
    });

    const { accountUdfs } =
      await this.redtailUdfService.getAccountUdfDefinitions();

    if (!accountUdfs?.length) {
      this.logger.info(`[INFO: initCrm] Account UDFs: ${JSON.stringify(accountUdfs)}`);
      this.logger.warn(
        `[WARNING: initCrm] Failed to retrieve account UDFs from Redtail CRM`,
      );
    }

    accountUdfs.forEach((udf) => {
      config.set(udf.name, udf.id);
    });

    await this.redtailTagService.initTags();

    return config;
  }

  /**
   * Returns the authorization header for Redtail CRM API requests.
   * @param apiKey The API key to use for authentication.
   * @returns An object containing the Authorization header.
   * @throws HttpException if the credentials are invalid.
   */
  public getAuthHeader(apiKey: string) {
    const isBasicAuth = isObjectOfType<Basic>(this.credentials, 'username');
    const isUserKeyAuth = isObjectOfType<UserKey>(this.credentials, 'userKey');

    const authScheme = isUserKeyAuth ? AuthScheme.USER_KEY : AuthScheme.BASIC;

    if (!isBasicAuth && !isUserKeyAuth) {
      throw new HttpException('Invalid credentials', HttpStatus.UNAUTHORIZED);
    }

    const stringToEncode = authScheme === AuthScheme.BASIC
      ? `${apiKey}:${this.credentials.username}:${this.credentials.password}`
      : `${apiKey}:${(this.credentials as UserKey).userKey}`;

    const authParams = Buffer.from(stringToEncode, 'utf8').toString('base64');
    return {
      Authorization: `${authScheme} ${authParams}`,
    };
  }

  /**
   * Initializes the page update mapper for Redtail CRM.
   * @returns The page update mapper for Redtail CRM.
   */
  initMappers(): PageUpdateMapper {
    return redtailMappers.bind(this)();
  }

  /**
   * Initializes the V2 page update mappers for this RedtailCRM instance.
   * @returns A PageUpdateMapperV2 object with all the initialized mappers.
   */
  initMappersV2(): PageUpdateMapperV2 {
    return redtailMappersV2.bind(this)();
  }

  /**
   * Updates the page information for a given interview, client, organisation and page update DTO.
   * @param interview - The interview object.
   * @param client - The client object.
   * @param organisation - The organisation object.
   * @param update - The page update DTO.
   * @returns The mapped page data.
   */
  public updatePageInfo(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: CrmPageInfoUpdateDto,
  ) {
    return this.pageUpdateMapper[update.pageName].mapPageData(
      interview,
      client,
      organisation,
      update.pageData,
    );
  }

  /**
   * Updates page information for V2 interviews
   * @param pageDefinition - The page definition from the template
   * @param answers - The form answers from the client
   * @param contactType - Whether this is for primary or secondary contact
   * @param client - The client object
   * @param organisation - The organisation object
   * @param interviewId - The interview ID for context
   * @param metadata - Optional metadata about the submission
   */
  public async updatePageInfoV2(
    pageDefinition: InterviewPageDefV2,
    answers: Record<string, any>,
    contactType: 'primary' | 'secondary',
    client: Client,
    organisation: Organisation,
    interviewId: string,
    metadata?: {
      timeSpent?: number;
      deviceType?: string;
      timestamp?: Date;
    },
  ): Promise<void> {
    const mapper = this.pageUpdateMapperV2[pageDefinition.pageType];
    if (!mapper) {
      throw new Error(`No V2 mapper found for page type: ${pageDefinition.pageType}`);
    }

    const context: V2MapperContext = {
      pageDefinition,
      answers,
      contactType,
      client,
      organisation,
      interviewId,
      metadata,
    };

    return mapper.mapPageData(context);
  }

  // #####################
  // ### CREDENTIALS ###
  // #####################

  public getCredentials(): Credentials {
    return this.credentials;
  }

  public setCredentials(credentials: RedtailCredentials): void {
    this.credentials = credentials;
  }

  public getAuthenticatedUserId(): Promise<string> {
    return Promise.resolve(this.credentials.userId);
  }

  /**
   * Authenticates the user and returns an object containing the database ID, user ID, and user key.
   * @returns An object containing the database ID, user ID, and user key.
   */
  public async authenticate(): Promise<CrmCredentials> {
    try {

      if (this.credentials.userKey) {
        return this.credentials;
      }

      const response = await this.httpService.get<AuthenticationResponseDto>(
        `${this.baseUrl}/authentication`,
        this.tenantId,
        { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.GET, forceFresh: true },
        { headers: this.getAuthHeader(this.apiKey) }
      );

      if (!response?.authenticated_user) {
        this.logger.error(
          `[ERROR: authenticate] Failed to authenticate user: ${response}`,
        );
        throw new CrmAuthException(
          `Failed to authenticate user in Redtail`,
          HttpStatus.BAD_REQUEST,
        );
      }

      const {
        database_id: databaseId,
        user_id: userId,
        user_key: userKey,
      } = response.authenticated_user || {};

      this.credentials = {
        ...this.credentials,
        databaseId,
        userId,
        userKey,
      };

      return {
        databaseId,
        userId,
        userKey,
      };
    } catch (error) {
      const message = error?.response?.data?.message || error?.message;
      this.logger.error(`[ERROR: authenticate] ${error?.message}`);

      throw new CrmAuthException(
        this.parseCrmAuthError(message),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private parseCrmAuthError(message: string) {
    const error = message.toLowerCase();

    if (error.includes('lock')) {
      return 'Redtail account locked, wait 15 minutes and relink your account'
    }

    if (error.includes('invalid login')) {
      return 'Invalid Redtail Login Credentials'
    }

    return 'An unknown error occurred during CRM authentication. Please try again later.';

  }

  public async getType(): Promise<CRMType> {
    return this.type;
  }

  async sendEmail(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ) {
    // Ensure organisation is included in emailOptions
    const emailOptionsWithOrg = {
      ...emailOptions,
      organisation: this.organisation
    };
    return this.emailService.sendEmail(emailOptionsWithOrg, contact, advisorId);
  }

  // #####################
  // ###    CONTACTS   ###
  // #####################
  // TODO: Move this to another service

  public async getContact(
    id: string,
    includeAccounts: boolean = false,
    includeAdditionalInfo: boolean = false,
  ): Promise<CrmContact> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${id}`;
    const { contact } = await this.httpService.get<RedtailGetContactResponseDto>(
      url,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.GET, forceFresh: true },
      { headers: this.getAuthHeader(this.apiKey) }
    );

    const { personalProfile } = await this.getContactPersonalProfile(id);

    const crmContact: CrmContact = {
      firstName: contact.first_name,
      lastName: contact.last_name,
      middleName: contact.middle_name,
      suffix: contact.suffix,
      dob: contact.dob,
      taxId: contact.tax_id,
      citizenship: personalProfile.citizenship,
      residence: 'USA',
      additionalInfo: {
        employmentStatus: null,
        jobDescription: null,
        companyAssociation: null,
        industryAffiliation: null,
        phones: [],
        addresses: [],
        emails: [],
      },
    };

    if (includeAccounts) {
      crmContact.accounts = await this.getAccounts(id);
    }

    if (includeAdditionalInfo) {
      const udfs = await this.redtailUdfService.getContactUdfs(id);

      crmContact.additionalInfo.employmentStatus = udfs.contactUdfs.find(
        (udf) =>
          udf.contactUdfFieldName ===
          RedtailContactUdfNameEnum.EMPLOYMENT_STATUS,
      )?.fieldValue;

      crmContact.additionalInfo.industryAffiliation = udfs.contactUdfs.find(
        (udf) =>
          udf.contactUdfFieldName ===
          RedtailContactUdfNameEnum.INDUSTRY_AFFILIATION,
      )?.fieldValue;

      crmContact.additionalInfo.companyAssociation = udfs.contactUdfs.find(
        (udf) =>
          udf.contactUdfFieldName ===
          RedtailContactUdfNameEnum.COMPANY_ASSOCIATION,
      )?.fieldValue;

      crmContact.additionalInfo.jobDescription = udfs.contactUdfs.find(
        (udf) =>
          udf.contactUdfFieldName === RedtailContactUdfNameEnum.JOB_DESCRIPTION,
      )?.fieldValue;

      const phones = await this.getPhoneNumbersOfContact(id);
      crmContact.additionalInfo.phones = phones;

      const { addresses } =
        await this.redtailAddressService.getContactAddresses(id);
      crmContact.additionalInfo.addresses = addresses.map((address) =>
        this.adapter.mapAddress(address),
      );

      const emails = await this.redtailEmailAddressService.getEmailAddresses(
        id,
      );
      crmContact.additionalInfo.emails = emails?.emails?.map((email) =>
        this.adapter.mapEmail(email),
      );
    }

    return crmContact;
  }

  /**
   *
   * @param id contact id
   * @returns Array of phone numbers associated with the contact
   */
  public async getPhoneNumbersOfContact(id: string) {
    const phones = await this.redtailPhoneNumberService.getContactPhoneNumbers(
      id,
    );

    return phones.phones.map((phone) => this.adapter.mapPhone(phone));
  }

  /**
   * * @param id contact id
   * @returns Mobile phone number of contact (Any number if mobile is not present) - Undefined if no number found
   */
  public async getMainMobileOfContact(id: string) {
    const phones = await this.getPhoneNumbersOfContact(id);
    if (isEmpty(phones)) {
      throw new NotFoundException(
        `Phone number not found for contact with id ${id}`,
      );
    }
    const phoneNumber =
      phones.find((phone) => phone.type === PhoneTypeEnum.MOBILE) || phones[0];
    return phoneNumber?.number;
  }

  /**
   *
   * @param id contact id
   * @returns Main email associated with the contact
   */
  public async getMainEmailOfContact(id: string): Promise<GenericEmail> {
    const emailAddresses =
      await this.redtailEmailAddressService.getEmailAddresses(id);

    if (isEmpty(emailAddresses?.emails)) {
      return undefined;
    }

    const email =
      emailAddresses?.emails?.find((email) => email.isPrimary) ||
      emailAddresses?.emails[0];
    return email ? this.adapter.mapEmail(email) : undefined;
  }

  /**
   * Retrieves a list of contacts from Redtail CRM based on the provided query parameters.
   * @param query - The query parameters to filter the contacts by.
   * @returns A Promise that resolves to an array of Contact objects.
   */
  public async getContacts(query: GetClientsQueryDto): Promise<CrmContact[]> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/search_basic`;
    const { contacts } = await this.httpService.get<RedtailGetContactsSearchResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        entityType: 'contact',
        operation: OperationType.LIST,
        cacheKeysObject: {
          query: query.search
        },
      },
      {
        headers: {
          ...this.getAuthHeader(this.apiKey),
          pagesize: query.limit,
          include: 'phones, emails, addresses',
        },
        params: { name: query.search },
      }
    );
    return contacts.map((contact) => this.adapter.mapContact(contact));
  }

  public async createContact(
    contactInfo: CreateContactDto,
  ): Promise<CreateContactResponseDto> {
    const { primaryContact, secondaryContact, primaryAdvisor, primaryCSA } =
      contactInfo;
    const primaryContactPromises = [];
    const secondaryContactPromises = [];
    const newPrimaryContact = await this.createNewContact(primaryContact);

    if (!newPrimaryContact.id) {
      throw new HttpException(
        'Failed to create primary contact',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const newPrimaryContactId = newPrimaryContact.id.toString();
    const redtailInfo: CreateContactResponseDto = {
      primaryContactCrmId: newPrimaryContactId,
      secondaryContactCrmId: undefined,
    };
    primaryContact.crmClientId = newPrimaryContactId;

    if (secondaryContact !== undefined && secondaryContact) {
      const crmSecondaryContact = await this.createNewContact(secondaryContact);

      if (!crmSecondaryContact?.id) {
        throw new HttpException(
          'Failed to create secondary contact',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
      const newSecondaryContactId = crmSecondaryContact.id.toString();
      secondaryContact.crmClientId = newSecondaryContactId;
      redtailInfo.secondaryContactCrmId = newSecondaryContactId;

      await this.redtailFamilyService.upsertFamily(contactInfo);

      // Create accounts for secondary contact, in parallel
      secondaryContactPromises.push(
        ...(secondaryContact.accounts || []).map((account) =>
          this.createAccountForContact(
            account,
            secondaryContact,
            primaryAdvisor,
            primaryCSA,
          ),
        ),
      );
    }

    primaryContactPromises.push(
      ...(primaryContact.accounts || []).map((account) =>
        this.createAccountForContact(
          account,
          primaryContact,
          primaryAdvisor,
          primaryCSA,
        ),
      ),
    );

    const onbordTag = await this.redtailTagService.getTagByTagName(
      DefaultTagsEnum.OnBord,
    );

    if (!onbordTag?.id) {
      this.logger.warn(
        `[WARNING: createContact] Failed to find OnBord tag group`,
      );
    } else {
      primaryContactPromises.push(
        this.redtailTagService.tagContact(onbordTag.id, newPrimaryContactId),
      );
    }
    if (primaryAdvisor) {
      primaryContactPromises.push(
        this.tagAdvisor(
          primaryAdvisor.personalInfo.firstName,
          primaryAdvisor.personalInfo.lastName,
          primaryContact.crmClientId,
        ),
      );
    }

    await Promise.all([...primaryContactPromises, ...secondaryContactPromises]);
    return redtailInfo;
  }

  /**
   * Adds a tag to a contact with the name and description of the advisor.
   * @param advisorFirstName - The first name of the advisor.
   * @param advisorLastName - The last name of the advisor.
   * @param contactId - The ID of the contact to tag.
   * @returns A promise that resolves with the result of tagging the contact.
   */
  public async tagAdvisor(
    advisorFirstName: string,
    advisorLastName: string,
    contactId: string,
  ) {
    const tagName = `OnBord Advisor ${advisorFirstName} ${advisorLastName}`;
    const tagDescription = `OnBord tag group for Contacts with a Primary Advisor of ${advisorFirstName} ${advisorLastName}`;

    const advisorTag = await this.redtailTagService.findOrCreateTagGroup(
      tagName,
      tagDescription,
    );

    if (!advisorTag?.id) {
      this.logger.warn(
        `[WARNING: tagAdvisor] Failed to find or create tag group ${tagName}`,
      );
      return;
    }

    return this.redtailTagService.tagContact(advisorTag.id, contactId);
  }

  /**
   * Creates an account creation task in Redtail CRM.
   * @param account - The account to create the task for.
   * @param primaryContact - The primary contact associated with the account.
   * @param primaryAdvisor - The primary advisor associated with the account.
   * @param primaryCSA - The primary client service associate associated with the account (optional).
   * @returns A Promise that resolves when the task is created.
   */
  public async createAccountCreationTask(
    account: AccountDto,
    contact: CrmContactDto,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole = undefined,
  ) {
    const { subject, description } = buildAccountCreationTaskDetails(
      account,
      contact,
    );
    return this.createTask(
      primaryAdvisor,
      primaryCSA,
      contact,
      subject,
      description,
    );
  }

  /**
   * Creates a new task in Redtail CRM.
   * @param primaryAdvisor The primary advisor assigned to the task.
   * @param primaryCSA The primary client service associate assigned to the task.
   * @param primaryContact The primary contact associated with the task.
   * @param subject The subject of the task.
   * @param description The description of the task.
   * @returns A Promise that resolves to the created task.
   */
  private async createTask(
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
    contact: CrmContactDto,
    subject: string,
    description: string,
  ) {
    const attendees = Array.from(
      new Set([primaryAdvisor.crmId, primaryCSA.crmId]),
    ).map((userId) => {
      return {
        type: ActivityAttendeeTypeEnum.USER,
        userId,
      };
    });

    return this.redtailActivityService.createActivity({
      activityCodeId: 1,
      allDay: false,
      repeats: ActivityRepeatEnum.NEVER,
      subject,
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
      description,
      attendees: attendees.map(attendee => ({
        ...attendee,
        userId: Number(attendee.userId)
      })),
      linkedContacts: [
        {
          contactId: Number(contact.crmClientId),
        },
      ],
      importance: 1,
      percentdone: 0,
      completed: false,
    });
  }

  private async createAccountForContact(
    account: AccountDto,
    contact: CrmContactDto,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
  ) {
    const orgConfig = this.organisation?.configuration;

    if (!orgConfig) {
      this.logger.warn(`[WARNING: createAccountForContact] No organisation configuration found`);
    }

    const advisoryRateAccountUdfFieldId =
      orgConfig[RedtailAccountUdfNameEnum.ADVISORY_RATE];
    const accountFeaturesUdfId =
      orgConfig[RedtailAccountUdfNameEnum.ACCOUNT_FEATURES];
    const nameOnAccount = `${contact.firstName} ${contact.lastName}`;
    const accountType = RedtailDBAccountType[account.type];

    if (!account.type.toLowerCase().includes('offline')) {
      await this.redtailAccountsService.createAccount(
        contact.crmClientId,
        {
          accountLabel: account.label,
          accountType,
          advisoryRate: account.advisoryRate,
          ownership: account.ownership,
          nameOnAccount,
          features: account.features,
        },
        advisoryRateAccountUdfFieldId,
        accountFeaturesUdfId,
      );
    }

    await this.createAccountCreationTask(
      account,
      contact,
      primaryAdvisor,
      primaryCSA,
    );
  }

  public async updateContact(
    contactInfo: UpdateContactDto,
  ): Promise<UpdateContactResponseDto> {
    const promises = [];
    const { crmClientId } = contactInfo.primaryContact;
    const { primaryContact, secondaryContact, primaryAdvisor, primaryCSA } =
      contactInfo;

    if (!primaryContact) {
      throw new HttpException(
        'Primary contact is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updateResult: UpdateContactResponseDto = {
      primaryContactCrmId: crmClientId ? `${crmClientId}` : undefined,
      secondaryContactCrmId: secondaryContact?.crmClientId ? `${secondaryContact?.crmClientId}` : undefined,
    };

    await this.updateContactName(
      crmClientId,
      primaryContact.firstName,
      primaryContact.lastName,
    );

    const primaryContactAccounts = primaryContact.accounts || [];
    const primaryContactAccountsPromises = primaryContactAccounts.map(
      (account) =>
        this.createAccountForContact(
          account,
          primaryContact,
          primaryAdvisor,
          primaryCSA,
        ),
    );

    promises.push(
      this.emailUpsert(primaryContact),
      this.phoneUpsert(primaryContact),
      this.updateSecondaryContact(contactInfo, primaryAdvisor, primaryCSA),
      this.tagAdvisor(
        primaryAdvisor.personalInfo.firstName,
        primaryAdvisor.personalInfo.lastName,
        primaryContact.crmClientId,
      ),
      ...primaryContactAccountsPromises,
    );

    await Promise.all(promises);

    return updateResult;
  }

  private async emailUpsert(contact: CrmContactDto) {
    const emailAddress = await this.getMainEmailOfContact(contact.crmClientId);
    if (!emailAddress) {
      const { email } =
        await this.redtailEmailAddressService.createEmailAddress(
          contact.crmClientId,
          contact.email,
        );
      return email;
    } else if (emailAddress.address !== contact.email) {
      const { email } =
        await this.redtailEmailAddressService.updateEmailAddress(
          contact.crmClientId,
          emailAddress.id.toString(),
          contact.email,
        );
      return email;
    }
    return emailAddress;
  }

  private async phoneUpsert(contact: CrmContactDto) {
    const phoneNumbers = await this.getPhoneNumbersOfContact(
      contact.crmClientId,
    );

    if (isEmpty(phoneNumbers)) {
      const redtailPhone =
        await this.redtailPhoneNumberService.createPhoneNumber(
          contact.crmClientId,
          contact.mobile,
        );
      return {
        ext: redtailPhone.extension,
        number: redtailPhone.number,
        type: PhoneTypeEnum.MOBILE,
        isPrimary: redtailPhone.isPrimary,
      };
    }

    const existingNumber =
      phoneNumbers.find((phone) => phone.type === PhoneTypeEnum.MOBILE) ||
      phoneNumbers[0];

    if (existingNumber?.number !== contact.mobile) {
      const redtailPhone =
        await this.redtailPhoneNumberService.updatePhoneNumber(
          contact.crmClientId,
          existingNumber.id,
          contact.mobile,
        );
      return {
        ext: redtailPhone.extension,
        number: redtailPhone.number,
        type: PhoneTypeEnum.MOBILE,
        isPrimary: redtailPhone.isPrimary,
      };
    }
    return existingNumber;
  }

  private async updateSecondaryContact(
    contactInfo: UpdateContactDto,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
  ) {
    const { secondaryContact } = contactInfo;

    if (!secondaryContact) {
      return;
    }

    const promises = [];

    // If not existent in CRM, create new contact and tag it
    if (!secondaryContact.crmClientId) {
      const newSecondaryContact = await this.createNewContact(secondaryContact);
      const newSecondaryContactId = newSecondaryContact.id.toString();
      if (!newSecondaryContact.id) {
        throw new HttpException(
          'Failed to create secondary contact',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
      promises.push(
        this.tagAdvisor(
          primaryAdvisor.personalInfo.firstName,
          primaryAdvisor.personalInfo.lastName,
          newSecondaryContactId,
        ),
      );
      secondaryContact.crmClientId = newSecondaryContactId;
    } else {
      promises.push(
        this.updateContactName(
          secondaryContact.crmClientId,
          secondaryContact.firstName,
          secondaryContact.lastName,
        ),
      );
      promises.push(this.phoneUpsert(secondaryContact));
    }

    // TODO: Investigate how to perform the sync instead of creating new accounts
    for (const account of secondaryContact.accounts || []) {
      promises.push(
        this.createAccountForContact(
          account,
          secondaryContact,
          primaryAdvisor,
          primaryCSA,
        ),
      );
    }

    promises.push(this.redtailFamilyService.upsertFamily(contactInfo));

    await Promise.all(promises);

    return secondaryContact;
  }

  // #####################
  // ###    CONTACTS   ###
  // #####################
  // TODO: Move this to another service

  /**
   * Creates a new contact in Redtail CRM alongside email and phone number.
   * @param contactInfo - The contact information to be created.
   * @returns The created contact.
   */
  public async createNewContact(
    contactInfo: ContactDto,
  ): Promise<RedtailContact> {
    const contactRequestBody = {
      type: RedtailCrmContactTypeEnum.INDIVIDUAL,
      status: RedtailStatusEnum.ACTIVE_CLIENT,
      first_name: contactInfo.firstName,
      last_name: contactInfo.lastName,
    };

    const { contact } = await this.httpService.post<RedtailCreateContactResponseDto>(
      `${this.baseUrl}/contacts`,
      this.tenantId,
      contactRequestBody,
      { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.CREATE },
      { headers: this.getAuthHeader(this.apiKey) }
    );

    if (!contact?.id) {
      throw new HttpException(
        'Failed to create contact in Redtail CRM',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    if (contactInfo.email) {
      await this.redtailEmailAddressService.createEmailAddress(
        contact?.id?.toString(),
        contactInfo.email,
      );
    }

    if (contactInfo.mobile) {
      await this.redtailPhoneNumberService.createPhoneNumber(
        contact?.id?.toString(),
        contactInfo.mobile,
      );
    }

    return contact;
  }

  /**
   * Updates the name of a contact in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param firstName The updated first name of the contact.
   * @param lastName The updated last name of the contact.
   * @param middleName The updated middle name of the contact (optional).
   * @param suffix The updated suffix of the contact (optional).
   * @returns A Promise that resolves with the updated Contact object.
   */
  public async updateContactName(
    crmClientId: string,
    firstName: string,
    lastName: string,
    middleName?: string,
    suffix?: SuffixEnum,
  ): Promise<RedtailContact> {
    const contactRequestBody = {
      first_name: firstName,
      last_name: lastName,
      middle_name: middleName,
      suffix_id: mapToSuffixId(suffix),
    };

    return this.patchContact(crmClientId, contactRequestBody);
  }

  /**
   * Updates the SSN of a contact in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param ssn The new SSN to update for the contact.
   * @returns A Promise that resolves with the updated contact object.
   */
  public async updateContactSSN(crmClientId: string, ssn: string) {
    const contactRequestBody = {
      tax_id: ssn,
    };

    return this.patchContact(crmClientId, contactRequestBody);
  }

  /**
   * Updates a contact in Redtail CRM.
   * @param crmClientId The ID of the contact to update.
   * @param updateContact The updated contact information.
   * @returns The updated contact.
   */
  public async patchContact(
    crmClientId: string,
    updateContact: UpdateContactRequestDto,
  ) {
    const { contact: updatedContact } = await this.httpService.put<RedtailUpdateContactResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}`,
      this.tenantId,
      updateContact,
      { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.PATCH },
      { headers: this.getAuthHeader(this.apiKey) }
    );
    return updatedContact;
  }

  public async getContactPersonalProfile(
    crmClientId: string,
  ): Promise<GetPersonalProfileResponse> {
    const response = await this.httpService.get<GetPersonalProfileResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/personal_profile`,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.GET },
      { headers: this.getAuthHeader(this.apiKey) }
    );
    return camelize(response);
  }

  public async updateContactPersonalProfile(
    crmClientId: string,
    personalInfo: UpdatePersonalProfileRequestDto,
  ) {
    const { contact: updatedContact } = await this.httpService.put<RedtailUpdateContactResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/personal_profile`,
      this.tenantId,
      personalInfo,
      { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'contact', operation: OperationType.PATCH, forceFresh: true },
      { headers: this.getAuthHeader(this.apiKey) }
    );
    return updatedContact;
  }

  // public async getAllUsers(query: GetCrmUsersQueryDto) {
  //   const { contact: updatedContact } =
  //     await makeRequest<RedtailUpdateContactResponseDto>(
  //       this.httpService,
  //       HttpMethodsEnum.PUT,
  //       `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}`,
  //       updateContact,
  //       { headers: this.getAuthHeader(this.apiKey) }
  //     );
  //   return updatedContact;
  // }

  public async getAllUsers(query: GetCrmUsersQueryDto) {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Users}`;
    const params = query ? {
      page: query.page,
      pagesize: query.limit,
    } : undefined;

    try {
      const response = await this.httpService.get<{ database_users?: RedtailUser[] }>(
        url,
        this.tenantId,
        { rateLimiter: 'redtail', integrationName: 'redtail', entityType: 'user', operation: OperationType.GET, forceFresh: true },
        {
          headers: this.getAuthHeader(this.apiKey),
          params
        }
      );

      return response.database_users?.map((user: RedtailUser) => {
        const {
          id: crmId,
          first_name: firstName,
          last_name: lastName,
        } = user;

        return {
          crmId,
          firstName,
          lastName,
        };
      });
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  // #####################
  // ###    ACCOUNTS   ###
  // #####################

  public async getAccounts(crmClientId: string): Promise<GenericCrmAccount[]> {
    const accounts = await this.redtailAccountsService.getAccounts(
      crmClientId,
      RedtailAccountTypeEnum.BROKERAGE_ACCOUNT,
    );
    const accountsInfo: GenericCrmAccount[] = [];

    for (const account of accounts) {
      const { accountBeneficiaries } =
        await this.redtailAccountsService.getAccountBeneficiaries(account.id);

      accountsInfo.push({
        beneficiaries: accountBeneficiaries.map((beneficiary) => {
          return {
            name: getBeneficaryFullName(beneficiary.name),
            fullName: getBeneficaryFullName(beneficiary.name),
            firstName: getBeneficaryFirstName(beneficiary.name),
            lastName: getBeneficaryLastName(beneficiary.name),
            dob: getDobFromBeneficiaryName(beneficiary.name),
            percentage: beneficiary.percentage,
            type:
              beneficiary.beneficiaryTypeDescription === 'Primary'
                ? GenericCrmBeneficiaryEnum.Primary
                : GenericCrmBeneficiaryEnum.Contingent,
          };
        }),
        name: account.number,
        type: account.accountType,
      });
    }

    return accountsInfo;
  }

  /**
   * Logs a communication for a CRM client by inserting a note.
   *
   * @param crmClientId - The ID of the CRM client.
   * @param logCommunicationDto - The data for the communication log.
   * @throws HttpException if failed to log communication.
   */
  public async logCommunication(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
  ): Promise<void> {
    const { categories } = await this.redtailNotesService.getCategories();
    const categoryCustomService = categories.find(
      (cat) => cat.name.toLowerCase() === 'customer service',
    );

    const note = await this.redtailNotesService.createNote(crmClientId, {
      body: `Client contacted via ${logCommunicationDto.communicationType}: ${logCommunicationDto.communicationDetails || ''
        }`,
      category_id: categoryCustomService.id,
      draft: false,
      note_type: RedtailNoteTypeEnum.Note,
      pinned: false,
    });

    if (isEmpty(note)) {
      throw new HttpException(
        'Failed to log communication',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
