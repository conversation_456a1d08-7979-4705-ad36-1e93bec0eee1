import { InterviewPageDefV2 } from 'src/interview-templates/schemas/v2/interview-page-def.schema';
import { Client } from 'src/clients/schemas/clients.schema';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { SubmitPageV2Dto } from 'src/interviews/dto/v2/submit-page-v2.dto';



import { PagesEnum } from 'src/shared/types/pages/pages.enum';

export interface V2MapperContext {
  pageDefinition: InterviewPageDefV2;
  answers: Record<string, any>;
  contactType: 'primary' | 'secondary';
  client: Client;
  organisation: Organisation;
  interviewId: string;
  metadata?: {
    timeSpent?: number;
    deviceType?: string;
    timestamp?: Date;
    accountId?: string; // Account ID for account-specific pages
  };
}

export interface V2SyncPageJobData {
  interviewId: string;
  pageName: string; // Unique identifier like "primary_address", "employment_status"
  pageType: string; // General type like "address", "employment" 
  answers: Record<string, any>;
  contactType: 'primary' | 'secondary';
  clientId: string;
  organisationId: string;
  advisorId: string;
  accountId?: string; // Account ID for account-specific pages
  metadata?: {
    timeSpent?: number;
    deviceType?: string;
    timestamp?: Date;
  };
}

export interface FormUpdateMapperV2 {
  mapPageData(context: V2MapperContext): Promise<void>;
}

export type PageUpdateMapperV2 = { [key in PagesEnum]: FormUpdateMapperV2 };