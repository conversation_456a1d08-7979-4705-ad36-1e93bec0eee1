// Import types from separate file
import {
  NameUpdateParams,
  EmploymentUpdateParams,
  DobUpdateParams,
  SsnUpdateParams,
  PhoneUpdateParams,
  AddressUpdateParams,
  JobUpdateParams,
  CompanyUpdateParams,
  VipUpdateParams,
  UsCitizenUpdateParams,
  ConflictsUpdateParams,
  BeneficiariesUpdateParams,
  CustomQuestionsUpdateParams,
} from './wealthbox-mapper-core.types';

// Shared page DTOs
import { EmploymentStatusEnum } from 'src/shared/types/pages/dto';

// Wealthbox utilities
import { 
  insertIntoUserContentField, 
  removeFromUserContentField 
} from '../utils/data-parsing/general-text-field';
import { computeCompanyAssociationValue } from 'src/integrations/crm/redtail/utils/company-association-parser';
import {
  WealthboxStoredClientInfo,
  parseDataNoteYAML,
  updateDataNoteYAML,
} from '../utils/data-parsing';
import { objectToYAML } from 'src/utils/yaml-parser';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import {
  GenericCrmBeneficiaries,
  GenericCrmBeneficiaryEnum,
} from 'src/integrations/crm/types/accounts/crm-account.type';
import { getLabelFromAccountNumber } from 'src/integrations/crm/utils/account.utils';
import { HttpException } from '@nestjs/common';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { AccountClientDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { phoneTypeToPhoneNumberKind } from 'src/integrations/crm/wealthbox/utils/phone-kind-mapper';
import { WealthboxPhoneNumber, WealthboxMailingAddress, WealthboxAddressKind } from 'src/integrations/crm/wealthbox/types';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { Country } from 'src/shared/types/general/country.enum';
import { isEmpty } from 'lodash';

export class WealthboxMapperCore {
  /**
   * Core name update logic - shared between v1 and v2
   */
  static async updateName(params: NameUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      first_name: pageData.firstName,
      last_name: pageData.lastName,
      suffix: !isEmpty(pageData.suffix) ? pageData.suffix : undefined,
      middle_name: !isEmpty(pageData.middleName) ? pageData.middleName : undefined,
    });
  }

  /**
   * Core employment update logic - shared between v1 and v2
   */
  static async updateEmployment(params: EmploymentUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, organisation, pageData, allowPageModification, interviewId } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    // V1 vs V2 difference: Page modification
    if (allowPageModification && interviewId) {
      // V1: Remove/add pages based on employment status
      if ([EmploymentStatusEnum.RETIRED, EmploymentStatusEnum.STUDENT, EmploymentStatusEnum.UNEMPLOYED].includes(pageData.status)) {
        await wealthboxCrm.interviewsService.removePageFromInterview(interviewId, PagesEnum.COMPANY);
        await wealthboxCrm.interviewsService.removePageFromInterview(interviewId, PagesEnum.JOB);
      } else {
        await wealthboxCrm.interviewsService.addPageToInterview(interviewId, PagesEnum.COMPANY);
        await wealthboxCrm.interviewsService.addPageToInterview(interviewId, PagesEnum.JOB);
      }
    }
    // V2: Skip page modification - handled by navigation rules

    // Shared CRM update logic
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.EMPLOYMENT_STATUS]: pageData.status,
        },
      ),
    });
  }

  /**
   * Core DOB update logic - shared between v1 and v2
   */
  static async updateDob(params: DobUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    const dobValue = pageData.dateOfBirth || pageData.birthDate || (pageData as any).dob;
    if (!dobValue) {
      return;
    }

    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      birth_date: dobValue,
    });
  }

  /**
   * Core SSN update logic - shared between v1 and v2
   */
  static async updateSsn(params: SsnUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.SSN]: pageData.ssn,
        },
      ),
    });
  }

  /**
   * Core phone update logic - shared between v1 and v2
   */
  static async updatePhone(params: PhoneUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    // Only handle alternate phones if they exist (matching V1 behavior)
    if (isEmpty(pageData?.alternatePhones)) return;

    const phoneNumbers: WealthboxPhoneNumber[] = pageData.alternatePhones.map(
      (phone) => ({
        principal: false,
        address: phone.number,
        kind: phoneTypeToPhoneNumberKind(
          PhoneTypeEnum[phone.type.toUpperCase()],
        ),
      }),
    );

    await wealthboxCrm.contactService.updateContact(
      contact.crmClientId,
      {
        phone_numbers: phoneNumbers,
      },
    );
  }

  /**
   * Core address update logic - shared between v1 and v2
   */
  static async updateAddress(params: AddressUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    const addressUpdates: WealthboxMailingAddress[] = [];

    // Get current contact to check existing addresses
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    // Legal address (Home address)
    if (pageData.legalAddress) {
      const existingHomeAddress = crmContact.street_addresses.find(
        (x) => x.kind === WealthboxAddressKind.Home,
      );
      
      const legalAddress: WealthboxMailingAddress = {
        id: existingHomeAddress?.id,
        street_line_1: pageData.legalAddress.line1,
        street_line_2: pageData.legalAddress.line2,
        city: pageData.legalAddress.city,
        state: pageData.legalAddress.state,
        zip_code: pageData.legalAddress.zip,
        address: pageData.legalAddress.line1,
        country: Country.US,
        kind: WealthboxAddressKind.Home,
        principal: false,
      };
      addressUpdates.push(legalAddress);
    }

    // Mailing address (Other address)
    if (pageData.mailingAddress) {
      const existingMailingAddress = crmContact.street_addresses.find(
        (x) => x.kind === WealthboxAddressKind.Other,
      );
      
      const mailingAddress: WealthboxMailingAddress = {
        id: existingMailingAddress?.id,
        street_line_1: pageData.mailingAddress.line1,
        street_line_2: pageData.mailingAddress.line2,
        city: pageData.mailingAddress.city,
        state: pageData.mailingAddress.state,
        zip_code: pageData.mailingAddress.zip,
        address: pageData.mailingAddress.line1,
        country: Country.US,
        kind: WealthboxAddressKind.Other,
        principal: false,
      };
      addressUpdates.push(mailingAddress);
    }

    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      street_addresses: [...addressUpdates],
    });
  }

  /**
   * Core job description update logic - shared between v1 and v2
   */
  static async updateJob(params: JobUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, organisation, pageData } = params;

    // Use the occupation field as per V1 implementation
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      occupation: {
        name: `${pageData.description}: ${pageData.type}`,
        start_date: '2000-01-01',
      },
    });
  }

  /**
   * Core company update logic - shared between v1 and v2
   */
  static async updateCompany(params: CompanyUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    // Create work address using street_addresses
    const existingWorkAddress = crmContact.street_addresses.find(
      (x) => x.kind === WealthboxAddressKind.Work,
    );

    const workAddress: WealthboxMailingAddress = {
      id: existingWorkAddress?.id,
      street_line_1: pageData.address?.line1 || '',
      street_line_2: pageData.address?.line2 || '',
      city: pageData.address?.city || '',
      state: pageData.address?.state || '',
      zip_code: pageData.address?.zip || '',
      address: pageData.address?.line1 || '',
      country: Country.US,
      kind: WealthboxAddressKind.Work,
      principal: false,
    };

    // Update work address
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      street_addresses: [workAddress],
    });

    // Store company name in background info
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.COMPANY]: pageData.name,
        },
      ),
    });
  }

  /**
   * Core VIP update logic - shared between v1 and v2
   */
  static async updateVip(params: VipUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, organisation, pageData } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    if (!pageData.companyName) {
      // Remove the company association field if no company name
      const newBackgroundInfo = removeFromUserContentField(
        crmContact.background_info,
        GenericUserDefinedFieldsEnum.COMPANY_ASSOCIATION,
      );

      if (newBackgroundInfo) {
        await wealthboxCrm.contactService.updateContact(
          contact.crmClientId,
          {
            background_information: newBackgroundInfo,
          },
        );
      }
      return;
    }

    // Use the same utility function as V1
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.COMPANY_ASSOCIATION]:
            computeCompanyAssociationValue(pageData),
        },
      ),
    });
  }

  /**
   * Core US Citizen update logic - shared between v1 and v2
   */
  static async updateUsCitizen(params: UsCitizenUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, client, organisation, pageData } = params;

    if (pageData.citizen && pageData.resident) {
      // Handle US citizen case using data notes
      const dataNote = await wealthboxCrm.notesService.getDataNoteContent(
        contact.crmClientId,
      );
      
      if (!dataNote) {
        await wealthboxCrm.notesService.upsertDataNote(
          contact.crmClientId,
          {
            linked_to: [
              {
                id: Number(contact.crmClientId),
                type: 'Contact',
              },
            ],
            content: objectToYAML({
              additionalInfo: {
                isUSCitizen: pageData.citizen,
                isUSResident: pageData.resident,
              },
              accounts: [],
            }),
          },
        );
        return;
      }

      const contactData: WealthboxStoredClientInfo = parseDataNoteYAML(
        dataNote?.content,
      );

      contactData.additionalInfo = {
        ...contactData.additionalInfo,
        isUSCitizen: pageData.citizen,
        isUSResident: pageData.resident,
      };

      await wealthboxCrm.notesService.upsertDataNote(contact.crmClientId, {
        linked_to: dataNote.linked_to,
        content: updateDataNoteYAML(contactData),
      });
      return;
    }

    // Handle non-citizen case by sending email
    const context: NonCitizenEmailContext = {
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
      subject: `Non-Citizen Status`,
    };

    return wealthboxCrm.sendEmail({
      to: client.primaryAdvisor.email,
      templateName: EmailTemplateNameEnum.NonCitizen,
      subject: `Non-Citizen Status`,
      context,
      organisation,
    });
  }

  /**
   * Core conflicts update logic - shared between v1 and v2
   */
  static async updateConflicts(params: ConflictsUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, client, organisation, pageData, allowPageModification, interviewId, interview } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    // Remove FINRA Document from required documents in case the client changes their answer
    if (!pageData.companyName) {
      if (allowPageModification && interviewId) {
        await wealthboxCrm.interviewsService.removeRequiredDocument(interviewId, {
          document: AccountClientDocumentsEnum.FinraDocument,
        });
      }

      // Remove the industry affiliation field
      const newBackgroundInfo = removeFromUserContentField(
        crmContact.background_info,
        GenericUserDefinedFieldsEnum.INDUSTRY_AFFILIATION,
      );

      if (newBackgroundInfo) {
        await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
          background_information: newBackgroundInfo,
        });
      }
      return;
    }

    // Add FINRA Document to required documents in the interview (only if docusign is selected)
    if (allowPageModification && interviewId && interview?.docusignSelected) {
      await wealthboxCrm.interviewsService.addRequiredDocument(interviewId, {
        document: AccountClientDocumentsEnum.FinraDocument,
      });
    }

    // Update contact background information with the company name
    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.INDUSTRY_AFFILIATION]: pageData.companyName,
        },
      ),
    });

    // Create a task in Wealthbox
    const subject = 'Obtain BD/FINRA approval letter for account opening';
    const description = 'Client employed by or associated with B/D, FINRA or Stock Exchange. Email for approval letter and submit to Schwab.';
    
    await wealthboxCrm.taskService.createTask({
      name: subject,
      description,
      assigned_to: Number(client.primaryAdvisor.crmId),
      due_date: new Date().toISOString(),
      linked_to: [
        {
          id: Number(contact.crmClientId),
          type: 'Contact',
        },
      ],
    });
  }

  /**
   * Core beneficiaries update logic - shared between v1 and v2
   */
  static async updateBeneficiaries(params: BeneficiariesUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData, beneficiaryType } = params;

    const { beneficiaries } = pageData;

    if (isEmpty(beneficiaries)) return;

    // Get data note for complex account-specific beneficiary management
    const dataNote = await wealthboxCrm.notesService.getDataNoteContent(
      contact.crmClientId,
    );

    if (!dataNote) {
      throw new HttpException(
        `Data note not found for client ${contact.crmClientId}`,
        404,
      );
    }

    const contactData: WealthboxStoredClientInfo = parseDataNoteYAML(
      dataNote?.content,
    );
    const contactAccounts = contactData.accounts;
    const accountToUpdate = contactAccounts?.find((x) => {
      const accountLabel = getLabelFromAccountNumber(x?.name).toLowerCase();
      return pageData.instance.label.toLowerCase().includes(accountLabel);
    });

    if (!accountToUpdate) {
      throw new HttpException(
        `Account ${pageData.instance.label} not found`,
        404,
      );
    }

    // Insert the new beneficiaries and update the existing ones if there are
    const newBeneficiaries = [];
    beneficiaries.forEach((beneficiary) => {
      const genericBenef: GenericCrmBeneficiaries = {
        firstName: beneficiary.firstName,
        lastName: beneficiary.lastName,
        fullName: `${beneficiary.firstName} ${beneficiary.lastName}`,
        percentage: beneficiary.allocationAmount,
        type: beneficiaryType === 'primary' ? GenericCrmBeneficiaryEnum.Primary : GenericCrmBeneficiaryEnum.Contingent,
        dob: beneficiary.dob,
      };

      const existingBeneficiary = accountToUpdate.beneficiaries.find(
        (benef) =>
          benef.fullName.toLowerCase() === genericBenef.fullName.toLowerCase(),
      );

      if (existingBeneficiary) {
        existingBeneficiary.percentage = genericBenef.percentage;
      } else {
        newBeneficiaries.push(genericBenef);
      }
    });

    accountToUpdate.beneficiaries.push(...newBeneficiaries);

    await wealthboxCrm.notesService.upsertDataNote(contact.crmClientId, {
      linked_to: [
        {
          id: Number(contact.crmClientId),
          type: 'Contact',
        },
      ],
      content: objectToYAML({
        ...contactData,
        accounts: contactAccounts,
      }),
    });
  }

  /**
   * Core custom questions update logic - shared between v1 and v2
   */
  static async updateCustomQuestions(params: CustomQuestionsUpdateParams): Promise<void> {
    const { wealthboxCrm, contact, pageData } = params;

    // Get current contact to access background info
    const crmContact = await wealthboxCrm.contactService.getContact(contact.crmClientId);

    if (!pageData.answer) {
      return;
    }

    await wealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [pageData.question.question]: pageData.answer,
        },
      ),
    });
  }

  /**
   * Helper: Format date for Wealthbox (MM/DD/YYYY)
   */
  private static formatDateForWealthbox(dateInput: string | Date): string {
    let date: Date;

    if (typeof dateInput === 'string') {
      date = new Date(dateInput);
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      throw new Error('Invalid date input');
    }

    if (isNaN(date.getTime())) {
      throw new Error('Invalid date value');
    }

    // Format as MM/DD/YYYY
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const year = date.getFullYear();

    return `${month}/${day}/${year}`;
  }
}