import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { FormUpdateMapperV2, V2MapperContext } from 'src/integrations/crm/types/form-update-mapper-v2';
import { WealthboxMapperCore } from '../../shared/wealthbox-mapper-core';
import { BeneficiariesPageDataDto } from 'src/integrations/crm/redtail/form-mappers/dto/add-beneficiary-request.dto';
import { BeneficiariesPageUpdateDto } from 'src/shared/types/pages/dto';

export class WealthboxContingentBeneficiariesMapperV2 implements FormUpdateMapperV2 {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps the V2 contingent beneficiaries page data to the corresponding fields in Wealthbox CRM for a given client.
   */
  async mapPageData(context: V2MapperContext): Promise<void> {
    const contact = context.contactType === 'primary'
      ? context.client.primaryContact
      : context.client.secondaryContact;

    // Extract V2 beneficiaries data
    const v2Data = context.answers as BeneficiariesPageUpdateDto;
    
    // Find the account this beneficiary page relates to using metadata
    const accountId = context.metadata?.accountId;
    let accountLabel = 'Unknown Account';
    
    if (accountId && contact.accounts) {
      const account = contact.accounts.find(acc => acc._id?.toString() === accountId);
      if (account) {
        accountLabel = account.label || account.masterAccountNumber || 'Unknown Account';
      }
    }

    // Transform V2 data structure to V1 format that the core mapper expects
    const v1FormattedData: BeneficiariesPageDataDto = {
      instance: {
        name: accountLabel,
        label: accountLabel
      },
      beneficiaries: v2Data.beneficiaries.map(ben => ({
        firstName: ben.firstName,
        lastName: ben.lastName,
        dob: ben.dateOfBirth || '',
        allocationAmount: ben.percentage?.toString() || '0'
      }))
    };

    return WealthboxMapperCore.updateBeneficiaries({
      wealthboxCrm: this.WealthboxCrm,
      contact,
      pageData: v1FormattedData,
      beneficiaryType: 'contingent',
    });
  }
}