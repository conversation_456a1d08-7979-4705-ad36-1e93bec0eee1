import { AccountTypeEnum } from "src/shared/types/accounts/account-type.enum";
import { AccountFeaturesEnum } from "src/shared/types/accounts/account-features.enum";

export type commonConfigs = {
  headers: {
    Authorization: string;
    'Content-Type': string;
  };
  timeout: number;
}

export type AccountDtoWithCRMClientId = {
  type: AccountTypeEnum;
  label: string;
  ownership: string;  
  masterAccountNumber?: string;
  features?: AccountFeaturesEnum[];
  advisoryRate?: number;
  crmClientId: string;
}