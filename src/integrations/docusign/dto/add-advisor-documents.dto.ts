import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { PdfCompatibleInterviewData } from 'src/interviews/services/pdf-data/pdf-data-provider.interface';

export type Applicant = {
  name: string;
  email: string;
  phone: string;
};

export class AddAdvisorDocumentsDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  advisorId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  organisationId?: string;

  @ApiProperty()
  @IsArray()
  files: Express.Multer.File[];

  @ApiProperty()
  @IsString()
  envelopeId: string | undefined;

  @ApiProperty()
  interviewData: PdfCompatibleInterviewData;

  @ApiProperty()
  @IsNumber()
  previousDocumentUploadNo: number;
}
