# V2 Simple Document Management - Revised Implementation Plan

## 📋 **Overview**

This document outlines a **context-based document management system** for Interview V2 that works with V2's stateless answer architecture. The system uses available context data (client properties, account types, organization features) rather than stored answers for conditional logic.

## 🎯 **Goals**

### **Primary Objectives**
- Add document upload pages based on client/account context (not answers)
- Support conditional document requirements using available context data
- Simple upload workflow with generic processing (DocuSign/email)
- Leverage existing V2 infrastructure (template composition, navigation)
- Maintain V2's stateless answer philosophy

### **Success Criteria**
- ✅ Account type triggers appropriate document pages
- ✅ Client context determines document requirements
- ✅ Documents processed via DocuSign or email
- ✅ Integration with existing interview completion validation
- ✅ Simple implementation using existing patterns

## 🏗️ **Context-Based Architecture**

### **Core Concept**
```typescript
// Instead of answer-based conditions (not available in V2):
// "If VIP answer is true → show VIP documents"

// Use context-based conditions (available during composition):
// "If client has retirement accounts → include retirement document pages"
// "If organization has DocuSign → process via DocuSign, else email"
```

## 📄 **Document Page Templates**

### **Context-Based Document Pages**
```typescript
// Document pages added during template composition based on context
interface DocumentPageTemplate {
  pageId: string;
  pageType: 'document_upload';
  pageName: string;
  pageTitle: string;
  
  // Context-based inclusion conditions
  inclusionConditions: ContextCondition[];
  
  // Upload configuration
  uploadConfig: {
    documentId: string;        // "retirement-forms", "joint-account-docs"
    documentName: string;      // "Retirement Account Forms"
    description?: string;      // Help text for user
    acceptedFormats: string[]; // ["pdf", "jpg", "png"]
    maxSizeBytes: number;      // File size limit
    isRequired: boolean;       // Required vs optional
    
    // Generic processor configuration
    processor: {
      type: 'docusign' | 'email' | 'custom';
      config?: any;
    };
  };
  
  // Account context (if account-specific)
  accountContext?: {
    accountType: string;
    features: string[];
  };
}

// Context conditions using available data
interface ContextCondition {
  field: string;     // "accounts.hasRetirement", "client.tier", "organization.features"
  operator: string;  // "equals", "contains", "exists"
  value: any;        // Expected value
}
```

### **Example Document Page Templates**
```typescript
// Retirement account documents
{
  pageId: 'retirement-documents-upload',
  pageType: 'document_upload',
  pageName: 'retirement_documents',
  pageTitle: 'Retirement Account Documents',
  
  // Include if client has retirement accounts
  inclusionConditions: [
    { field: 'accounts.hasRetirement', operator: 'equals', value: true }
  ],
  
  uploadConfig: {
    documentId: 'retirement-forms',
    documentName: 'Retirement Account Forms',
    description: 'Please upload IRA or 401k transfer forms',
    acceptedFormats: ['pdf'],
    maxSizeBytes: ********,
    isRequired: true,
    
    processor: {
      type: 'docusign' // Use DocuSign if available
    }
  }
}

// Joint account ownership documents
{
  pageId: 'joint-ownership-upload',
  pageType: 'document_upload', 
  pageName: 'joint_ownership_docs',
  pageTitle: 'Joint Account Documentation',
  
  // Include if any account has joint ownership
  inclusionConditions: [
    { field: 'accounts.hasJoint', operator: 'equals', value: true }
  ],
  
  uploadConfig: {
    documentId: 'joint-ownership',
    documentName: 'Joint Ownership Documents',
    description: 'Marriage certificate or joint ownership proof',
    acceptedFormats: ['pdf', 'jpg', 'png'],
    maxSizeBytes: 5242880,
    isRequired: true,
    
    processor: {
      type: 'docusign'
    }
  }
}

// Organization-specific compliance documents
{
  pageId: 'compliance-docs-upload',
  pageType: 'document_upload',
  pageName: 'compliance_documents', 
  pageTitle: 'Compliance Documentation',
  
  // Include if organization requires compliance docs
  inclusionConditions: [
    { field: 'organization.features.requiresComplianceDocs', operator: 'equals', value: true }
  ],
  
  uploadConfig: {
    documentId: 'compliance-forms',
    documentName: 'Compliance Forms',
    description: 'Organization-required compliance documentation',
    acceptedFormats: ['pdf'],
    maxSizeBytes: ********,
    isRequired: false, // Optional
    
    processor: {
      type: 'email', // Send to compliance team
      config: { 
        emailType: 'compliance_document',
        recipients: ['<EMAIL>']
      }
    }
  }
}
```

## 🔧 **Template Composition Integration**

### **Extend Interview Composer Service**
```typescript
// Add to InterviewComposerService
export class InterviewComposerService {
  
  async composeInterview(dto: ComposeInterviewDto): Promise<InterviewTemplateV2> {
    // ... existing composition logic
    
    // Add document pages based on context
    const documentPages = await this.addContextBasedDocumentPages(
      baseTemplate,
      accountTemplates,
      context
    );
    
    // Merge document pages into composed template
    const composedTemplate = {
      ...baseTemplate,
      pages: [
        ...baseTemplate.pages,
        ...documentPages
      ]
    };
    
    return composedTemplate;
  }
  
  private async addContextBasedDocumentPages(
    baseTemplate: InterviewTemplateV2,
    accountTemplates: InterviewTemplateV2[],
    context: InterviewContext
  ): Promise<DocumentPageTemplate[]> {
    const documentPages: DocumentPageTemplate[] = [];
    const availableDocumentTemplates = this.getDocumentPageTemplates();
    
    for (const docTemplate of availableDocumentTemplates) {
      // Evaluate context conditions
      const shouldInclude = this.evaluateContextConditions(
        docTemplate.inclusionConditions,
        context
      );
      
      if (shouldInclude) {
        documentPages.push({
          ...docTemplate,
          // Set account context if needed
          accountContext: this.getRelevantAccountContext(docTemplate, context)
        });
      }
    }
    
    return documentPages;
  }
  
  private evaluateContextConditions(
    conditions: ContextCondition[],
    context: InterviewContext
  ): boolean {
    return conditions.every(condition => {
      const value = this.getContextValue(condition.field, context);
      return this.evaluateCondition(condition.operator, value, condition.value);
    });
  }
  
  private getContextValue(field: string, context: InterviewContext): any {
    // Extract values from context
    switch (field) {
      case 'accounts.hasRetirement':
        return context.accounts.some(acc => 
          ['ira', 'roth_ira', '401k'].includes(acc.accountType)
        );
      case 'accounts.hasJoint':
        return context.accounts.some(acc => 
          acc.ownership === 'joint'
        );
      case 'client.tier':
        return context.client.advisor?.tier;
      case 'organization.features.requiresComplianceDocs':
        return context.organization.features?.includes('compliance_docs');
      default:
        return this.getNestedValue(context, field);
    }
  }
}
```

## 📤 **Generic Upload Processing** 

### **Document Upload Service**
```typescript
@Injectable()
export class DocumentUploadService {
  constructor(
    private docusignService: DocusignService,
    private interviewEnvelopeService: InterviewEnvelopeService,
    private notificationService: NotificationService,
  ) {}

  async processDocumentUpload(
    interviewId: string,
    pageDefinition: DocumentPageTemplate,
    file: Express.Multer.File
  ): Promise<void> {
    
    // Route to appropriate processor
    switch (pageDefinition.uploadConfig.processor.type) {
      case 'docusign':
        await this.processDocuSignUpload(interviewId, pageDefinition, file);
        break;
        
      case 'email':
        await this.processEmailUpload(interviewId, pageDefinition, file);
        break;
        
      case 'custom':
        await this.processCustomUpload(interviewId, pageDefinition, file);
        break;
        
      default:
        throw new Error(`Unknown processor: ${pageDefinition.uploadConfig.processor.type}`);
    }
  }

  // DocuSign processor (synchronous like V1)
  private async processDocuSignUpload(
    interviewId: string,
    pageDefinition: DocumentPageTemplate,
    file: Express.Multer.File
  ): Promise<void> {
    const interview = await this.getInterview(interviewId);
    
    // Create envelope if doesn't exist (existing V1 pattern)
    if (!interview.envelopeId) {
      const envelopeId = await this.interviewEnvelopeService.createEnvelope(interview);
      await this.updateInterviewEnvelope(interviewId, envelopeId);
      interview.envelopeId = envelopeId;
    }
    
    // Add document to envelope (existing pattern)
    const { client } = interview;
    await this.docusignService.addClientDocuments({
      organisationId: client.organisationId.toString(),
      advisorId: client.primaryAdvisor.id.toString(),
      envelopeId: interview.envelopeId,
      files: [file]
    });
    
    // Record upload
    await this.recordDocumentUpload(
      interviewId, 
      pageDefinition.uploadConfig.documentId,
      'docusign'
    );
  }

  // Email processor
  private async processEmailUpload(
    interviewId: string,
    pageDefinition: DocumentPageTemplate,
    file: Express.Multer.File
  ): Promise<void> {
    const interview = await this.getInterview(interviewId);
    const config = pageDefinition.uploadConfig.processor.config;
    
    // Send notification email (no attachment - just notification)
    await this.notificationService.sendDocumentUploadEmail({
      recipients: config?.recipients || [interview.client.primaryAdvisor.email],
      clientName: `${interview.client.firstName} ${interview.client.lastName}`,
      documentName: pageDefinition.uploadConfig.documentName,
      interviewId,
      uploadedAt: new Date()
    });
    
    // Record upload
    await this.recordDocumentUpload(
      interviewId,
      pageDefinition.uploadConfig.documentId,
      'email'
    );
  }

  // Simple upload tracking
  private async recordDocumentUpload(
    interviewId: string, 
    documentId: string,
    processor: string
  ): Promise<void> {
    // Add to interview document uploads array (like V1)
    await this.interviewModel.updateOne(
      { _id: interviewId },
      { 
        $push: { 
          documentUploads: {
            documentId,
            processor,
            uploadedAt: new Date()
          }
        }
      }
    );
  }
}
```

## 🛤️ **Navigation Flow**

### **Document Page Navigation**
```typescript
// Document pages flow naturally in the navigation sequence
// No special handling needed - they're just regular pages with upload functionality

export class InterviewV2NavigationService {
  
  async submitPageAndNavigate(
    interviewId: string,
    dto: SubmitPageV2Dto
  ): Promise<PageNavigationResultV2Dto> {
    
    const pageDefinition = await this.getPageDefinition(interviewId, dto.pageId);
    
    // Handle document upload pages
    if (pageDefinition.pageType === 'document_upload') {
      // Process uploaded file
      if (dto.uploadedFile) {
        await this.documentUploadService.processDocumentUpload(
          interviewId,
          pageDefinition as DocumentPageTemplate,
          dto.uploadedFile
        );
      } else if (pageDefinition.uploadConfig.isRequired) {
        throw new BadRequestException('Document upload is required for this page');
      }
    }
    
    // Continue with normal navigation flow
    const nextPageId = await this.determineNextPage(interviewId, dto.pageId);
    
    return {
      currentPageCompleted: true,
      nextPageId,
      navigationState: await this.getCurrentNavigationState(interviewId)
    };
  }
}
```

## ✅ **Validation Integration**

### **Document Upload Validation**
```typescript
// Extend InterviewCompletionValidationService
export class InterviewCompletionValidationService {
  
  async validateDocumentUploads(interviewId: string): Promise<ValidationError[]> {
    const interview = await this.getInterview(interviewId);
    const template = await this.getInterviewTemplate(interview.templateId);
    const documentPages = template.pages.filter(p => p.pageType === 'document_upload');
    const errors: ValidationError[] = [];
    
    for (const documentPage of documentPages) {
      const docPage = documentPage as DocumentPageTemplate;
      
      // Skip if not required
      if (!docPage.uploadConfig.isRequired) continue;
      
      // Check if uploaded
      const isUploaded = interview.documentUploads?.some(
        upload => upload.documentId === docPage.uploadConfig.documentId
      );
      
      if (!isUploaded) {
        errors.push({
          type: 'REQUIRED_DOCUMENT_MISSING',
          message: `Required document not uploaded: ${docPage.uploadConfig.documentName}`,
          pageId: docPage.pageId,
          pageName: docPage.pageName
        });
      }
    }
    
    return errors;
  }
}
```

## 🎛️ **Upload API Endpoints**

### **Document Upload Controller**
```typescript
// Add to InterviewsV2Controller
export class InterviewsV2Controller {
  
  @Post(':interviewId/pages/:pageId/upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocumentPage(
    @Param('interviewId') interviewId: string,
    @Param('pageId') pageId: string,
    @UploadedFile() file: Express.Multer.File
  ) {
    // Get page definition
    const pageDefinition = await this.getPageDefinition(interviewId, pageId);
    
    if (pageDefinition.pageType !== 'document_upload') {
      throw new BadRequestException('Page is not a document upload page');
    }
    
    // Validate file
    this.validateUploadFile(file, pageDefinition.uploadConfig);
    
    // Process upload
    await this.documentUploadService.processDocumentUpload(
      interviewId,
      pageDefinition as DocumentPageTemplate,
      file
    );
    
    return { 
      message: 'Document uploaded successfully',
      documentName: pageDefinition.uploadConfig.documentName 
    };
  }
  
  @Get(':interviewId/documents/status')
  async getDocumentUploadStatus(@Param('interviewId') interviewId: string) {
    const interview = await this.getInterview(interviewId);
    const template = await this.getInterviewTemplate(interview.templateId);
    const documentPages = template.pages.filter(p => p.pageType === 'document_upload');
    
    return {
      totalDocumentPages: documentPages.length,
      requiredDocumentPages: documentPages.filter(p => p.uploadConfig.isRequired).length,
      uploadedDocuments: interview.documentUploads?.length || 0,
      uploads: interview.documentUploads || []
    };
  }
}
```

## 📊 **Implementation Plan**

### **Phase 1: Context-Based Template Extension (2 weeks)**
- Add document page templates to interview template system
- Extend composer service with context evaluation
- Create document page template definitions for common scenarios

### **Phase 2: Upload Processing Service (1.5 weeks)**
- Build generic document upload processor
- Integrate with existing DocuSign envelope workflow (V1 pattern)
- Add email processing option

### **Phase 3: Navigation & API Integration (1 week)**
- Extend navigation service for document pages
- Add upload endpoints to V2 controller
- File validation and error handling

### **Phase 4: Validation & Testing (1.5 weeks)**
- Integrate with completion validation service
- Testing with different context scenarios
- Edge case handling and documentation

**Total Timeline: 6 weeks**

## 🎯 **Context-Based Examples**

### **Example 1: Retirement Account Documents**
```typescript
// Context available during composition:
const context = {
  client: { tier: 'standard' },
  accounts: [
    { accountType: 'ira', ownership: 'individual' },
    { accountType: 'brokerage', ownership: 'joint' }
  ],
  organization: { features: ['docusign'] }
};

// Results in retirement document page being included
// Because accounts.hasRetirement evaluates to true
```

### **Example 2: Organization-Specific Documents**
```typescript
// Organization requires compliance documents
const context = {
  organization: { 
    features: ['compliance_docs', 'docusign'] 
  }
};

// Results in compliance document page being included
// Processed via email to compliance team
```

## ✅ **Key Benefits of Revised Approach**

1. **🎯 Works with V2 Architecture** - Uses context data, not stored answers
2. **⚡ Template Composition Integration** - Documents included during composition
3. **🔄 Leverages Existing Systems** - DocuSign workflow, navigation service
4. **📊 Context-Rich Conditions** - Account types, client properties, org features
5. **🚀 Simple Implementation** - Extends existing patterns rather than new architecture
6. **📱 Synchronous Processing** - Familiar V1-style envelope creation

## 🚨 **Key Limitations Addressed**

- **No Answer Storage**: Uses rich context data instead of answer-based conditions
- **Template-Time Decisions**: Document pages included during composition, not runtime
- **Context-Based Logic**: Leverages available client/account/organization data
- **Stateless Philosophy**: Maintains V2's approach while adding document functionality

This revised approach provides **simple, practical document management** that works within V2's architectural constraints while delivering the conditional logic needed for different client scenarios.