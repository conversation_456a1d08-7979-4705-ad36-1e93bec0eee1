# Testing Context Variables in Template Creation

This guide demonstrates how to create templates using the new client and account context variables system.

## Prerequisites

1. **Authentication**:
```bash
./scripts/api-testing/api-base.sh auth
```

2. **Get Organization ID**:
```bash
source scripts/api-testing/api-base.sh
echo $(get_org_id)
```

## 1. Test Variable Discovery Endpoints

First, let's verify the new variable endpoints are working:

### Client Context Variables
```bash
source scripts/api-testing/api-base.sh && api_request "GET" "/interview-templates/available-variables/client-context"
```

**Expected Response**: Should show client variables like:
```json
{
  "client.status.isExistingClient": {
    "description": "Whether client already exists in CRM system",
    "type": "boolean",
    "example": true
  },
  "client.features.docusignSelected": {
    "description": "Whether client will use DocuSign for document signing",
    "type": "boolean",
    "example": true
  }
}
```

### Multi-Account Variables (for Client Templates)
```bash
source scripts/api-testing/api-base.sh && api_request "GET" "/interview-templates/available-variables/multi-account-context"
```

**Expected Response**: Should show multi-account variables like:
```json
{
  "accounts.hasRetirement": {
    "description": "Whether client has any retirement accounts (IRA, 401k, Roth)",
    "type": "boolean", 
    "example": true
  },
  "accounts.count": {
    "description": "Total number of client accounts",
    "type": "number",
    "example": 3
  }
}
```

### Current Account Variables (for Account Templates)
```bash
source scripts/api-testing/api-base.sh && api_request "GET" "/interview-templates/available-variables/current-account-context"
```

**Expected Response**: Should show current account variables like:
```json
{
  "current_account.ownership": {
    "description": "Ownership type of current account (individual, joint, trust, etc.)",
    "type": "string",
    "example": "individual"
  },
  "current_account.advisoryRate": {
    "description": "Advisory fee rate for current account",
    "type": "number",
    "example": 1.25
  }
}
```

## 2. Create Client Template with Context Variables

### Test 1: Valid Client Template with Client and Multi-Account Variables

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates" '{
  "templateName": "Smart Client Onboarding",
  "templateType": "client_onboarding",
  "description": "Client template using context variables for smart conditional logic",
  "pages": [
    {
      "pageName": "welcome",
      "pageTitle": "Welcome",
      "description": "Welcome page with client context awareness",
      "fields": [
        {
          "fieldName": "welcomeMessage",
          "fieldType": "text",
          "label": "Welcome Message",
          "required": false
        }
      ],
      "flow": {
        "rules": [
          {
            "ruleId": "skip-for-existing-clients",
            "ruleName": "Skip detailed onboarding for existing clients",
            "priority": 1,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "client.status.isExistingClient",
                  "operator": "equals",
                  "value": true
                }
              ]
            },
            "goToPageName": "account_summary",
            "isActive": true
          },
          {
            "ruleId": "skip-employment-if-no-retirement",
            "ruleName": "Skip employment questions if no retirement accounts",
            "priority": 2,
            "when": {
              "logic": "AND", 
              "conditions": [
                {
                  "field": "accounts.hasRetirement",
                  "operator": "equals",
                  "value": false
                }
              ]
            },
            "goToPageName": "contact_info",
            "isActive": true
          }
        ],
        "defaultNext": {
          "pageName": "personal_info"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "personal_info", 
      "pageTitle": "Personal Information",
      "description": "Personal information collection",
      "fields": [
        {
          "fieldName": "firstName",
          "fieldType": "text",
          "label": "First Name",
          "required": true
        }
      ],
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "account_summary"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "contact_info",
      "pageTitle": "Contact Information", 
      "description": "Contact details",
      "fields": [
        {
          "fieldName": "email",
          "fieldType": "email",
          "label": "Email",
          "required": true
        }
      ],
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "account_summary"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "account_summary",
      "pageTitle": "Account Summary",
      "description": "Final summary page",
      "fields": [
        {
          "fieldName": "summary",
          "fieldType": "text",
          "label": "Summary",
          "required": false
        }
      ],
      "flow": {
        "rules": [],
        "defaultNext": null,
        "isTerminal": true,
        "allowBack": true
      }
    }
  ]
}'
```

**Expected Result**: ✅ Template should be created successfully with client and accounts context variables.

### Test 2: Invalid Client Template (Should Fail Validation)

This should fail because we're trying to use `current_account` variables in a client template:

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates" '{
  "templateName": "Invalid Client Template",
  "templateType": "client_onboarding",
  "description": "This should fail validation",
  "pages": [
    {
      "pageName": "welcome",
      "pageTitle": "Welcome",
      "fields": [
        {
          "fieldName": "message",
          "fieldType": "text",
          "label": "Message",
          "required": false
        }
      ],
      "flow": {
        "rules": [
          {
            "ruleId": "invalid-rule",
            "ruleName": "Invalid rule using current account variable",
            "priority": 1,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "current_account.ownership",
                  "operator": "equals",
                  "value": "individual"
                }
              ]
            },
            "goToPageName": "next_page",
            "isActive": true
          }
        ],
        "defaultNext": {
          "pageName": "next_page"
        },
        "isTerminal": false
      }
    }
  ]
}'
```

**Expected Result**: ❌ Should return validation error:
```json
{
  "statusCode": 400,
  "message": "Variable 'current_account.ownership' can only be used in account-specific templates. Found in rule 'Invalid rule using current account variable' for template type 'client_onboarding'. Use multi-account variables like 'accounts.hasJoint' instead."
}
```

## 3. Create Account Template with Current Account Variables

### Test 3: Valid Account Template

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates" '{
  "templateName": "Smart IRA Setup",
  "templateType": "account", 
  "accountType": "ira",
  "description": "IRA account template using current account variables",
  "pages": [
    {
      "pageName": "account_setup",
      "pageTitle": "Account Setup",
      "description": "IRA account setup with smart conditional logic",
      "fields": [
        {
          "fieldName": "accountName",
          "fieldType": "text",
          "label": "Account Name",
          "required": true
        }
      ],
      "flow": {
        "rules": [
          {
            "ruleId": "skip-joint-setup-individual",
            "ruleName": "Skip joint setup for individual accounts",
            "priority": 1,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "current_account.ownership",
                  "operator": "equals", 
                  "value": "individual"
                }
              ]
            },
            "goToPageName": "beneficiaries",
            "isActive": true
          }
        ],
        "defaultNext": {
          "pageName": "joint_setup"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "joint_setup",
      "pageTitle": "Joint Account Setup",
      "description": "Joint account configuration", 
      "fields": [
        {
          "fieldName": "jointOwner",
          "fieldType": "text",
          "label": "Joint Owner",
          "required": true
        }
      ],
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "beneficiaries"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "beneficiaries",
      "pageTitle": "Beneficiaries",
      "description": "Beneficiary information",
      "fields": [
        {
          "fieldName": "primaryBeneficiary",
          "fieldType": "text",
          "label": "Primary Beneficiary",
          "required": true
        }
      ],
      "flow": {
        "rules": [],
        "defaultNext": null,
        "isTerminal": true,
        "allowBack": true
      }
    }
  ]
}'
```

**Expected Result**: ✅ Account template should be created successfully with current_account variables.

### Test 4: Account Template with Multiple Variable Types

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates" '{
  "templateName": "Premium Brokerage Setup",
  "templateType": "account",
  "accountType": "brokerage", 
  "description": "Brokerage template using client, account, and current account variables",
  "pages": [
    {
      "pageName": "account_preferences",
      "pageTitle": "Account Preferences",
      "description": "Configure account based on client profile and account details",
      "fields": [
        {
          "fieldName": "tradingLevel",
          "fieldType": "select",
          "label": "Trading Level",
          "required": true
        }
      ],
      "flow": {
        "rules": [
          {
            "ruleId": "premium-features-existing-client",
            "ruleName": "Enable premium features for existing clients",
            "priority": 1,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "client.status.isExistingClient",
                  "operator": "equals",
                  "value": true
                },
                {
                  "field": "current_account.advisoryRate", 
                  "operator": "greater_than",
                  "value": 1.0
                }
              ]
            },
            "goToPageName": "premium_setup",
            "isActive": true
          },
          {
            "ruleId": "skip-complex-for-joint",
            "ruleName": "Skip complex setup for joint accounts",
            "priority": 2,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "current_account.ownership",
                  "operator": "equals",
                  "value": "joint"
                }
              ]
            },
            "goToPageName": "basic_setup",
            "isActive": true
          }
        ],
        "defaultNext": {
          "pageName": "standard_setup"
        },
        "isTerminal": false,
        "allowBack": true
      }
    },
    {
      "pageName": "premium_setup",
      "pageTitle": "Premium Setup",
      "fields": [{"fieldName": "premiumFeatures", "fieldType": "text", "label": "Premium Features", "required": false}],
      "flow": {"rules": [], "defaultNext": null, "isTerminal": true, "allowBack": true}
    },
    {
      "pageName": "basic_setup", 
      "pageTitle": "Basic Setup",
      "fields": [{"fieldName": "basicFeatures", "fieldType": "text", "label": "Basic Features", "required": false}],
      "flow": {"rules": [], "defaultNext": null, "isTerminal": true, "allowBack": true}
    },
    {
      "pageName": "standard_setup",
      "pageTitle": "Standard Setup", 
      "fields": [{"fieldName": "standardFeatures", "fieldType": "text", "label": "Standard Features", "required": false}],
      "flow": {"rules": [], "defaultNext": null, "isTerminal": true, "allowBack": true}
    }
  ]
}'
```

**Expected Result**: ✅ Should create successfully, demonstrating complex conditional logic using multiple variable types.

## 4. Test Template Updates with Context Variables

### Test 5: Update Template with New Variables

First get the template ID from a previous creation, then update it:

```bash
# Get template ID from previous creation response, then:
TEMPLATE_ID="REPLACE_WITH_ACTUAL_ID"

source scripts/api-testing/api-base.sh && api_request "PUT" "/v2/organisations/$(get_org_id)/interview-templates/$TEMPLATE_ID" '{
  "templateName": "Updated Smart Client Onboarding",
  "pages": [
    {
      "pageName": "welcome",
      "pageTitle": "Updated Welcome",
      "description": "Updated with new conditional logic",
      "fields": [
        {
          "fieldName": "welcomeMessage",
          "fieldType": "text",
          "label": "Welcome Message",
          "required": false
        }
      ],
      "flow": {
        "rules": [
          {
            "ruleId": "docusign-flow",
            "ruleName": "Direct to DocuSign if selected",
            "priority": 1,
            "when": {
              "logic": "AND",
              "conditions": [
                {
                  "field": "client.features.docusignSelected",
                  "operator": "equals",
                  "value": true
                }
              ]
            },
            "goToPageName": "docusign_setup",
            "isActive": true
          }
        ],
        "defaultNext": {
          "pageName": "manual_setup"
        },
        "isTerminal": false
      }
    },
    {
      "pageName": "docusign_setup",
      "pageTitle": "DocuSign Setup",
      "fields": [{"fieldName": "docusignConfig", "fieldType": "text", "label": "DocuSign Config", "required": false}],
      "flow": {"rules": [], "defaultNext": null, "isTerminal": true}
    },
    {
      "pageName": "manual_setup",
      "pageTitle": "Manual Setup", 
      "fields": [{"fieldName": "manualConfig", "fieldType": "text", "label": "Manual Config", "required": false}],
      "flow": {"rules": [], "defaultNext": null, "isTerminal": true}
    }
  ]
}'
```

## 5. Verify Templates Work in Interview Flow

After creating templates, test them in actual interview flow to verify context variables work:

### Test 6: Create Client with Context Data

Create a client that will trigger the conditional logic:

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/clients" '{
    "id": null,
    "readyToSend": true,
    "fixedRate": true,
    "customTemplates": ["TEMPLATE_ID_FROM_ABOVE"],
    "featuresSelected": true,
    "docusignSelected": true,
    "doClientProfiling": true,
    "addAccountsSelected": true,
    "primaryContact": {
        "firstName": "ContextTest",
        "lastName": "Client",
        "email": "<EMAIL>",
        "mobile": "+***********",
        "skipContactInterview": false,
        "crmClientId": "existing-crm-123",
        "accounts": [
            {
                "type": "ira",
                "label": "Test IRA",
                "ownership": "individual",
                "masterAccountNumber": "1111-1111",
                "advisoryRate": "1.50",
                "features": []
            },
            {
                "type": "brokerage",
                "label": "Test Brokerage", 
                "ownership": "joint",
                "masterAccountNumber": "2222-2222",
                "advisoryRate": "0.75",
                "features": []
            }
        ]
    },
    "primaryAdvisor": {
        "id": "6749e59e29b4e1c735982a1b"
    },
    "primaryCSA": {
        "id": "6749e59e29b4e1c735982a1b"
    },
    "secondaryAdvisor": [],
    "secondaryCSA": [],
    "sendNow": true,
    "notificationMethods": ["email"],
    "sendAdv2b": true,
    "interviewTemplateId": "TEMPLATE_ID_FROM_ABOVE"
}'
```

This client has:
- `crmClientId` (so `client.status.isExistingClient` = true)
- `docusignSelected: true` (so `client.features.docusignSelected` = true)  
- Both IRA and brokerage accounts (so `accounts.hasRetirement` = true)
- Different ownership types and advisory rates for account-level variables

### Test 7: Navigate Interview and Verify Conditional Logic

After creating the client and interview:

```bash
# Get interview ID
source scripts/api-testing/api-base.sh && api_request "GET" "/v2/interviews/client/CLIENT_ID"

# Get navigation state - should show conditional logic working
source scripts/api-testing/api-base.sh && api_request "GET" "/v2/interviews/INTERVIEW_ID/navigation/state"

# Submit first page to see if conditional logic triggers
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/INTERVIEW_ID/pages/submit" '{
  "pageName": "welcome",
  "answers": {
    "welcomeMessage": "Testing context variables"
  }
}'
```

**Expected Result**: The response should show navigation going to pages determined by the context variables (e.g., if existing client, should skip certain pages).

## 6. Testing Checklist

- [ ] ✅ Variable discovery endpoints return correct variables
- [ ] ✅ Client template creation succeeds with client/multi-account variables
- [ ] ✅ Client template creation fails with current_account variables (validation working)
- [ ] ✅ Account template creation succeeds with all variable types
- [ ] ✅ Template updates work with new context variables
- [ ] ✅ Interview flow respects conditional logic based on client context
- [ ] ✅ Account-specific interviews use current_account variables correctly
- [ ] ✅ Error messages provide helpful guidance on correct variable usage

## 7. Expected Log Output

When testing conditional logic, check Docker logs for context evaluation:

```bash
docker logs onbord-backend --tail 50 | grep -E "(FlowEvaluationService|ClientContextService)"
```

**Look for**:
- ✅ `[FlowEvaluationService] Rule matched: Skip detailed onboarding for existing clients`
- ✅ `[ClientContextService] Building interview context for client`
- ✅ Context variables being resolved correctly in conditional evaluation

This comprehensive test suite validates that the context variables system works end-to-end from template creation through interview execution.