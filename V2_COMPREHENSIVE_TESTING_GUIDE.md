# V2 Comprehensive Testing Guide

This guide provides bulletproof API calls for testing the V2 interview system, including template creation, client creation, interview navigation, and validation testing.

## Prerequisites

### 1. Authentication Framework
Use the robust API testing framework with automatic token management:

```bash
# Initial authentication
./scripts/api-testing/api-base.sh auth

# Check authentication status
./scripts/api-testing/api-base.sh status

# Cleanup auth files if needed
./scripts/api-testing/api-base.sh cleanup
```

**⚠️ IMPORTANT:** The `api_request` wrapper from the framework has issues with V2 endpoints. Use direct `curl` commands after authentication.

### 2. Bulletproof API Request Method
After authentication, use direct curl with the stored token:

```bash
# Authenticate first
./scripts/api-testing/api-base.sh auth

# Get token and organization info
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')

# Make API calls with curl directly
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" "http://localhost:3000/endpoint"
```

### 3. Get Organization Information
Use direct curl to get organization context:

```bash
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
echo "Using Organization ID: $ORG_ID"
```

### 4. Environment Verification
```bash
# Verify backend is running
curl -s http://localhost:3000/health || echo "❌ Backend not running"

# Check Docker services
docker ps | grep onbord-backend || echo "❌ Docker services not running"
```

## Part 1: Template Creation & Management

### 1.1 Check Existing V2 Templates

**Recommended:** Use existing templates first before creating new ones:

```bash
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates"
```

**Look for published templates and note their IDs for client creation.**

### 1.2 Create V2 Template with Proper ComponentTypes (Optional)

**Critical:** V2 templates MUST have proper componentTypes for CRM mapping to work.

```bash
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates" -d '{
  "templateName": "V2 Testing Template",
  "templateDescription": "Complete V2 template with proper componentTypes for testing",
  "version": "1.0.0",
  "defaultOrder": ["name", "address", "phone", "dob", "ssn", "employment", "company", "us_citizen", "conflicts-of-interest", "primary_beneficiaries", "contingent_beneficiaries"],
  "pages": [
    {
      "pageId": "name-page",
      "pageName": "name",
      "pageType": "name",
      "pageTitle": "Personal Information",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "address"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "address-page", 
      "pageName": "address",
      "pageType": "address",
      "pageTitle": "Address Information",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "phone"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "phone-page",
      "pageName": "phone", 
      "pageType": "phone",
      "pageTitle": "Phone Information",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "dob"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "dob-page",
      "pageName": "dob",
      "pageType": "dob", 
      "pageTitle": "Date of Birth",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "ssn"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "ssn-page",
      "pageName": "ssn",
      "pageType": "ssn",
      "pageTitle": "Social Security Number",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "employment"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "employment-page",
      "pageName": "employment",
      "pageType": "employment",
      "pageTitle": "Employment Information",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [
          {
            "ruleName": "Skip to Company Official if Unemployed/Retired",
            "priority": 1,
            "when": {
              "logic": "OR",
              "conditions": [
                {
                  "field": "status",
                  "operator": "equals",
                  "value": "unemployed"
                },
                {
                  "field": "status",
                  "operator": "equals", 
                  "value": "retired"
                }
              ]
            },
            "goToPageName": "us_citizen"
          }
        ],
        "defaultNext": {
          "pageName": "company"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "company-page",
      "pageName": "company",
      "pageType": "company",
      "pageTitle": "Company Information",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "us_citizen"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "us-citizen-page",
      "pageName": "us_citizen",
      "pageType": "us_citizen",
      "pageTitle": "US Citizenship",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "conflicts-of-interest"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "conflicts-page",
      "pageName": "conflicts-of-interest",
      "pageType": "conflicts-of-interest",
      "pageTitle": "Conflicts of Interest",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "primary_beneficiaries"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "primary-beneficiaries-page",
      "pageName": "primary_beneficiaries",
      "pageType": "primary_beneficiaries",
      "pageTitle": "Primary Beneficiaries",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": {
          "pageName": "contingent_beneficiaries"
        },
        "allowBack": true
      }
    },
    {
      "pageId": "contingent-beneficiaries-page",
      "pageName": "contingent_beneficiaries",
      "pageType": "contingent_beneficiaries", 
      "pageTitle": "Contingent Beneficiaries",
      "isActive": true,
      "isRequired": true,
      "flow": {
        "rules": [],
        "defaultNext": null,
        "allowBack": true,
        "isTerminal": true
      }
    }
  ]
}'
```

### 1.3 Publish Template (If Created)

Extract the template ID from the creation response and publish:

```bash
# Replace TEMPLATE_ID with the actual ID from creation response
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
TEMPLATE_ID="TEMPLATE_ID_FROM_RESPONSE"
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates/$TEMPLATE_ID/publish" -d '{
  "publishNotes": "Initial V2 testing template with proper componentTypes",
  "makeDefault": true
}'
```

### 1.4 Verify Template Availability

```bash
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates"
```

**Expected Response:**
- ✅ Template with `"status": "published"`
- ✅ All pages have proper `pageType` matching componentTypes
- ✅ Flow rules configured correctly
- ✅ Template ID for client creation

## Part 2: Client Creation & Interview Testing

### 2.1 Create V2 Client with Interview

**Critical Parameters:**
- `sendNow: true` - Required for V2 interview creation
- `customTemplates` - Array with published template ID
- `interviewTemplateId` - Same template ID
- `advisoryRate` - Must be a number, not string

```bash
# Get credentials and IDs
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
TEMPLATE_ID="YOUR_TEMPLATE_ID_HERE"  # Get from templates list above
ADVISOR_ID="YOUR_ADVISOR_ID_HERE"    # Get from organization response

curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/organisations/$ORG_ID/clients" -d '{
    "id": null,
    "readyToSend": true,
    "fixedRate": true,
    "customTemplates": ["'$TEMPLATE_ID'"],
    "featuresSelected": true,
    "docusignSelected": true,
    "doClientProfiling": true,
    "addAccountsSelected": true,
    "primaryContact": {
        "firstName": "TestV2",
        "lastName": "Client",
        "email": "<EMAIL>",
        "mobile": "+***********",
        "skipContactInterview": false,
        "accounts": [
            {
                "type": "ira",
                "label": "Test IRA Account",
                "ownership": "individual",
                "masterAccountNumber": "1111-1111",
                "advisoryRate": 1.00,
                "features": []
            }
        ]
    },
    "primaryAdvisor": {
        "id": "'$ADVISOR_ID'"
    },
    "primaryCSA": {
        "id": "'$ADVISOR_ID'"
    },
    "secondaryAdvisor": [],
    "secondaryCSA": [],
    "sendNow": true,
    "notificationMethods": [
        "email"
    ],
    "sendAdv2b": true,
    "interviewTemplateId": "'$TEMPLATE_ID'"
}'
```

### 2.2 Monitor Queue Processing

```bash
# Wait for queue processing
echo "⏳ Waiting for queue processing..."
sleep 10

# Check Docker logs for job completion
docker logs onbord-backend --tail 30 | grep -E "(Job.*completed|Job.*failed|Interview.*created)"
```

### 2.3 Verify Interview Creation

```bash
# Replace CLIENT_ID with the ID from client creation response
TOKEN=$(cat scripts/api-testing/token.txt)
CLIENT_ID="YOUR_CLIENT_ID_HERE"
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/interviews/client/$CLIENT_ID"
```

**Expected Response:**
- ✅ Array with one interview object
- ✅ `"apiVersion": "v2"`
- ✅ `"sealed": false`
- ✅ Template pages populated
- ✅ Interview ID for navigation testing

## Part 3: Navigation Testing

### 3.1 Get Initial Navigation State

```bash
TOKEN=$(cat scripts/api-testing/token.txt)
INTERVIEW_ID="YOUR_INTERVIEW_ID_HERE"
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/interviews/$INTERVIEW_ID/navigation/state"
```

### 3.2 Complete Interview Flow

**Step 1: Name Page**
```bash
TOKEN=$(cat scripts/api-testing/token.txt)
INTERVIEW_ID="YOUR_INTERVIEW_ID_HERE"
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "name",
  "answers": {
    "firstName": "John",
    "lastName": "Doe",
    "middleName": "M",
    "suffix": "Jr"
  }
}'
```

**Step 2: Address Page**
```bash
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "address",
  "answers": {
    "legalAddress": {
      "line1": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zip": "12345"
    }
  }
}'
```

**Step 3: Phone Page**
```bash
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "phone",
  "answers": {
    "alternatePhones": []
  }
}'
```

**Step 4: Date of Birth**
```bash
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "dob",
  "answers": {
    "dateOfBirth": "1990-01-01"
  }
}'
```

**Step 5: SSN**
```bash
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "ssn",
  "answers": {
    "ssn": "***********"
  }
}'
```

**Step 6: Employment (Test Conditional Logic)**
```bash
# Test skip logic - should skip company page
curl -H "Authorization: Bearer $TOKEN" -H "Content-Type: application/json" -X POST \
  "http://localhost:3000/v2/interviews/$INTERVIEW_ID/pages/submit" -d '{
  "pageName": "employment",
  "answers": {
    "status": "retired"
  }
}'
```

**Expected:** `"nextPageName": "us_citizen"` (skipped company page)

**Step 7: US Citizen**
```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "us_citizen",
  "answers": {
    "citizen": true,
    "resident": true
  }
}'
```

**Step 8: Conflicts of Interest**
```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "conflicts-of-interest",
  "answers": {
    "hasConflicts": false,
    "beneficialInterest": false
  }
}'
```

**Step 9: Primary Beneficiaries**
```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "primary_beneficiaries",
  "answers": {
    "beneficiaries": [
      {
        "name": "Jane Doe",
        "relationship": "spouse",
        "percentage": 100
      }
    ]
  }
}'
```

**Step 10: Complete Interview**
```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "contingent_beneficiaries",
  "answers": {
    "beneficiaries": []
  }
}'
```

**Expected:** `"isComplete": true` and DocuSign envelope creation

## Part 4: Validation Testing

### 4.1 Test Page Order Validation

```bash
# Should fail - cannot skip to future page
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "conflicts-of-interest",
  "answers": {
    "hasConflicts": false
  }
}'
```

**Expected Error:** `400 Bad Request` - "Cannot submit page"

### 4.2 Test Data Type Validation

```bash
# Should fail - wrong data type
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "name",
  "answers": {
    "firstName": 123,
    "lastName": "Doe"
  }
}'
```

**Expected Error:** `400 Bad Request` - "Validation failed"

### 4.3 Test Required Field Validation

```bash
# Should fail - missing required field
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{
  "pageName": "name",
  "answers": {
    "lastName": "Doe"
  }
}'
```

**Expected Error:** `400 Bad Request` - "firstName must be a string"

### 4.4 Test Back Navigation

```bash
# After submitting several pages
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/interviews/$INTERVIEW_ID/navigation/back"
```

**Expected:** Previous page navigation with audit trail

## Part 5: CRM Sync & Completion Testing

### 5.1 Monitor CRM Sync Logs

```bash
# Watch for CRM sync events
docker logs onbord-backend --tail 50 | grep -E "(CRM.*sync|FlowProducer|SYNC_PAGE_V2)"
```

**Look for:**
- ✅ `FlowProducer` parent-child job creation
- ✅ `SYNC_PAGE_V2` jobs in CRM queue
- ✅ `UPDATE_PAGE_STATUS_V2` completion
- ✅ No sync errors

### 5.2 Check Interview Completion

```bash
# Monitor completion queue
docker logs onbord-backend --tail 30 | grep -E "(COMPLETE_INTERVIEW_V2|DocuSign|envelope)"
```

**Look for:**
- ✅ `COMPLETE_INTERVIEW_V2` job execution
- ✅ DocuSign envelope creation
- ✅ `V2PdfDataProvider` logs
- ✅ Interview marked as complete

### 5.3 Verify Final State

```bash
source scripts/api-testing/api-base.sh && api_request "GET" "/v2/interviews/$INTERVIEW_ID/navigation/state"
```

**Expected:**
- ✅ `"isComplete": true`
- ✅ All pages in `visitedPages`
- ✅ No `nextPageName`

## Part 6: Troubleshooting & Common Issues

### 6.1 Authentication Issues

**⚠️ Known Issue:** The `api_request` wrapper has compatibility issues with V2 endpoints, returning 401 errors even with valid tokens.

**Solution:** Use direct `curl` commands as shown throughout this guide.

```bash
# Re-authenticate if tokens expire
./scripts/api-testing/api-base.sh cleanup
./scripts/api-testing/api-base.sh auth

# Verify authentication with V1 endpoint
TOKEN=$(cat scripts/api-testing/token.txt)
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations"

# If 401 errors persist with V2 endpoints, the token is expired
```

### 6.2 Template Issues

```bash
# List available templates
TOKEN=$(cat scripts/api-testing/token.txt)
ORG_ID=$(curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3000/organisations" | jq -r '.result[0]._id')
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates"

# Check template details
TEMPLATE_ID="YOUR_TEMPLATE_ID"
curl -H "Authorization: Bearer $TOKEN" "http://localhost:3000/v2/organisations/$ORG_ID/interview-templates/$TEMPLATE_ID"
```

### 6.3 Queue Monitoring

```bash
# Check queue status
curl -s "http://localhost:3000/queues/api/queues?activeQueue=interview_v2_queue"

# Check failed jobs
curl -s "http://localhost:3000/queues/api/queues?status=failed"
```

### 6.4 Common Data Validation Errors

**Error:** `advisoryRate must be a number`
**Solution:** Use numeric value, not string: `"advisoryRate": 1.00` not `"advisoryRate": "1.00"`

**Error:** `401 Unauthorized` on V2 endpoints
**Solution:** Use direct curl instead of `api_request` wrapper

**Error:** Template not found
**Solution:** Check template list and use correct published template ID

### 6.4 Log Analysis

```bash
# Comprehensive log check
docker logs onbord-backend --tail 100 | grep -E "(ERROR|WARN|V2.*Job|Interview.*V2)"

# Specific V2 interview logs
docker logs onbord-backend --tail 50 | grep "InterviewV2"

# CRM sync logs
docker logs onbord-backend --tail 50 | grep -E "(CRM|sync|FlowProducer)"
```

## Part 7: Complete Testing Checklist

### Pre-Testing Setup
- [ ] ✅ Backend services running (`docker ps`)
- [ ] ✅ Authentication working (`./scripts/api-testing/api-base.sh status`)
- [ ] ✅ Organization ID available (`$(get_org_id)`)

### Template Management
- [ ] ✅ V2 template created with proper componentTypes
- [ ] ✅ Template published successfully
- [ ] ✅ Template appears in organization templates list

### Client & Interview Creation
- [ ] ✅ V2 client created with `sendNow: true`
- [ ] ✅ Queue processing completed (check logs)
- [ ] ✅ Interview auto-created with V2 schema
- [ ] ✅ Page instances composed correctly

### Navigation & Flow Testing
- [ ] ✅ Initial navigation state retrieval
- [ ] ✅ Sequential page submission working
- [ ] ✅ Conditional navigation (employment skip logic)
- [ ] ✅ Back navigation functionality
- [ ] ✅ Page order validation blocking skips

### Validation System Testing
- [ ] ✅ Data type validation (string/number/object)
- [ ] ✅ Required field validation
- [ ] ✅ DTO-based validation working per page type
- [ ] ✅ Meaningful error messages

### CRM Integration Testing
- [ ] ✅ CRM sync jobs queued for each page
- [ ] ✅ FlowProducer parent-child relationships
- [ ] ✅ Page sync status updates (SYNCED)
- [ ] ✅ No CRM sync failures in logs

### Completion & DocuSign Testing
- [ ] ✅ Interview completion triggered
- [ ] ✅ V2 PDF data provider execution
- [ ] ✅ DocuSign envelope creation
- [ ] ✅ Completion notifications sent

### Final Verification
- [ ] ✅ Interview marked as complete (`isComplete: true`)
- [ ] ✅ All audit trails logged
- [ ] ✅ No errors in application logs
- [ ] ✅ CRM data synchronized properly

## Part 8: Helper Scripts

### 8.1 Quick Template Creation Script

```bash
#!/bin/bash
# save as create-v2-template.sh
source scripts/api-testing/api-base.sh

echo "Creating V2 template..."
RESPONSE=$(api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates" '{...template payload...}')
TEMPLATE_ID=$(echo "$RESPONSE" | jq -r '._id')

echo "Publishing template $TEMPLATE_ID..."
api_request "POST" "/v2/organisations/$(get_org_id)/interview-templates/$TEMPLATE_ID/publish" '{
  "publishNotes": "Auto-created V2 testing template",
  "makeDefault": true
}'

echo "✅ Template created and published: $TEMPLATE_ID"
```

### 8.2 Complete Interview Flow Script

```bash
#!/bin/bash
# save as complete-v2-interview.sh
source scripts/api-testing/api-base.sh

INTERVIEW_ID="$1"
if [ -z "$INTERVIEW_ID" ]; then
    echo "Usage: $0 <interview_id>"
    exit 1
fi

echo "Completing interview $INTERVIEW_ID..."

# Submit all pages with valid data
api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{"pageName":"name","answers":{"firstName":"Test","lastName":"Client"}}'
api_request "POST" "/v2/interviews/$INTERVIEW_ID/pages/submit" '{"pageName":"address","answers":{"legalAddress":{"line1":"123 Main St","city":"Test","state":"CA","zip":"12345"}}}'
# ... continue for all pages

echo "✅ Interview completed"
```

## Part 9: Production Validation

For production readiness testing, use the comprehensive CRM validation approach:

### 9.1 Production Client Creation

```bash
source scripts/api-testing/api-base.sh && api_request "POST" "/v2/organisations/$(get_org_id)/clients" '{
    "primaryContact": {
        "firstName": "ProductionV2",
        "lastName": "ValidationClient",
        "email": "<EMAIL>",
        "accounts": [
            {
                "type": "ira",
                "label": "Production IRA",
                "ownership": "individual",
                "masterAccountNumber": "PROD-1111",
                "advisoryRate": "1.25"
            }
        ]
    },
    "sendNow": true,
    "interviewTemplateId": "'$TEMPLATE_ID'"
}'
```

### 9.2 Monitor Production Logs

```bash
# Watch for V2 PDF data provider comprehensive logging
docker logs onbord-backend --tail 100 | grep -E "(V2PdfDataProvider|CRM-ENRICHED|COMPLETE.*DATA)"
```

**Expected Production Logs:**
- ✅ `[V2PdfDataProvider] ========== COMPLETE CRM-ENRICHED DATA ==========`
- ✅ `[V2PdfDataProvider] PRIMARY CONTACT CRM-ENRICHED DATA:`
- ✅ `[V2PdfDataProvider] PRIMARY CONTACT ACCOUNTS (CRM-ENRICHED):`
- ✅ All contact, organization, and advisor data logged comprehensively

This consolidated guide provides bulletproof testing methodology for the V2 interview system with proper authentication, template creation, client testing, and production validation.