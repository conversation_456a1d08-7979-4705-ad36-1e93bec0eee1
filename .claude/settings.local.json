{"permissions": {"allow": ["<PERSON><PERSON>(cat:*)", "Bash(eza:*)", "<PERSON><PERSON>(uv run:*)", "mcp__aider-mcp-server__aider_ai_code", "mcp__aider-mcp-server__list_models", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(terraform state show:*)", "Bash(terraform plan:*)", "Bash(rg:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm install:*)", "Bash(npm run test:*)", "Bash(node:*)", "Bash(npm run lint)", "Bash(npm ls:*)", "Bash(npx eslint:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm test:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(diff:*)", "mcp__ide__getDiagnostics", "Bash(git checkout:*)", "Bash(git restore:*)", "Bash(git rm:*)", "WebFetch(domain:docs.nestjs.com)", "Bash(cp:*)", "Bash(/tmp/interview_templates_api.sh:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/api-testing/api-base.sh:*)", "Bash(/tmp/test_api_simple.sh:*)", "Bash(./scripts/api-testing/test-example.sh:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(authenticate)", "Bash(docker logs:*)", "mcp__mongo__find", "mcp__mongo__listCollections", "<PERSON><PERSON>(docker exec:*)", "mcp__sequential-thinking-tools__sequentialthinking_tools", "mcp__sequential-thinking__sequentialthinking", "Bash(docker compose logs:*)", "Bash(timeout 30 npm test -- src/super-http/super-http.service.spec.ts --runInBand --no-coverage)", "<PERSON><PERSON>(gtimeout:*)", "Bash(TOKEN=$(cat scripts/api-testing/token.txt):*)", "Bash(TOKEN=$(cat scripts/api-testing/token.txt):*)", "Bash(*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["aider-mcp-server"]}