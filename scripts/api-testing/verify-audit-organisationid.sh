#!/bin/bash

# Verify OrganisationId Integration in Audit System
# This script verifies that all audit calls include organisationId for proper multi-tenant isolation

set -e

# Load base functions for formatting
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/api-base.sh"

# Test results
CHECKS_PASSED=0
CHECKS_TOTAL=0

check_result() {
    local test_name="$1"
    local result="$2"
    
    ((CHECKS_TOTAL++))
    
    if [ "$result" = "PASS" ]; then
        ((CHECKS_PASSED++))
        success "✅ $test_name"
    else
        error "❌ $test_name"
    fi
}

log "=== Verifying OrganisationId Integration in Audit System ==="
log ""

# Test 1: Schema has organisationId field
log "Test 1: Schema includes organisationId field"
if grep -A 10 -B 5 "organisationId!" src/audits/schemas/interview-audit.schema.ts | grep -q "SchemaTypes.ObjectId\|ObjectId"; then
    check_result "Schema has organisationId field" "PASS"
else
    check_result "Schema has organisationId field" "FAIL"
fi

# Test 2: Schema has organisationId index
log "Test 2: Schema has organisationId indexes"
if grep -q "organisationId.*1.*-1\|organisationId.*index" src/audits/schemas/interview-audit.schema.ts; then
    check_result "Schema has organisationId indexes" "PASS"
else
    check_result "Schema has organisationId indexes" "FAIL"
fi

# Test 3: All audit interfaces include organisationId
log "Test 3: All audit interfaces include organisationId"
INTERFACES_COUNT=$(grep -c "export interface.*Audit" src/interviews/services/v2/audit/interview-audit.service.ts)
ORGANISATIONID_COUNT=$(grep -A 10 "export interface.*Audit" src/interviews/services/v2/audit/interview-audit.service.ts | grep -c "organisationId")

if [ "$ORGANISATIONID_COUNT" -ge "$INTERFACES_COUNT" ]; then
    check_result "All audit interfaces include organisationId" "PASS"
else
    check_result "All audit interfaces include organisationId ($ORGANISATIONID_COUNT/$INTERFACES_COUNT)" "FAIL"
fi

# Test 4: All audit method implementations include organisationId
log "Test 4: All audit method implementations include organisationId"
AUDIT_METHODS=$(grep -c "logInterview\|logPage\|logNavigation\|logData" src/interviews/services/v2/audit/interview-audit.service.ts)
AUDIT_ORG_USAGE=$(grep -A 15 "auditModel.create" src/interviews/services/v2/audit/interview-audit.service.ts | grep -c "organisationId")

if [ "$AUDIT_ORG_USAGE" -ge 8 ]; then  # Expecting at least 8 audit method implementations
    check_result "All audit implementations include organisationId" "PASS"
else
    check_result "All audit implementations include organisationId ($AUDIT_ORG_USAGE found)" "FAIL"
fi

# Test 5: Service calls include organisationId
log "Test 5: Service calls include organisationId"
CORE_SERVICE_ORG=$(grep -A 10 "auditService\.log" src/interviews/services/v2/core/interview-core.service.ts | grep -c "organisationId" || echo "0")
NAV_SERVICE_ORG=$(grep -A 10 "auditService\.log" src/interviews/services/v2/navigation/interview-navigation.service.ts | grep -c "organisationId" || echo "0")
V2_SERVICE_ORG=$(grep -A 10 "auditService\.log" src/interviews/services/v2/interview-v2.service.ts | grep -c "organisationId" || echo "0")

TOTAL_SERVICE_ORG=$((CORE_SERVICE_ORG + NAV_SERVICE_ORG + V2_SERVICE_ORG))

if [ "$TOTAL_SERVICE_ORG" -ge 6 ]; then  # Expecting multiple audit calls across services
    check_result "Service calls include organisationId ($TOTAL_SERVICE_ORG found)" "PASS"
else
    check_result "Service calls include organisationId ($TOTAL_SERVICE_ORG found)" "FAIL"
fi

# Test 6: Database query recommendations
log "Test 6: Multi-tenant query patterns"
if grep -q "organisationId.*1.*-1\|organisationId.*index" src/audits/schemas/interview-audit.schema.ts; then
    check_result "Multi-tenant indexes configured" "PASS"
else
    check_result "Multi-tenant indexes configured" "FAIL"
fi

log ""
log "=== Test Results ==="
log "Passed: $CHECKS_PASSED / $CHECKS_TOTAL"

PASS_RATE=$((CHECKS_PASSED * 100 / CHECKS_TOTAL))

if [ $PASS_RATE -eq 100 ]; then
    success "🎉 All organisationId integration checks passed!"
    log ""
    log "The audit system now properly supports multi-tenancy:"
    log "  ✓ Schema includes organisationId field with proper indexes"
    log "  ✓ All audit interfaces require organisationId"
    log "  ✓ All audit method implementations store organisationId"
    log "  ✓ Service calls include organisationId from interview context"
    log "  ✓ Database queries can be filtered by organisation"
    log ""
    log "Sample multi-tenant queries:"
    log "  # Get all audit entries for an organisation"
    log "  db.interviewaudits_v2.find({organisationId: ObjectId('ORG_ID')})"
    log ""
    log "  # Get audit entries for specific interview with org filter"
    log "  db.interviewaudits_v2.find({"
    log "    organisationId: ObjectId('ORG_ID'),"
    log "    interviewId: ObjectId('INTERVIEW_ID')"
    log "  })"
    log ""
    log "  # Get page completion events for an organisation"
    log "  db.interviewaudits_v2.find({"
    log "    organisationId: ObjectId('ORG_ID'),"
    log "    eventType: 'page_completed'"
    log "  })"
    
elif [ $PASS_RATE -ge 80 ]; then
    warn "Most organisationId integration checks passed ($PASS_RATE%)"
    log "Some minor issues detected but core multi-tenancy should work"
    
else
    error "OrganisationId integration validation failed ($PASS_RATE%)"
    log "Major issues detected that need to be addressed for proper multi-tenancy"
    exit 1
fi

log ""
log "Next steps:"
log "1. Test with real data using V2_INTERVIEW_TESTING_GUIDE.md"
log "2. Verify MongoDB entries include organisationId"
log "3. Test multi-tenant isolation in queries"